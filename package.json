{"name": "home", "version": "1.0.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "npx simple-git-hooks", "dev": "cross-env NODE_ENV=development ech-micro --port 9010", "build": "cross-env NODE_ENV=production tsc && ech-micro build && pnpm run version", "version": "node ./scripts/generate-version.js", "lint": "pnpm run lint:script && pnpm run lint:style", "lint:fix": "pnpm run lint:script --fix && pnpm run lint:style --fix", "lint:script": "eslint **/*.{js,jsx,ts,tsx}", "lint:style": "stylelint \"src/**/*.{less,css}\""}, "dependencies": {"@ant-design/icons": "~4.7.0", "@echronos/antd": "^4.24.17", "@echronos/core": "0.2.2", "@echronos/echOS-card-block": "1.0.1-test08", "@echronos/echos-icon": "^1.0.5", "@echronos/echos-ui": "2.0.0", "@echronos/icons": "^0.0.2", "@echronos/react": "^0.0.4", "@loadable/component": "^5.15.3", "@rc-component/portal": "^1.1.2", "@types/react-i18next": "^8.1.0", "ahooks": "~3.7.8", "antd": "^4.24.11", "big.js": "~6.2.1", "classnames": "~2.3.2", "copy-to-clipboard": "~3.3.3", "core-js": "^3.31.0", "dayjs": "~1.11.8", "echarts": "5.4.0", "i18next": "^24.0.5", "lodash": "~4.17.21", "react": "^17.0.2", "react-color": "^2.17.3", "react-dom": "^17.0.2", "react-helmet": "~6.1.0", "react-i18next": "^15.1.3", "react-image-crop": "^10.1.8", "react-infinite-scroll-component": "~6.1.0", "react-router-dom": "~6.3.0", "react-slick": "^0.29.0", "react-window": "~1.8.9", "slick-carousel": "^1.8.1", "styled-components": "6.0.0-rc.3", "uuid": "^9.0.1"}, "devDependencies": {"@echronos/ech-micro": "^0.1.4", "@echronos/eslint-config": "^0.0.1", "@echronos/swc-plugin-transform-imports": "^1.6.0", "@echronos/user": "^1.0.0", "@swc/core": "1.3.105", "@swc/jest": "^0.2.26", "@types/big.js": "^6.1.6", "@types/loadable__component": "^5.13.5", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/react": "^17.0.62", "@types/react-color": "^3.0.6", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^17.0.20", "@types/react-helmet": "^6.1.6", "@types/react-slick": "^0.23.10", "@types/react-window": "^1.8.5", "cross-env": "^7.0.3", "eslint": "~8.22.0", "http-proxy-middleware": "^2.0.6", "jest": "^29.5.0", "less": "^4.1.3", "lint-staged": "^12.3.7", "prettier": "^2.8.8", "simple-git-hooks": "^2.8.1", "stylelint": "^15.8.0", "typescript": "^5.1.3", "webpack-chain": "^6.5.1"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --concurrent false"}, "lint-staged": {"**/*.{ts,tsx,less,css,json,js,jsx}": ["prettier --write --cache --ignore-unknown"], "**/*.{js,jsx,ts,tsx}": ["eslint --cache --fix"], "**/*.{less,css}": ["stylelint --cache --fix"]}}