import { useEffect, useState, useCallback, useRef, memo } from 'react';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import Echarts from '@/blocks/public/components/echarts';
import useEcharts from '@/blocks/public/hooks/use-echarts';
import CardContext from '@/blocks/public/components/card-context';
import CardHeaderTitle from '@/blocks/public/components/card-header-title';
import CalendarYear, { CalendarYearInstance } from '@/blocks/public/components/calendar-year';
import { getFinanceHomePageTwo, FinanceHomeTwoTotal } from '@/blocks/public/apis';
import { formatPrice } from '@/blocks/public/utils/utils';
import { IEchBlockCard } from '@/blocks/interface';
import useElSize from '@/hooks/use-el-size';
import styles from './index.module.less';

export type FinanceCollectionPaymentProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};

interface ChartList {
  skuName: string;
  totalNum: Array<number>;
  startColor: string;
  endColor: string;
  color: string;
}
const dateYearNum = new Date().getFullYear();
function FinanceContrastChart({ appName, logo }: FinanceCollectionPaymentProps) {
  const { t } = useTranslation();
  const defaultAppName = t('desk_financeChart_defaultName');
  const echartsRef = useEcharts();
  const calendarYearRef = useRef<CalendarYearInstance>(null);
  const [isPermission, setIsPermission] = useState(true);
  const [currentYear, setCurrentYear] = useState<number>(dateYearNum || 0);
  const [isShow, setIsShow] = useState(false);
  // 获取当前卡片的DOM元素
  const editCardBlockRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(editCardBlockRef, 324);
  const [chartList, setChartList] = useState<ChartList[]>([
    {
      skuName: t('desk_financeChart_collection'),
      totalNum: [],
      startColor: 'rgba(14,118,254,0.20)',
      endColor: 'rgba(15,118,255,0.00)',
      color: '#457af7',
    },
    {
      skuName: t('desk_financeChart_payment'),
      totalNum: [],
      startColor: 'rgba(45,213,145,0.20)',
      endColor: 'rgba(51,214,148,0.00)',
      color: '#06c88f',
    },
  ]);
  const { data, loading, run } = useRequest(
    () =>
      getFinanceHomePageTwo({ year: `${currentYear}` })
        .then((res) => res.list)
        .catch((err) => {
          if (err.code === 5) {
            setIsPermission(false);
          }
        }),
    {
      manual: true,
    }
  );

  const splitStr = useCallback((str: string) => {
    const arr = str.split('-');
    return Number(arr[arr.length - 1]);
  }, []);

  // 传入数组，根据数组对象排序，根据对象中的某个属性
  const sortArr = useCallback(
    (arr: Array<FinanceHomeTwoTotal>, key: 'monthIndex') =>
      arr.sort((a, b) => splitStr(a[key]) - splitStr(b[key])),
    [splitStr]
  );

  useEffect(() => {
    if (data) {
      const totalNumOne = sortArr(
        data?.filter((item) => item.type === 2),
        'monthIndex'
      ).map((item) => item.totalAmount);

      const totalNumTwo = sortArr(
        data?.filter((item) => item.type === 1),
        'monthIndex'
      ).map((item) => item.totalAmount);

      setChartList([
        {
          skuName: t('desk_financeChart_collection'),
          totalNum: totalNumTwo.length ? totalNumTwo : [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          startColor: 'rgba(14,118,254,0.20)',
          endColor: 'rgba(15,118,255,0.00)',
          color: '#457af7',
        },
        {
          skuName: t('desk_financeChart_payment'),
          totalNum: totalNumOne.length ? totalNumOne : [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          startColor: 'rgba(45,213,145,0.20)',
          endColor: 'rgba(51,214,148,0.00)',
          color: '#06c88f',
        },
      ]);
    }
  }, [data, sortArr]); // eslint-disable-line

  useEffect(() => {
    let resize: ResizeObserver;
    if (echartsRef.current && editCardBlockRef.current) {
      resize = new ResizeObserver(() => {
        echartsRef.current?.getEcharts()?.resize();
      });
      resize.observe(editCardBlockRef.current);
    }
    return () => {
      resize?.disconnect();
    };
  }, [echartsRef, isSmall]);

  useEffect(() => {
    echartsRef.current?.setOption((echarts) => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
        formatter(
          params: {
            name: string;
            value: number;
          }[]
        ) {
          let html = `<div style="color:#040919">${params[0].name}</div>`;
          const messgeData = chartList.map(
            (m, index) =>
              `<div style="display:flex;align-items:center">
                  <span style="width: 6px; height: 6px ;border-radius: 50%;background: ${
                    m.color
                  };display: inline-block"></span>
                  <span style="margin-left:4px;color:#040919">${
                    m.skuName
                  }<span style="margin-left:10px;color:#040919">${formatPrice(
                params[index].value
              )}</span> </span>
                </div>`
          );
          html += messgeData.join('');
          return html;
        },
      },
      grid: {
        top: '10%',
        left: '1%',
        right: '2%',
        bottom: '2%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [
          `1${t('desk_financeChart_month')}`,
          `2${t('desk_financeChart_month')}`,
          `3${t('desk_financeChart_month')}`,
          `4${t('desk_financeChart_month')}`,
          `5${t('desk_financeChart_month')}`,
          `6${t('desk_financeChart_month')}`,
          `7${t('desk_financeChart_month')}`,
          `8${t('desk_financeChart_month')}`,
          `9${t('desk_financeChart_month')}`,
          `10${t('desk_financeChart_month')}`,
          `11${t('desk_financeChart_month')}`,
          `12${t('desk_financeChart_month')}`,
          // '1月',
          // '2月',
          // '3月',
          // '4月',
          // '5月',
          // '6月',
          // '7月',
          // '8月',
          // '9月',
          // '10月',
          // '11月',
          // '12月',
        ],
        axisLabel: {
          color: '#888B98',
        },
      },
      yAxis: {
        type: 'value',
        min: chartList.every((e) => e.totalNum.length) ? null : 0,
        max: chartList.every((e) => e.totalNum.length) ? null : 2500,
        splitLine: {
          lineStyle: {
            type: 'dotted',
            color: 'rgba(177, 179, 190, 0.3)',
          },
        },
      },
      series: chartList.map((item) => {
        if (item.totalNum.length) {
          return {
            type: 'line',
            lineStyle: {
              color: item.color,
            },
            itemStyle: {
              normal: {
                color: item.color,
                borderColor: '#fff',
                borderType: 'solid',
                borderWidth: 1,
              },
              lineStyle: {
                width: 20,
                type: 'dotted',
              },
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: item.startColor,
                },
                {
                  offset: 1,
                  color: item.endColor,
                },
              ]),
            },
            data: item.totalNum,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 15,
          };
        }
        return {};
      }),
    }));
  }, [chartList, echartsRef, isShow]); // eslint-disable-line

  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentYear]);

  return (
    <CardContext
      loading={loading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName ?? defaultAppName}
    >
      <div
        ref={editCardBlockRef}
        className={classNames(styles.financeContrastChartCard, { [styles.small]: isSmall })}
      >
        {isShow ? (
          <div className={styles.editCardBlock}>
            <div className={styles.editCard}>
              <CardHeaderTitle
                size={editCardBlockRef.current?.offsetWidth}
                isSmall={isSmall}
                logo={logo}
                cardName={appName ?? defaultAppName}
                headerRight={
                  <div className={styles.rightItemBtns}>
                    {/* <div
                    className={styles.resetBtn}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      if (calendarYearRef) {
                        calendarYearRef.current?.resetDate();
                        setCurrentYear(dateYearNum);
                        setIsShow(false);
                      }
                    }}
                  >
                    重置
                  </div> */}
                    <div
                      className={styles.resetBtn}
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        setIsShow(false);
                      }}
                    >
                      {t('desk_financeChart_done')}
                    </div>
                  </div>
                }
              />
              <div className={styles.body}>
                <CalendarYear
                  ref={calendarYearRef}
                  currentYear={currentYear}
                  isShowHeadrSwitch={false}
                  isSmall={isSmall}
                  onYearChange={(year) => {
                    setCurrentYear(year);
                    setIsShow(false);
                  }}
                />
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.financeChart}>
            <CardHeaderTitle
              size={editCardBlockRef.current?.offsetWidth}
              isSmall={isSmall}
              logo={logo}
              cardName={appName ?? defaultAppName}
              headerRight={
                <div className={styles.headerRight}>
                  <div className={styles.dateItem}>
                    <span className={styles.dateText}>{currentYear}</span>
                    <span className={styles.dateText}>{t('desk_financeChart_year')}</span>
                  </div>
                </div>
              }
            />
            <div className={styles.body}>
              <Echarts ref={echartsRef} className={styles.echarts} />
            </div>
            <div
              className={classNames(styles.editItemBtn, styles.isShowEdit)}
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setIsShow(true);
              }}
            >
              {t('desk_financeChart_edit')}
            </div>
          </div>
        )}
      </div>
    </CardContext>
  );
}

FinanceContrastChart.defaultProps = {
  // appName: '收付金额对比',
  appName: undefined,
  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1723021260207222.png`,
};

export default memo(FinanceContrastChart);
