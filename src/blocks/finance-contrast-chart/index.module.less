.financeContrastChartCard {
  width: 100%;
  height: 100%;
}

.financeChart {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  position: relative;
  flex-direction: column;
  background-color: rgb(255 255 255);

  &:hover {
    .isShowEdit {
      display: block;
    }
  }
}

.body {
  width: 100%;
  height: 100%;
}

.dateItem {
  color: #888b98;
  font-size: 12px;
  min-width: 60px;
  height: 24px;
  line-height: 22px;
  text-align: center;
  border-radius: 50px;
  border: 1px solid #b1b3be;
  font-family: '苹方-简', sans-serif;
}

.echarts {
  width: 100%;
  height: 100%;
}

.editCardBlock {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rightItemBtns {
  display: flex;
}

.resetBtn {
  color: #040919;
  font-size: 10px;
  width: 40px;
  height: 18px;
  line-height: 17px;
  padding: 1px;
  box-sizing: border-box;
  border-radius: 4px;
  text-align: center;
  font-family: '苹方-简', sans-serif;
  background: rgb(255 255 255 / 22%);
  border: 0.5px solid rgb(0 0 0 / 10%);
  box-shadow: 0 1px 1px 0 rgb(0 0 0 / 8%);
  cursor: pointer;
}

.editCard {
  display: flex;
  width: 256px;
  height: 256px;
  padding: 12px;
  flex-direction: column;
  border-radius: 18px;
  background: #fff;
}

.editItemBtn {
  display: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  box-sizing: border-box;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: rgb(255 255 255 / 30%);
  background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
    rgb(255 255 255 / 20%);
  backdrop-filter: blur(30px);
  box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  cursor: pointer;
}

.small {
  .financeChart {
    padding: 10px;
  }

  .dateItem {
    font-size: 10px;
  }

  .editItemBtn {
    font-size: 12px;
    height: 34px;
    line-height: 34px;
  }

  .editCard {
    width: 170px;
    height: 168px;
    padding: 12px;
  }
}
