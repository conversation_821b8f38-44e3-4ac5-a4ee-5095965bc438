.outSide {
  :global {
    width: 100%;
    height: 100%;
    position: relative;
    // border: 1px solid black;
    border-radius: 18px;
    background: rgb(255 255 255 / 70%);
    user-select: none;
    backdrop-filter: blur(25.6px);
  }
}

.insideTop {
  :global {
    display: flex;
    width: calc(100% - 24px);
    height: 42px;
    align-items: center;
    justify-content: space-between;
  }
}

.logo {
  :global {
    width: 22px;
    height: 22px;
    margin-right: 4px;
    margin-left: 12px;
  }
}

.clip {
  width: 110px;
  margin-left: 5px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.procurement {
  :global {
    font-size: 12px;
    font-weight: 500;
    margin-top: 1px;
    // margin-left: -33px;
  }
}

.demo {
  :global {
    color: #888b98;
    font-size: 12px;
    width: 120px;
    max-width: 120px;
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
  }
}

.number {
  :global {
    // font-family: D-DIN;
    font-size: 28px;
    font-weight: bold;
  }
}

.title {
  :global {
    color: #888b98;
    font-size: 12px;
    width: 60px;
    overflow: hidden;
    cursor: pointer;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }
}

.insideBottomWrap {
  :global {
    display: flex;
    // align-content: flex-start;
    flex-wrap: wrap;
    // height: 100%;
  }
}

.insideBottom {
  :global {
    display: flex;
    width: 85px;
    height: 70px;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    // float: left;
  }
}

.editBtn {
  :global {
    display: none;
    width: 100%;
    height: 40px;
    line-height: 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    cursor: pointer;
    border-radius: 0 0 18px 18px;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    text-align: center;
  }
}

.outSide:hover .editBtn {
  :global {
    display: block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1000;
    cursor: pointer;
    border-radius: 0 0 18px 18px;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    text-align: center;
    backdrop-filter: blur(30px);
  }
}

// 小尺寸的卡片样式
.small {
  .insideTop {
    :global {
      width: calc(100% - 16px);
      height: 32px;
    }
  }

  .logo {
    :global {
      width: 12px;
      height: 12px;
      margin-right: 0;
      margin-left: 12px;
    }
  }

  .procurement {
    :global {
      font-size: 8px;
    }
  }

  .demo {
    :global {
      font-size: 8px;
      width: 40px;
      max-width: 40px;
    }
  }

  .number {
    :global {
      font-size: 18px;
      font-weight: bold;
      height: 24px;
    }
  }

  .title {
    :global {
      font-size: 8px;
      width: 100%;
      text-align: center;
    }
  }

  .insideBottom {
    :global {
      width: 33.33%;
      height: 44px;
    }
  }
}

.small:hover .editBtn {
  :global {
    font-size: 8px;
    height: 24px;
    line-height: 24px;
  }
}
