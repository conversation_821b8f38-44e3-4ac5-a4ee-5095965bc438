import { createHttpRequest, PaginationResponse } from '@echronos/core';

/**
 * @description 待审核入、出库单据
 */

interface StockApprovalParams {
  orderStatus: number;
  pageNo: number;
  pageSize: number;
}

export interface StockApprovalData {
  id: number;
  orderNo: string;
  customerCompanyName: string;
  businessName: string;
  businessType: number;
}

function getUrl(val: string) {
  switch (val) {
    case 'enter':
      return 'inbound';
    case 'out':
      return 'outbound';
    default:
      return '';
  }
}

function getStockApprovalApi(
  params: StockApprovalParams,
  type: string
): PaginationResponse<StockApprovalData> {
  return createHttpRequest('ech-psi')(`/v1/platform/${getUrl(type)}/order/selectPage`, {
    params,
    autoToast: false,
  });
}

export default getStockApprovalApi;
