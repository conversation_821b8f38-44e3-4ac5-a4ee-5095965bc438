import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { isFunction } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import NoPermissionCard from '@/blocks/public/components/no-permission-card';
import { IEchBlockCard } from '@/blocks/interface';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import useElSize from '@/hooks/use-el-size';
import getEnumData from '@/utils/stock/stock-order-enums';
import type { StockApprovalData } from './get-stock-approval-api';
import getStockApprovalApi from './get-stock-approval-api';
import './stock-approval.less';

const orders = [
  {
    type: 'enter',
    titleKey: 'desk_stockApproval_enter',
    listUrl: '/stock/enter?orderStatus=0',
    searchUrl: '/stock/enter?orderStatus=0&showSearch=1',
    code: 'AV_001_002',
  },
  {
    type: 'out',
    titleKey: 'desk_stockApproval_out',
    listUrl: '/stock/out?orderStatus=0',
    searchUrl: '/stock/out?orderStatus=0&showSearch=1',
    code: 'AV_001_001',
  },
];

interface StockApprovalEntryProps extends IEchBlockCard {
  type: string;
}

function StockApprovalEntry({ type, navigate }: StockApprovalEntryProps) {
  const { t } = useTranslation();
  const [list, setList] = useState<StockApprovalData[]>([]);
  const requestParams = useRef({
    orderStatus: 0,
    pageNo: 1,
    pageSize: 10,
  });
  const [isPerm, setIsPerm] = useState(true);
  const totalPages = useRef(0);
  const orderInfo = orders.find((item) => item.type === type);

  // 获取当前DOM元素
  const echBlockStockApprovalRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockStockApprovalRef, 324);

  const { run } = useRequest(getStockApprovalApi, {
    manual: true,
    defaultParams: [{ ...requestParams.current }, type],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (requestParams.current.pageNo === 1) {
        setList([...res.list]);
      } else {
        setList([...list, ...res.list]);
      }
      requestParams.current.pageNo += 1;
    },
    onError: (err: any) => {
      if (err.code === 5) {
        setIsPerm(false);
      }
    },
  });

  const onLoadMore = () => {
    if (!(requestParams.current.pageNo <= totalPages.current)) return;
    run({ ...requestParams.current }, type);
  };

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  useEffect(() => {
    run({ ...requestParams.current }, type);
  }, [run, type]);

  return (
    <div
      ref={echBlockStockApprovalRef}
      className={classNames('ech-block-stock-approval', { small: isSmall })}
    >
      <div className="ech-block-stock-approval-header">
        <span
          role="button"
          tabIndex={0}
          className="ech-block-stock-approval-header-text"
          onClick={() => onNavTo(orderInfo?.listUrl || '')}
        >
          {t('desk_stockApproval_pendingTitle', { type: t(orderInfo?.titleKey || '') })}
        </span>
        {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
        <img
          className="ech-block-stock-approval-header-img"
          src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722590524163162.png`}
          alt=""
          onClick={() => onNavTo(orderInfo?.searchUrl || '')}
        />
      </div>
      <div className="ech-block-stock-approval-title">
        <div className="ech-block-stock-approval-title-text">
          <span>{t('desk_stockApproval_supplier')}</span>
          <span>{t('desk_stockApproval_orderType')}</span>
        </div>
      </div>
      {list.length > 0 ? (
        <div
          className="ech-block-stock-approval-content"
          id={`ech-block-stock-approval-content-${type}`}
          onWheel={(e) => e.stopPropagation()}
        >
          <InfiniteScroll
            scrollableTarget={`ech-block-stock-approval-content-${type}`}
            dataLength={list.length}
            hasMore={requestParams.current.pageNo < totalPages.current}
            endMessage={null}
            next={onLoadMore}
            loader={null}
          >
            {list.map((item) => (
              <div
                role="button"
                tabIndex={0}
                key={item.id}
                className="ech-block-stock-approval-item"
                onClick={() => {
                  const businessType = getEnumData('businessType', type, item.businessType, 'type');
                  const stockType = type === 'out' ? 'outbound' : 'inbound';
                  onNavTo(`/stock/${stockType}/detail?stockType=${businessType}&id=${item.id}`);
                }}
              >
                <div className="ech-block-stock-approval-item-text">
                  {item.customerCompanyName || item.orderNo}
                </div>
                <div>{item.businessName}</div>
              </div>
            ))}
          </InfiniteScroll>
        </div>
      ) : (
        <div className="ech-block-stock-approval-no-data">
          <img
            className="ech-block-stock-approval-no-data-img"
            src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722821732400463.png`}
            alt=""
          />
          {t('desk_stockApproval_noData')}
        </div>
      )}
      {!isPerm && (
        <NoPermissionCard
          appName={t('desk_stockApproval_warehouseManagement')}
          isSmall={isSmall}
          size={echBlockStockApprovalRef.current?.offsetWidth}
        />
      )}
    </div>
  );
}

export default StockApprovalEntry;
