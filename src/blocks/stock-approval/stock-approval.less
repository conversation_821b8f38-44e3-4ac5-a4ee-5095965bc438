.ech-block-stock-approval {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(25.6px);
}

.ech-block-stock-approval-header {
  font-weight: 600;
  display: flex;
  padding: 12px;
  justify-content: space-between;
}

.ech-block-stock-approval-header-text {
  cursor: pointer;
}

.ech-block-stock-approval-header-img {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.ech-block-stock-approval-title {
  padding: 0 12px;
}

.ech-block-stock-approval-title-text {
  color: #888b98;
  font-size: 12px;
  display: flex;
  height: 28px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  border-radius: 13px;
  background: rgb(198 204 216 / 30%);

  & > span {
    line-height: 18px;
  }
}

.ech-block-stock-approval-content {
  padding: 0 12px;
  flex: 1;
  overflow: auto;
  scrollbar-width: none;
}

.ech-block-stock-approval-item {
  font-size: 12px;
  display: flex;
  padding: 14px 16px;
  justify-content: space-between;
  cursor: pointer;

  &:not(:last-child) {
    border-bottom: 1px solid rgb(0 25 109 / 10%);
  }
}

.ech-block-stock-approval-item-text {
  &:hover {
    color: #008cff;
  }
}

.ech-block-stock-approval-no-data {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.ech-block-stock-approval-no-data-img {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
}

// 小尺寸的卡片样式
.small {
  .ech-block-stock-approval-header {
    font-weight: 600;
    display: flex;
    padding: 12px;
    justify-content: space-between;
  }

  .ech-block-stock-approval-header-text {
    font-size: 10px;
  }

  .ech-block-stock-approval-header-img {
    width: 12px;
    height: 12px;
    cursor: pointer;
  }

  .ech-block-stock-approval-title-text {
    font-size: 10px;
    height: 24px;
  }

  .ech-block-stock-approval-item {
    font-size: 8px;
    padding: 11px 16px;
  }
}
