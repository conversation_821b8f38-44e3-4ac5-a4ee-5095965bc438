import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import SelectionCondition from './conpoment';
import styles from './index.module.less';

function Scrm() {
  const { t } = useTranslation();
  const [quartered, setQuartered] = useState<number[]>([]); // 季度
  const [visible, setVisible] = useState(false);

  return (
    <div className={styles.scrm_component}>
      <div
        role="button"
        tabIndex={0}
        onClick={() => {
          setVisible(true);
        }}
      >
        {t('desk_scrm_trigger')}
      </div>
      <SelectionCondition
        className={styles.pink}
        quartered={quartered}
        radioQuarter
        memberType={1}
        radioMember
        visible={visible}
        memberIded={[143]}
        onSuccess={(e) => {
          setQuartered(e);
          setVisible(false);
        }}
      />
    </div>
  );
}

export default Scrm;
