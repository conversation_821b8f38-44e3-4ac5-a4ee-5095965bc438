.editbox {
  color: #040919;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  position: absolute;
  top: 0;
  left: 0;
  flex-direction: column;
  background-color: #fff;
}

.editHead {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.success {
  font-size: 10px;
  display: flex;
  width: 40px;
  height: 18px;
  line-height: 1;
  justify-content: center;
  box-sizing: border-box;
  cursor: pointer;
  align-items: center;
  border-radius: 4px;
  background: rgb(255 255 255 / 22%);
  border: 0.5px solid rgb(0 0 0 / 10%);
  box-shadow: 0 1px 1px 0 rgb(0 0 0 / 8%);
}

.logoName {
  font-size: 15px;
  font-weight: 500;
  display: flex;
  align-items: center;

  img {
    width: 22px;
    height: 22px;
    margin-right: 4px;
  }
}

.tip {
  color: rgb(4 9 25 / 70%);
  font-size: 12px;
  line-height: 20px;
  margin-top: 16px;
  margin-bottom: 8px;
}

.slectBox {
  position: relative;
  cursor: pointer;
}

.select {
  color: #040919;
  font-size: 14px;
  display: flex;
  min-height: 34px;
  padding: 6px 12px;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px;
  background: #f5f6fa;
}

.selectLabel {
  min-width: fit-content;
  padding-right: 20px;
}

.tier {
  margin-top: 8px;
  overflow-y: auto;
  flex: 1;
}

.onselect {
  flex: 1;
  text-align: right;
  color: #008cff;
  font-size: 12px;
  white-space: pre-wrap; /* 保留换行 */
  overflow-wrap: break-word; /* 长单词换行 */
  word-break: break-all; /* 任何地方换行 */
}

.box {
  width: 72px;
  padding: 4px;
  position: absolute;
  top: 36px;
  right: 0;
  cursor: pointer;
  border-radius: 6px;
  background: rgb(228 228 228 / 60%);
  backdrop-filter: blur(50px);
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
  z-index: 999;
}

.selectItem {
  color: #040919;
  font-size: 12px;
  display: flex;
  height: 20px;
  margin-bottom: 2px;
  justify-content: center;
  align-items: center;
  border-radius: 3px;

  &:hover {
    color: #008cff;
    background: rgb(255 255 255 / 70%);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.selected {
  color: #008cff;
  background: rgb(255 255 255 / 70%);
}

// 成员

.memberList {
  color: #040919;
  display: flex;
  width: calc(100% - 36px);
  height: calc(100% - 28px);
  padding: 12px;
  overflow: hidden;
  position: absolute;
  top: 14px;
  left: 14px;
  box-sizing: border-box;
  flex-direction: column;
  border-radius: 14px;
  background: #fff;
  border: 1px solid rgb(0 0 0 / 15%);
  box-shadow: 8px 6px 20px 0 rgb(2 9 58 / 12%), -8px 6px 20px 0 rgb(2 9 58 / 12%);
}

.memberTitle {
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  margin-bottom: 8px;
}

.search {
  display: flex;

  .cancel {
    color: #008cff;
    font-size: 12px;
    display: flex;
    min-width: fit-content;
    height: 22px;
    margin-left: 4px;
    justify-content: center;
    cursor: pointer;
    align-items: center;
  }

  .ant-input-affix-wrapper-sm {
    padding: 0 7px;
    border-radius: 4px !important;
  }
}

.selectMember {
  flex: 1;
  overflow-y: auto;
}

.itemMember {
  font-size: 14px;
  display: flex;
  line-height: 22px;
  padding: 8px 0;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.5px solid rgb(0 0 0 / 20%);

  .icon {
    min-width: 24px;
    height: 22px;
  }
}

.member {
  flex: 1;
  white-space: pre-wrap; /* 保留换行 */
  overflow-wrap: break-word; /* 长单词换行 */
  word-break: break-all; /* 任何地方换行 */
}

.nullData {
  display: flex;
  width: 100%;
  height: 100px;
  justify-content: center;
  align-items: center;
}
