import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import useElSize from '@/hooks/use-el-size';
import type { ProcurementNtionProp } from '../uoum-card/api/get-uoum-procurement-notice';
import { getUoumProcurementNotice } from '../uoum-card/api/get-uoum-procurement-notice';
import Cart from '../uoum-card/components/cart-title';
import CartContent from '../uoum-card/components/cart-content';

function UoumProcurementNoticeCard() {
  const { t } = useTranslation();
  // 获取当前卡片的DOM元素
  const uoumProcurementNoticeCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(uoumProcurementNoticeCardRef, 324);
  // 获取元素的宽度
  const domWidth = uoumProcurementNoticeCardRef.current?.offsetWidth;

  const [notionData, setNotionData] = useState<ProcurementNtionProp[]>([]);
  const { run, loading } = useRequest(
    () =>
      getUoumProcurementNotice({
        siteId: +import.meta.env.BIZ_UM_VITE_SITE_ID,
        categoryIdList: [],
        pageNo: 1,
        pageSize: 2,
        trendsTypeList: [0, 1, 2, 4],
      }),
    {
      manual: true,
      onSuccess: (res) => {
        setNotionData(res.list);
      },
    }
  );
  useEffect(() => {
    run();
  }, [run]);
  return (
    <div ref={uoumProcurementNoticeCardRef} style={{ width: '100%', height: '100%' }}>
      {!loading && (
        <Cart
          type="ProcurementNtion"
          url="bidding/0124"
          title={t('desk_uoumProcurement_title')}
          isSmall={isSmall}
          size={domWidth}
        >
          <CartContent
            data={notionData}
            type="notion"
            url="bidding/0124"
            isSmall={isSmall}
            size={domWidth}
          />
        </Cart>
      )}
    </div>
  );
}

export default UoumProcurementNoticeCard;
