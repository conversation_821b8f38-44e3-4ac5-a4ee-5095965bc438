import { createHttpRequest } from '@echronos/core';

/**
 * 获取团队商机成熟度覆盖率卡片
 */

export interface DeptInfoType {
  deptId: number; // 团队ID
  deptName: string; // 团队名称
}

interface TunityMoneyType {
  money: string;
  goalRate?: string;
  holidayMoney?: string;
  changeMoney: string;
  changeType: number;
}

export interface DeptMatureCardType {
  deptList: DeptInfoType[]; // 团队信息列表
  opportunityMoney: TunityMoneyType; // 商机总金额
  matureOpportunityMoney: TunityMoneyType; // 成熟商机总金额
  deptStatisticsQuarter: number[]; // 季度列表
  deptStatisticsList: number[]; // 团队id
}

function getDeptMatureCard(blockId: string): Promise<DeptMatureCardType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getDeptMatureCard', {
    method: 'GET',
    params: {
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default getDeptMatureCard;
