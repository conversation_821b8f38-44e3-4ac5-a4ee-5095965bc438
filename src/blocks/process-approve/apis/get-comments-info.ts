import { createHttpRequest } from '@echronos/core';

export interface CommentsInfo {
  processName: ''; // 流程名称
  intermediateNodeVo: {
    createTime: number;
    staffInfoList: {
      memberName: string;
    }[];
    isArrive: number;
    nodeTitle: string;
  }[];
}

interface GetCommentsInfoParams {
  processInstanceId: string;
}

/**
 * 获取待审批数据详情
 * @param data
 */
function getCommentsInfo(params: GetCommentsInfoParams): Promise<CommentsInfo> {
  return createHttpRequest('ech-workflow')('/v1/flowable/processInstance/commentsInfo', {
    params,
    method: 'GET',
    autoToast: false,
  });
}

export default getCommentsInfo;
