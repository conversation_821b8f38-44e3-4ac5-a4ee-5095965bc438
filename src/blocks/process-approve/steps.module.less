@import '@/styles/mixins/mixins.less';

.stepsBox {
  .colBox {
    display: inline-block;
    vertical-align: middle;
    position: relative;
  }

  .dashedLine {
    width: 1px;
    height: 45px;
    position: absolute;
    top: 0;
    left: 6px;
    z-index: -1;
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023815/1692106136040738.png');

    &.active {
      background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023816/1692149647732218.png');
    }
  }

  .contentBox {
    max-width: calc(100% - 30px);
    margin-left: 12px;
    .text-overflow();
  }

  .status {
    width: 12px;
    height: 12px;
  }

  .finish {
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181169298328.png');
    background-size: 100% 100%;
  }

  .process {
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181239596297.png');
    background-size: 100% 100%;
  }

  .wait {
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181258334846.png');
    background-size: 100% 100%;
  }

  .stepItem {
    color: #888b98;
    font-size: 12px;
    line-height: 18px;
    padding: 8px 0;
  }
}

._150 {
  .stepItem {
    font-size: 8px;
  }

  .contentBox {
    margin-left: 10px;
  }
}
