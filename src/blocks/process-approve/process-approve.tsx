import { useEffect, useState, memo, useRef } from 'react';
import { useRequest } from 'ahooks';
import classnames from 'classnames';
import { isFunction } from 'lodash';
import dayjs from 'dayjs';
import { Spin } from '@echronos/antd';
import { IEchBlockCard } from '@/blocks/interface';
import { useTranslation } from 'react-i18next';
import useElSize from '@/hooks/use-el-size';
import getDesktopMyAppProcess from './apis/get-desktop-my-app-process';
import { IMyApp } from './apis/model';
import Steps from './steps';
import styles from './process-approve.module.less';
import './process-approve.less';
import NoDataCard from '../public/components/no-data-card';

export interface ProcessApproveProps extends IEchBlockCard {
  appName?: string;
  logo?: string;
}

interface TaskInfo {
  processName: string;
  createTime: string;
  nodeTitle: string;
  steps: {
    nodeTitle: string;
    nodeContent: string;
  }[];
}

function ProcessApprove({ appName, logo, navigate }: ProcessApproveProps) {
  const [task, setTask] = useState<TaskInfo>(null as unknown as TaskInfo);
  // 获取当前卡片的DOM元素
  const processApproveRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(processApproveRef, 150);
  // 多语言导入
  const { t } = useTranslation();
  const processAppName = appName || t('desk_apps_procAprv');
  // @ts-ignore
  const { data, loading, run }: { data: IMyApp; loading: boolean; run: () => void } = useRequest(
    () =>
      getDesktopMyAppProcess().then((res) => {
        if (!res.list.length) {
          return {};
        }
        return res.list[0];
      }),
    {
      manual: true,
    }
  );

  const JumpAppFn = () => {
    let processInstanceId = '';
    if (data.content && data.content[0] && data.content[0].workflowProcessInstanceId) {
      processInstanceId = data.content[0].workflowProcessInstanceId;
    }
    if (isFunction(navigate)) {
      navigate(`/admin/process/pending?processInstanceId=${processInstanceId}`);
    }
  };

  useEffect(() => {
    if (!data?.content || !data?.content[0]) {
      return;
    }
    const content = data.content[0];

    const steps = [];
    if (content?.intermediateNodeVo?.length > 0) {
      steps.push({
        nodeTitle: content.intermediateNodeVo[0].nodeTitle,
        nodeContent: content.intermediateNodeVo[0].staffInfoList[0].memberName,
      });
      const approveIndex = content.intermediateNodeVo.findIndex(
        (item: { isArrive: number }) => item.isArrive === 1
      );
      if (approveIndex >= 0) {
        steps.push({
          nodeTitle: content.intermediateNodeVo[approveIndex].nodeTitle,
          nodeContent: content.intermediateNodeVo[approveIndex].staffInfoList
            .map((item: { memberName: string }) => `${item.memberName}${t('desk_apps_aprv')}`)
            .join('、'),
        });
      }
      const next = approveIndex + 1;
      if (content.intermediateNodeVo[next]) {
        let checkPersonsStr = '';
        if (content.intermediateNodeVo[next]?.staffInfoList?.length > 0) {
          if (content.intermediateNodeVo[next].staffInfoList?.length > 1) {
            checkPersonsStr = `${content.intermediateNodeVo[2].staffInfoList[0].memberName}${t(
              'desk_apps_and'
            )}${content.intermediateNodeVo[next].staffInfoList?.length}${t('desk_apps_person')}`;
          } else {
            checkPersonsStr = `${content.intermediateNodeVo[2].staffInfoList[0].memberName}`;
          }
        }
        steps.push({
          nodeTitle: content.intermediateNodeVo[next].nodeTitle,
          nodeContent: checkPersonsStr,
        });
      }
    }
    setTask({
      processName: content?.workflowProcessName,
      createTime: dayjs(content?.intermediateNodeVo[0]?.createTime).format(t('desk_apps_time')),
      nodeTitle: content?.intermediateNodeVo[1].nodeTitle,
      steps,
    });
  }, [data]); //eslint-disable-line

  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      ref={processApproveRef}
      className={classnames(styles.processApprove, 'm-process-approve', {
        [styles.small]: isSmall,
      })}
      role="button"
      tabIndex={0}
      onClick={(e) => {
        e.stopPropagation();
        JumpAppFn();
      }}
    >
      <Spin spinning={loading}>
        <div className={classnames(styles.header, 'header')}>
          <div
            className={classnames(styles.icon, 'icon')}
            style={{ backgroundImage: logo ? `url(${logo})` : undefined }}
          />
          <span className={classnames(styles.title, 'title')}>{processAppName}</span>
        </div>
        {task ? (
          <>
            <div className={styles.line}>
              <div className={classnames(styles.processTitle, 'process-title')}>
                {task.processName}
              </div>
              <div className={styles.status}>{t('desk_apps_status')}</div>
            </div>
            <div className={classnames(styles.time, 'time')}>
              {task.createTime}
              {t('public_submit')}
            </div>
            <Steps
              isSmall={isSmall}
              size={processApproveRef.current?.offsetWidth}
              current={1}
              data={
                task.steps?.length > 2
                  ? [
                      {
                        content: `${task.steps[0].nodeTitle} ${task.steps[0].nodeContent}`,
                        id: 1,
                      },
                      {
                        content: (
                          <div>
                            <div className={classnames(styles.approveNode, 'approve-node')}>
                              {task.steps[1].nodeTitle}
                            </div>
                            <div className={classnames(styles.approvePerson, 'approve-person')}>
                              {task.steps[1].nodeContent}
                            </div>
                          </div>
                        ),
                        id: 2,
                      },
                      {
                        content: `${task.steps[2].nodeTitle} ${task.steps[2].nodeContent}`,
                        id: 3,
                      },
                    ]
                  : [
                      {
                        content: `${task.steps[0].nodeTitle} ${task.steps[0].nodeContent}`,
                        id: 1,
                      },
                      {
                        content: (
                          <div>
                            <div className={classnames(styles.approveNode, 'approve-node')}>
                              {task.steps[1].nodeTitle}
                            </div>
                            <div className={classnames(styles.approvePerson, 'approve-person')}>
                              {task.steps[1].nodeContent}
                            </div>
                          </div>
                        ),
                        id: 2,
                      },
                    ]
              }
            />
          </>
        ) : (
          <NoDataCard
            data={t('desk_apps_nowEv')}
            msg={t('desk_apps_emptyDes')}
            size={processApproveRef.current?.offsetWidth}
            isSmall={isSmall}
          />
        )}
      </Spin>
    </div>
  );
}

ProcessApprove.defaultProps = {
  appName: undefined,
  logo: '',
};

export default memo(ProcessApprove, () => true);
