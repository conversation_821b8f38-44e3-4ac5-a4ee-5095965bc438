import { useEffect, useRef, useState } from 'react';
import { isFunction } from 'lodash';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import type { RadioChangeEvent } from '@echronos/antd';
import { Radio } from '@echronos/antd';
import { IEchBlockCard } from '@/blocks/interface';
import { formatPrice } from '@/blocks/public/utils/utils';
import CardContext from '@/blocks/public/components/card-context';
import CardHeaderTitle from '@/blocks/public/components/card-header-title';
import {
  getSalesReceivableHeadAmount,
  getPurchasePayableHeadAmount,
  getSalesCardPermission,
} from '@/blocks/public/apis';
import { useTranslation } from 'react-i18next';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import qichuIcon from './images/qichu-icon.png';
import qimoIcon from './images/qimo-icon.png';
import benqiyingshouIcon from './images/benqiyingshou-icon.png';
import benqiyishouIcon from './images/benqiyishou-icon.png';
import zengjiaIcon from './images/zengjia-icon.png';
import huishouIcon from './images/huishou-icon.png';
import styles from './index.module.less';

export type FinanceReceivablePayableProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};
const radioOptionsList = [
  { label: 'desk_financeRP_receivable', value: 1 },
  { label: 'desk_financeRP_payable', value: 0 },
];
const dateFormat = 'YYYY.MM.DD';
// 默认当前天数，往前推30天,开始时间为0点，结束时间为24点
const currentDateRange = [dayjs().subtract(29, 'days').startOf('day'), dayjs().endOf('day')];
function FinanceReceivablePayable({ appName, logo, navigate }: FinanceReceivablePayableProps) {
  const { t } = useTranslation();
  const [isPermission, setIsPermission] = useState(true);
  const [radioVal, setRadioVal] = useState(1);
  const startDate = currentDateRange[0].valueOf();
  const endDate = currentDateRange[1].valueOf();

  // 获取当前卡片的DOM元素
  const echBlockfinanceReceivablePayableRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockfinanceReceivablePayableRef, 150);

  const { data, loading, run } = useRequest(
    () =>
      Promise.all([
        getSalesReceivableHeadAmount({ startDate, endDate }),
        getPurchasePayableHeadAmount({ startDate, endDate }),
      ])
        .then((result) => ({
          receivable: result[0],
          payable: result[1],
        }))
        .catch((err) => {
          if (err[0]?.code === 5 || err[1]?.code === 5) {
            setIsPermission(false);
          }
          return {
            receivable: {
              periodicDepositAmount: '0',
              addAmount: '0',
              takeBackAmount: '0',
              endTermAmount: '0',
            },
            payable: {
              periodicDepositAmount: '0',
              addAmount: '0',
              takeBackAmount: '0',
              endTermAmount: '0',
            },
          };
        }),
    {
      manual: true,
    }
  );

  const getCardPermission = () => {
    getSalesCardPermission()
      .then(() => {
        run();
      })
      .catch((err) => {
        if (err.code === 5) {
          setIsPermission(false);
        }
      });
  };

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const JumpFinanceFn = () => {
    onNavTo(
      radioVal
        ? `/finance-management/receivable?startTime=${startDate}&endTime=${endDate}`
        : `/finance-management/payable?startTime=${startDate}&endTime=${endDate}`
    );
  };
  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    setRadioVal(value);
  };

  useEffect(() => {
    getCardPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={loading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName ?? t('desk_financeRP_defaultName')}
    >
      <div
        ref={echBlockfinanceReceivablePayableRef}
        className={classNames(styles.financeReceivablePayable, { [styles.small]: isSmall })}
      >
        <CardHeaderTitle
          size={echBlockfinanceReceivablePayableRef.current?.offsetWidth}
          isSmall={isSmall}
          logo={logo}
          cardName={appName ?? t('desk_financeRP_defaultName')}
          headerRight={
            <div className={styles.headerRight}>
              <Radio.Group
                options={radioOptionsList.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                onChange={onChange}
                value={radioVal}
                optionType="button"
                buttonStyle="solid"
                size="small"
              />
            </div>
          }
        />
        <div className={styles.body} role="button" tabIndex={0} onClick={() => JumpFinanceFn()}>
          <div className={styles.bodyTop}>
            <span>{t('desk_financeRP_dateRange')}</span>
            <span>
              {dayjs(startDate).format(dateFormat)} - {dayjs(endDate).format(dateFormat)}
            </span>
          </div>
          <div className={styles.bodyBottom}>
            <div className={styles.rankItem}>
              <span className={styles.rankItemTitle}>
                <img src={qichuIcon} alt="icon" className={styles.icon} />
                {t('desk_financeRP_openingBalance')}
              </span>
              <span
                className={styles.rankItemPrice}
                title={
                  radioVal
                    ? `¥${formatPrice(data?.receivable.periodicDepositAmount || 0)}`
                    : `¥${formatPrice(data?.payable.periodicDepositAmount || 0)}`
                }
              >
                {radioVal
                  ? `¥${formatPrice(data?.receivable.periodicDepositAmount || 0)}`
                  : `¥${formatPrice(data?.payable.periodicDepositAmount || 0)}`}
              </span>
            </div>
            <div className={styles.rankItem}>
              <span className={styles.rankItemTitle}>
                <img src={qimoIcon} alt="icon" className={styles.icon} />
                {t('desk_financeRP_closingBalance')}
              </span>
              <span
                className={styles.rankItemPrice}
                title={
                  radioVal
                    ? `¥${formatPrice(data?.receivable.endTermAmount || 0)}`
                    : `¥${formatPrice(data?.payable.endTermAmount || 0)}`
                }
              >
                {radioVal
                  ? `¥${formatPrice(data?.receivable.endTermAmount || 0)}`
                  : `¥${formatPrice(data?.payable.endTermAmount || 0)}`}
              </span>
            </div>
            <div className={styles.rankItem}>
              <span className={styles.rankItemTitle}>
                <img
                  src={radioVal ? benqiyingshouIcon : zengjiaIcon}
                  alt="icon"
                  className={styles.icon}
                />
                {radioVal
                  ? t('desk_financeRP_currentReceivable')
                  : t('desk_financeRP_currentPayable')}
              </span>
              <span
                className={styles.rankItemPrice}
                title={
                  radioVal
                    ? `¥${formatPrice(data?.receivable.addAmount || 0)}`
                    : `¥${formatPrice(data?.payable.addAmount || 0)}`
                }
              >
                {radioVal
                  ? `¥${formatPrice(data?.receivable.addAmount || 0)}`
                  : `¥${formatPrice(data?.payable.addAmount || 0)}`}
              </span>
            </div>
            <div className={styles.rankItem}>
              <span className={styles.rankItemTitle}>
                <img
                  src={radioVal ? benqiyishouIcon : huishouIcon}
                  alt="icon"
                  className={styles.icon}
                />
                {radioVal ? t('desk_financeRP_received') : t('desk_financeRP_paid')}
              </span>
              <span
                className={styles.rankItemPrice}
                title={
                  radioVal
                    ? `¥${formatPrice(data?.receivable.takeBackAmount || 0)}`
                    : `¥${formatPrice(data?.payable.takeBackAmount || 0)}`
                }
              >
                {radioVal
                  ? `¥${formatPrice(data?.receivable.takeBackAmount || 0)}`
                  : `¥${formatPrice(data?.payable.takeBackAmount || 0)}`}
              </span>
            </div>
          </div>
        </div>
      </div>
    </CardContext>
  );
}

FinanceReceivablePayable.defaultProps = {
  // appName: '应收应付',
  appName: undefined,

  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1723021260207222.png`,
};

export default FinanceReceivablePayable;
