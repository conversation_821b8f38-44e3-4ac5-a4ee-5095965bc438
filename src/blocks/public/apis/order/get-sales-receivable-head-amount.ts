import { createHttpRequest } from '@echronos/core';
import type {
  PurchasePayableHeadAmountParams,
  PurchasePayableHeadAmountResponse,
} from './get-purchase-payable-head-amount';

/**
 * 销售应收-主页头部
 */
function getSalesReceivableHeadAmount(
  params: PurchasePayableHeadAmountParams
): Promise<PurchasePayableHeadAmountResponse> {
  return createHttpRequest('ech-order')('/v1/order/sales/receivableHeadAmount', {
    method: 'GET',
    params,
    autoToast: false,
  });
}

export default getSalesReceivableHeadAmount;
