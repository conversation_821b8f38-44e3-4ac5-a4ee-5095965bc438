import { createHttpRequest, PaginationResponse } from '@echronos/core';

interface FinanceHomeTwo {
  year: string;
}

export interface FinanceHomeTwoTotal {
  totalType: 1 | 2 | 3 | 4 | 5 | 6; // 1总订单收款，,2总订单付款，,3总充值收款，,4总充值付款，,5总提现收款，,6总提现付款
  totalTypeStr: string;
  type: 1 | 2; // ,1收款，,2付款
  typeStr: string;
  totalAmount: number; // 统计金额
  count: number; // 统计数量
  monthIndex: string; // 年月
}

/**
 * 财务-部分统计-收付金额对比
 */
function getFinanceHomePageTwo(params: FinanceHomeTwo): PaginationResponse<FinanceHomeTwoTotal> {
  return createHttpRequest('ech-finance')('/v1/finance/home/<USER>', {
    params,
    method: 'GET',
    autoToast: false,
  });
}

export default getFinanceHomePageTwo;
