// eslint-disable-next-line no-unused-vars
type SimpleFn<Value = void, RETURN = void> = (value: Value) => RETURN;

interface OnErrorEventHandlerNonNull {
  // eslint-disable-next-line no-unused-vars
  (event: Event | string, source?: string, lineno?: number, colno?: number, error?: Error): any;
}

type OnErrorEventHandler = OnErrorEventHandlerNonNull | null;

function appendScript<Value = any>(
  varName: string,
  script: string,
  onSuccess?: SimpleFn<Value> | null,
  onError?: OnErrorEventHandler
) {
  if (import.meta.env.SSR) {
    return;
  }
  // @ts-ignore
  const plugin = window[varName] as Value | null;
  if (plugin) {
    onSuccess?.(plugin);
  } else {
    const scriptEl = document.createElement('script');
    scriptEl.src = script;
    scriptEl.async = true;
    scriptEl.defer = true;

    const handleError: OnErrorEventHandler = (e) => {
      onError?.(e);
      scriptEl.remove?.();
    };
    scriptEl.addEventListener(
      'load',
      () => {
        // @ts-ignore
        onSuccess?.(window[varName] as Value);
        scriptEl.removeEventListener('error', handleError);
      },
      { once: true }
    );
    scriptEl.addEventListener('error', handleError, { once: true });
    document.head.appendChild(scriptEl);
  }
}

export default appendScript;
