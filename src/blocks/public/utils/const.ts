// 判断手机号正则
export const PHONE_REGEXP = /^1[2-9]\d{9}$/;
// 格式化手机号正则
export const PHONE_FORMAT = /(\d{3})\d{4}(\d{4})/;
// 价格千分位正则
export const PRICE_FORMAT = /\B(?=(\d{3})+(?!\d))/g;
// 证件号正则
export const CERTIFICATE_REGEXP = /^[\da-z]+$/i;
// 判断图片后缀的正则表达式
export const IMAGE_EXT_REGEXP = /\.(a?png|jpe?g|gif|svgz?|ico|bmp|webp|xbm|tif|pjp)$/;
// obs域名地址
export const OBS_DOMAIN = 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/';

// 空方法
export const EMPTY_FN = () => {};

// 是手机端
export const IS_PHONE =
  typeof window !== 'undefined' &&
  /(Android|iPhone|SymbianOS|Windows Phone|iPod)/.test(window.navigator.userAgent);
