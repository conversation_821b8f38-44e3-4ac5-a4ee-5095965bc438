import { createHttpRequest, PaginationResponse } from '@echronos/core';

interface paramsType {
  conditionType: number;
  quarter: number | number[];
}

export interface MemberListType {
  dataId: number; // 数据ID
  dataName: string; // 数据名称
}

function getSelectCondition(params: paramsType): PaginationResponse<MemberListType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getSelectCondition', {
    method: 'GET',
    params,
    autoToast: false,
  });
}

export default getSelectCondition;
