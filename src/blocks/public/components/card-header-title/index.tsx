import { ReactNode, HTMLAttributes } from 'react';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

export type CardHeaderTitleProps = HTMLAttributes<HTMLDivElement> & {
  logo?: string;
  cardName?: string;
  cardNameColor?: string;
  cardNameSize?: number;
  headerRight?: ReactNode | null;
  isSmall?: boolean;
  size?: number;
};

function CardHeaderTitle({
  logo,
  cardName,
  cardNameColor,
  cardNameSize,
  headerRight,
  isSmall,
  size,
  ...props
}: CardHeaderTitleProps) {
  const { t } = useTranslation();
  return (
    <div className={classNames(styles.header, { [styles[`_${size}`]]: isSmall })} {...props}>
      <div className={classNames(styles.headerLeft, styles.clip)}>
        {logo && (
          <img src={logo} alt={cardName ?? t('desk_card_defaultApp')} className={styles.logoImg} />
        )}
        <div
          className={classNames(styles.cardName, styles.clip)}
          style={{ color: cardNameColor, fontSize: cardNameSize }}
          title={cardName ?? t('desk_card_defaultApp')}
        >
          {cardName ?? t('desk_card_defaultApp')}
        </div>
      </div>
      <div className={styles.headerRight}>{headerRight}</div>
    </div>
  );
}

CardHeaderTitle.defaultProps = {
  logo: '',
  cardName: undefined,
  cardNameColor: '#000',
  cardNameSize: '14px',
  headerRight: null,
  isSmall: false,
  size: 150,
};

export default CardHeaderTitle;
