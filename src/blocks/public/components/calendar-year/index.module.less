.calendarYear {
  display: flex;
  width: 100%;
  min-width: 200px;
  height: 100%;
  min-height: 180px;
  background-color: #fff;
  flex-direction: column;
}

.calendarHeadr {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendarHeadrLeft {
  display: flex;
  align-items: center;
}

.calendarHeadrText {
  margin: 1px 10px 0;
}

.leftBtn,
.rightBtn {
  color: #000;
  display: inline-block;
  width: 6px;
  height: 6px;
  position: relative;
  transform: rotate(-45deg);
  cursor: pointer;

  &::after {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    position: absolute;
    top: 3px;
    left: 3px;
    border: 0 solid currentcolor;
    border-width: 1px 0 0 1px;
  }

  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    position: absolute;
    top: 0;
    left: 0;
    border: 0 solid currentcolor;
    border-width: 1px 0 0 1px;
  }
}

.rightBtn {
  transform: rotate(135deg);
}

.calendarHeadrRightBtn {
  cursor: pointer;
}

.calendarBody {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.calendarText {
  color: #040919;
  width: 55px;
  height: 28px;
  line-height: 28px;
  margin: 0 10px;
  border-radius: 14px;
  text-align: center;
  cursor: pointer;

  &:first-child {
    color: #b1b3be;
  }

  &:last-child {
    color: #b1b3be;
  }

  &:hover:not(.selcetDate) {
    background: rgb(217 238 255 / 30%);
  }
}

.selcetDate {
  background-color: #008cff;
  color: #fff;
}

// 小尺寸的卡片样式
.small {
  width: 100%;
  min-width: 112px;
  height: 112px;
  min-height: 112px;
  overflow-y: auto;
  scrollbar-width: none;
}
