.echBlockWrap {
  width: 100%;
  height: 100%;
  position: relative;

  &:hover {
    .isShowEdit {
      display: block;
    }
  }
}

.spinWrap {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.editItemBtn {
  display: none;
  width: 100%;
  height: 40px;
  line-height: 40px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  box-sizing: border-box;
  border-width: 1px 0 0;
  border-style: solid;
  border-color: rgb(255 255 255 / 30%);
  background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
    rgb(255 255 255 / 20%);
  backdrop-filter: blur(30px);
  box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  cursor: pointer;
}

// 小卡片样式
.spinWrapSmall_150 {
  background-position: 10px 10px, center !important;
  background-size: 150px 148px, cover !important;
}
