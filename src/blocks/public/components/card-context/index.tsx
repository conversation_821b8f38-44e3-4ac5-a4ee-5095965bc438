import { useTranslation } from 'react-i18next';
import { ReactNode } from 'react';
import classNames from 'classnames';
import { Spin } from 'antd';
import { IEchBlockCard } from '@/blocks/interface';
import NoPermissionCard from '../no-permission-card';
import styles from './index.module.less';

export type CardContextProps = IEchBlockCard & {
  size?: number;
  isSmall?: boolean;
  spinWrapperClassName?: string;
  loading?: boolean;
  isPermission?: boolean;
  appName?: string;
  cardName?: string;
  logo?: string;
  children?: ReactNode | null;
  isShowEdit?: boolean;
  editClick?: () => void;
};

function CardContext({
  size,
  isSmall,
  spinWrapperClassName,
  loading,
  isPermission,
  appName,
  logo,
  cardName,
  children,
  isShowEdit,
  editClick,
}: CardContextProps) {
  const { t } = useTranslation();

  return (
    <div className={styles.echBlockWrap}>
      <Spin
        spinning={loading}
        wrapperClassName={classNames(styles.spinWrap, spinWrapperClassName, {
          [styles[`spinWrapSmall_${size}`]]: isSmall,
        })}
      >
        {children}
        {!isPermission && (
          <NoPermissionCard
            logo={logo}
            appName={appName}
            cardName={cardName}
            isSmall={isSmall}
            size={size}
          />
        )}
      </Spin>
      {isShowEdit && (
        <div
          className={classNames(styles.editItemBtn, styles.isShowEdit)}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (editClick) editClick();
          }}
        >
          {t('desk_cardContext_edit')}
        </div>
      )}
    </div>
  );
}

CardContext.defaultProps = {
  size: 150,
  isSmall: false,
  spinWrapperClassName: '',
  loading: false,
  isPermission: false,
  appName: '',
  cardName: '',
  logo: '',
  children: null,
  isShowEdit: false,
  editClick: () => {},
};

export default CardContext;
