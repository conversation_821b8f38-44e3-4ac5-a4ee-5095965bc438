import { IEchBlockCard } from '@/blocks/interface';
import { useRef } from 'react';
import classNames from 'classnames';
import styles from './index.module.less';

export type NoDataCardProps = IEchBlockCard & {
  path?: string;
  data?: string;
  msg?: string;
  isSmall?: boolean;
  size?: number;
};
function NoDataCard({ path, data, msg, size, isSmall }: NoDataCardProps) {
  const noDataCardRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={noDataCardRef}
      className={classNames(styles.noDataCard, { [styles[`_${size}`]]: isSmall })}
    >
      <div className={styles.noDataCardContext}>
        <div className={styles.noData}>
          <img className={styles.noDataImg} src={path} alt="" />
          {data}
        </div>
        {msg && <div className={styles.noDataMsg}>{msg}</div>}
      </div>
    </div>
  );
}

NoDataCard.defaultProps = {
  path: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202383/1691050919843370.png',
  data: '暂无数据',
  msg: '',
  isSmall: false,
  size: 150,
};

export default NoDataCard;
