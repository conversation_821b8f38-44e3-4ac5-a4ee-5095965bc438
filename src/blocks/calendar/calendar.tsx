import { Calendar } from 'antd';
import { memo, useRef } from 'react';
import { useMemoizedFn } from 'ahooks';
import { isFunction } from 'lodash';
import { IEchBlockCard } from '@/blocks/interface';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import Left from './icon/left';
import Right from './icon/right';
import style from './calendar.module.less';
import './mobile.less';

export type CalendarCpProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};

function CalendarCp({ appName, logo, navigate }: CalendarCpProps) {
  // 多语言导入
  const { t } = useTranslation();
  // 获取当前卡片的DOM元素
  const echBlockmCalendarRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockmCalendarRef, 150);

  const JumpAppFn = () => {
    if (isFunction(navigate)) {
      navigate('/user/calendar/home');
    }
  };
  const headerRender = useMemoizedFn(({ value, onChange }) => (
    <div className={style.header}>
      <div className={style.headerLeft}>
        <div
          className={style.calendarIcon}
          style={{ backgroundImage: logo ? `url(${logo})` : undefined }}
        />
        <span className={style.title}>{appName ?? t('desk_apps_calendar')}</span>
      </div>
      <div className={style.changetime}>
        <Left
          className={style.changeIcon}
          onClick={(e) => {
            e.stopPropagation();
            const now = value.clone().month(value.$M - 1);
            onChange(now);
          }}
        />
        {value.$M + 1}
        {t('desk_apps_mon')}
        <Right
          className={style.changeIcon}
          onClick={(e) => {
            e.stopPropagation();
            const now = value.clone().month(value.$M + 1);
            onChange(now);
          }}
        />
      </div>
    </div>
  ));

  return (
    <div
      ref={echBlockmCalendarRef}
      className={classNames(style.echBlockmCalendar, { [style.small]: isSmall })}
      role="button"
      tabIndex={0}
      onClick={(e) => {
        e.stopPropagation();
        JumpAppFn();
      }}
    >
      <Calendar fullscreen={false} headerRender={headerRender} />
    </div>
  );
}

CalendarCp.defaultProps = {
  // appName: '日历日程',
  appName: undefined,
  logo: '',
};

export default memo(CalendarCp, () => true);
