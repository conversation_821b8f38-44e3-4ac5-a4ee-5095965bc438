import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const rightIcon: IconComponentProps['icon'] = {
  name: 'right',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'm10.031 18.528 6-6c.29-.29.29-.77 0-1.06l-6-6a.743.743 0 0 0-1.02 0c-.15.14-.23.33-.24.52-.01.2.06.4.2.54l5.47 5.47-5.47 5.47a.75.75 0 1 0 1.06 1.06Z',
        },
      },
    ],
  },
};

function Right(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={rightIcon} />;
}

Right.displayName = 'EchRightIcon';

export default Right;
