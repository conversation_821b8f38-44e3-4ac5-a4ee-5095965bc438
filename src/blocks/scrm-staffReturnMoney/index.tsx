import { Tooltip } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import Icon from '@echronos/echos-icon';
import classNames from 'classnames';
import useElSize from '@/hooks/use-el-size';
import { useTranslation } from 'react-i18next';
import SelectionCondition, {
  getCurrentQuarter,
} from '@/blocks/public/components/selection-condition';
import { GetMemberRefundCard, MemberRefundQuarterType, postQueryConditionSave } from './api';
import { IEchBlockCard } from '../interface';
import './index.less';

function StaffReturnMoney({ navigate, blockId }: IEchBlockCard) {
  const { t } = useTranslation();
  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子

  const [quarter, setQuarter] = useState<number[]>(getCurrentQuarter([])); // 选中的季度项

  const [memberId, setMemberId] = useState<number[]>([]); // 选择的成员

  const [memberRefundData, setMemberRefundData] = useState<MemberRefundQuarterType>({
    deptStatisticsQuarter: getCurrentQuarter([]),
    userStatisticsList: [],
    memberList: [],
    targetMoney: '',
    returnedMoney: { money: '', changeMoney: '', changeType: 0 },
  });

  // 获取当前卡片的DOM元素
  const echBlockScrmStaffReturnMoneyRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmStaffReturnMoneyRef, 150);

  // 鼠标移入移出——编辑按钮显隐
  const handleMouseEnter = () => {
    setEditBtnShow(true);
  };
  const handleMouseLeave = () => {
    setEditBtnShow(false);
  };

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${memberRefundData.deptStatisticsQuarter[0]}`);
    }
  };

  // 获取卡片的信息
  const getQuarterMenberReturnMoney = useCallback(() => {
    GetMemberRefundCard(blockId?.toString() || '').then((res: MemberRefundQuarterType) => {
      if (!res.deptStatisticsQuarter || !res.userStatisticsList.length) {
        setEditBtnShow(true);
        return;
      }
      setMemberRefundData(res);
      setQuarter(res.deptStatisticsQuarter);
      setMemberId(res.userStatisticsList);
      setEditBoxShow(false);
    });
  }, [blockId]);

  const priceNode = (e: string) => {
    if (e.length > 9) {
      return (
        <Tooltip title={e}>
          <span className="moneyNum reduce">{e}</span>
        </Tooltip>
      );
    }
    if (e.length > 7 && e.length <= 9) {
      return <span className="moneyNum reduce">{e}</span>;
    }
    return <span className="moneyNum">{e}</span>;
  };

  // 完成
  const handleSuccess = (quarters: number[], deptIds: number[]) => {
    postQueryConditionSave(quarters, deptIds, blockId?.toString() || '').finally(() =>
      getQuarterMenberReturnMoney()
    );
  };

  useEffect(() => {
    getQuarterMenberReturnMoney();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      ref={echBlockScrmStaffReturnMoneyRef}
      className={classNames('ech-block-scrm-staffReturnMoney', { small: isSmall })}
      role="presentation"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onJumpPage}
    >
      {!editBoxShow ? (
        <>
          <div className="headBox">
            <img
              src={
                (memberRefundData.memberList.length && memberRefundData.memberList[0].avatar) ||
                `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722678121403974.png`
              }
              alt=""
              className="headimg"
            />
            {memberRefundData.memberList[0]?.memberName?.length > 12 ? (
              <Tooltip title={memberRefundData.memberList[0]?.memberName}>
                {memberRefundData.memberList[0]?.memberName || '--'}
              </Tooltip>
            ) : (
              <span className="headName">{memberRefundData.memberList[0]?.memberName || '--'}</span>
            )}
          </div>
          <div className="returnMoneyBox">
            <div className="returnMoney">
              <span>
                {t('desk_scrmStaffReturnMoney_quarterTarget', {
                  quarter: memberRefundData.deptStatisticsQuarter[0],
                })}
              </span>
              <div className="priceBox">
                <div className="price">
                  {priceNode(memberRefundData.targetMoney || '--')}
                  <span className="unit">{t('desk_scrmStaffReturnMoney_unit')}</span>
                </div>
              </div>
            </div>
            <div className="returnMoney extimate">
              <span>
                {t('desk_scrmStaffReturnMoney_quarterExpected', {
                  quarter: memberRefundData.deptStatisticsQuarter[0],
                })}
              </span>
              <div className="priceBox bottom">
                <div className="price">
                  {priceNode(memberRefundData.returnedMoney?.money || '--')}
                  <span className="unit">{t('desk_scrmStaffReturnMoney_unit')}</span>
                </div>
                <div
                  className={classNames('contrastBox', {
                    rise: memberRefundData.returnedMoney?.changeType === 1,
                    lower: memberRefundData.returnedMoney?.changeType === 2,
                  })}
                >
                  <span className="unchanged">
                    {t('desk_scrmStaffReturnMoney_compareLastWeek')}
                  </span>
                  <span
                    className={classNames('contrastPrice', {
                      unchanged: memberRefundData.returnedMoney?.changeType === 0,
                    })}
                  >
                    {memberRefundData.returnedMoney?.changeType === 0
                      ? t('desk_scrmStaffReturnMoney_noChange')
                      : t('desk_scrmStaffReturnMoney_changeAmount', {
                          amount: memberRefundData.returnedMoney?.changeMoney,
                        })}
                  </span>
                  {memberRefundData.returnedMoney?.changeType !== 0 ? (
                    <div
                      className={classNames('iconBox', {
                        upArrow: memberRefundData.returnedMoney?.changeType === 1,
                        downArrow: memberRefundData.returnedMoney?.changeType === 2,
                      })}
                    >
                      <Icon
                        name={
                          memberRefundData.returnedMoney?.changeType === 1
                            ? 'up_arrow'
                            : 'down_arrow'
                        }
                        size={8}
                        className={classNames('iconArrow', {
                          upArrow: memberRefundData.returnedMoney?.changeType === 1,
                          downArrow: memberRefundData.returnedMoney?.changeType === 2,
                        })}
                      />
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>

          {/* 编辑按钮 */}
          {editBtnShow && (
            <div
              className="editBtn"
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setEditBoxShow(true);
              }}
            >
              {t('desk_scrmStaffReturnMoney_edit')}
            </div>
          )}
        </>
      ) : (
        <SelectionCondition
          size={echBlockScrmStaffReturnMoneyRef.current?.offsetWidth}
          isSmall={isSmall}
          className="editBox"
          memberName={t('desk_scrmStaffReturnMoney_staff')}
          radioQuarter
          visible={editBoxShow}
          quartered={quarter}
          memberType={2}
          radioMember
          memberIded={memberId}
          onSuccess={(quarters, deptIds) => {
            handleSuccess(quarters, deptIds || []);
          }}
        />
      )}
    </div>
  );
}

export default StaffReturnMoney;
