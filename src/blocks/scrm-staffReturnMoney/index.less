.ech-block-scrm-staffReturnMoney {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 8px 12px;
  position: relative;
  flex-direction: column;
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(25px);
  border-radius: 18px;
  font-family: '苹方-简', sans-serif;
  cursor: pointer;

  .headBox {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 22px;
    align-items: center;

    .headName {
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
    }

    .headimg {
      width: 22px;
      height: 22px;
      margin-right: 6px;
      border-radius: 50%;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 10%);
    }
  }

  .returnMoneyBox {
    display: flex;
    height: 70%;
    flex-direction: column;
    margin-top: 30px;
    justify-content: space-between;
  }

  .returnMoney {
    color: #888b98;
    font-size: 14px;
    display: flex;
    min-height: 66px;
    justify-content: space-between;
    flex-direction: column;
  }

  .priceBox {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }

  .bottom {
    flex-direction: column;
    align-items: flex-start;
  }

  .price {
    color: #040919;
    font-size: 40px;
    font-weight: bold;
    display: flex;
    width: 100%;
    align-items: flex-end;

    .moneyNum {
      line-height: 1;
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
    }

    .reduce {
      font-size: 24px;
    }

    .unit {
      font-size: 12px;
      font-weight: normal;
      min-width: fit-content;
      margin-left: 6px;
    }
  }

  .contrastBox {
    color: #05d380;
    font-size: 12px;
    display: flex;
    line-height: 1;
    align-items: center;
    margin-top: 8px;

    .unchanged {
      color: #888b98;
    }

    .contrastPrice {
      font-weight: 500;
      margin: 0 4px;
    }

    .iconBox {
      display: flex;
      width: 12px;
      height: 12px;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
    }

    .iconArrow {
      font-weight: bold;
    }

    // 箭头颜色+背景颜色
    .upArrow {
      background: #d0efe2;
    }

    .downArrow {
      background: #fcdddf;
    }
  }

  // 字体颜色
  .rise {
    color: #05d380;
  }

  .lower {
    color: #ea1c26;
  }

  .editBtn {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  }
}

.small {
  &.ech-block-scrm-staffReturnMoney {
    .headBox {
      font-size: 10px;
      height: 22px;
      line-height: 22px;

      .headimg {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }

    .returnMoneyBox {
      height: 72%;
      margin-top: 10px;
    }

    .returnMoney {
      font-size: 10px;
      min-height: 40px;
    }

    .price {
      font-size: 24px;
      margin-top: 4px;

      .unit {
        font-size: 10px;
      }
    }

    .contrastBox {
      font-size: 10px;
    }

    .editBtn {
      font-size: 12px;
      height: 32px;
    }
  }
}
