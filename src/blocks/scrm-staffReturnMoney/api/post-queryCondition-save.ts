import { createHttpRequest } from '@echronos/core';

/**
 * 员工季度预计回款--季度单选条件,卡片查询条件保存
 */

function postQueryConditionSave(quarterList: number[], userList: number[], blockId: string) {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/queryCondition/save', {
    method: 'POST',
    data: {
      type: 3,
      quarterList,
      userList, // 员工季度预计回款--成员用户ID多选条件[userId]
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default postQueryConditionSave;
