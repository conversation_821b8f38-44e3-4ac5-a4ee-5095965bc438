import { createHttpRequest } from '@echronos/core';

interface ReturnedMoneyType {
  money: string; // 金额【单位：万元】
  goalRate?: string; // 占目标倍率
  holidayMoney?: string; // 历史对比金额【单位：万元】
  changeMoney: string; // 变化金额【单位：万元】
  changeType: number; // 变化类型 0-没变化，1-新增，2-减少
}

interface MemberDataType {
  memberId: number; // 成员id
  memberName: string; // 成员名称
  userId?: number; // 用户Id
  companyId?: number; // 公司id
  avatar: string; // 成员头像
  monthList?: string[]; // 统计月列表
}

export interface MemberRefundQuarterType {
  deptStatisticsQuarter: number[]; // 指定员工回款数据--季度单选条件
  userStatisticsList: number[]; // 指定员工回款数据--员工多选条件[userId]
  memberList: MemberDataType[]; // 成员列表
  targetMoney: string; // 目标金额
  returnedMoney: ReturnedMoneyType; // 成员商机回款金额总计;
}

function GetMemberRefundCard(blockId: string): Promise<MemberRefundQuarterType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getMemberStatisticsCard', {
    method: 'GET',
    params: {
      cardUuid: blockId,
    },
    autoToast: false,
  });
}

export default GetMemberRefundCard;
