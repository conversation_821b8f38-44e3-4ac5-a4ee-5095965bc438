import { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import useElSize from '@/hooks/use-el-size';
import { useTranslation } from 'react-i18next';
import { IEchBlockCard } from '../interface';
import CardContext from '../public/components/card-context';
import getOpportunityTotalAmount from './api/get-opportunity-total-amount';
import styles from './index.module.less';

function ScrmOpportunityTotalAmount({ navigate, blockId }: IEchBlockCard) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [totalAmount, setTotalAmount] = useState('');

  // 获取当前卡片的DOM元素
  const echBlockScrmOpportunityTotalAmountRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmOpportunityTotalAmountRef, 150);

  // 价格单位换算
  const priceUnit = (price: number) => {
    if (!price) return { price: '--', unit: t('desk_scrmOpportunity_yuan') };

    if (price >= 100000000) {
      return {
        price: (Number(price) / 100000000).toFixed(2),
        unit: t('desk_scrmOpportunity_hundredMillion'),
      };
    }
    if (price >= 10000) {
      return {
        price: (Number(price) / 10000).toFixed(2),
        unit: t('desk_scrmOpportunity_tenThousand'),
      };
    }
    return { price: Number(price).toFixed(2), unit: t('desk_scrmOpportunity_yuan') };
  };

  // 解构（价格，单位）
  const { price, unit } = useMemo(() => priceUnit(Number(totalAmount)), [totalAmount]); // eslint-disable-line

  useEffect(() => {
    if (blockId) {
      setLoading(true);
      getOpportunityTotalAmount(blockId)
        .then((res) => {
          setTotalAmount(res.opportunityTotalAmount);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [blockId]);

  return (
    <CardContext
      size={echBlockScrmOpportunityTotalAmountRef.current?.offsetWidth}
      isSmall={isSmall}
      loading={loading}
      isPermission
      spinWrapperClassName={styles.spinWrapper}
    >
      <div
        ref={echBlockScrmOpportunityTotalAmountRef}
        role="button"
        tabIndex={0}
        onClick={(e) => {
          e.stopPropagation();
          if (navigate) {
            navigate('/scrm/client/business-opportunity');
          }
        }}
        className={classNames(styles.scrm_opportunity_total_amount, { [styles.small]: isSmall })}
      >
        <div
          className={classNames(styles.numbers, {
            [styles.smallNum]: totalAmount.length > 8,
          })}
          title={price}
        >
          {price}
        </div>
        <div className={styles.labelName}>{t('desk_scrmOpportunity_totalAmount', { unit })}</div>
      </div>
    </CardContext>
  );
}

export default ScrmOpportunityTotalAmount;
