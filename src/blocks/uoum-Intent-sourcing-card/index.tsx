import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import useElSize from '@/hooks/use-el-size';
import type { ProcurementNtionProp } from '../uoum-card/api/get-uoum-procurement-notice';
import { getUoumProcurementNotice } from '../uoum-card/api/get-uoum-procurement-notice';
import Cart from '../uoum-card/components/cart-title';
import CartContent from '../uoum-card/components/cart-content';

function UoumIntentSourcingCard() {
  const { t } = useTranslation();
  // 获取当前卡片的DOM元素
  const uoumIntentSourcingCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(uoumIntentSourcingCardRef, 324);
  // 获取元素的宽度
  const domWidth = uoumIntentSourcingCardRef.current?.offsetWidth;

  const [intentData, setIntentData] = useState<ProcurementNtionProp[]>([]);
  // 测试环境站点id
  const { run, loading } = useRequest(
    () =>
      // categoryIdList
      getUoumProcurementNotice({
        pageNo: 1,
        pageSize: 2,
        siteId: +import.meta.env.BIZ_UM_VITE_SITE_ID,
        trendsTypeList: [5],
      }),
    {
      manual: true,
      onSuccess: (res) => {
        setIntentData(res.list);
      },
    }
  );
  useEffect(() => {
    run();
  }, [run]);
  return (
    <div ref={uoumIntentSourcingCardRef} style={{ width: '100%', height: '100%' }}>
      {!loading && (
        <Cart
          type="IntentSource"
          url="bidding/5"
          title={t('desk_uoumIntent_title')}
          isSmall={isSmall}
          size={domWidth}
        >
          <CartContent
            type="source"
            data={intentData}
            url="bidding/5"
            isSmall={isSmall}
            size={domWidth}
          />
        </Cart>
      )}
    </div>
  );
}

export default UoumIntentSourcingCard;
