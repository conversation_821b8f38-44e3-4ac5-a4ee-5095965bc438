.outSide {
  :global {
    display: flex;
    width: 100%;
    height: 100%;
    // border: 1px solid black;
    padding: 10px;
    justify-content: space-between;
    position: relative;
    border-radius: 18px;
    background: rgb(255 255 255 / 70%);
    backdrop-filter: blur(25px);
    user-select: none;
  }
}

.hidden {
  background: transparent;
  backdrop-filter: blur(0);
}

.insideTop {
  :global {
    display: flex;
    width: 100%;
    height: 50px;
    justify-content: space-between;
    align-items: center;
  }
}

.insideLeft {
  :global {
    display: flex;
    width: 100px;
    height: 100%;
    // justify-content: space-around;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .logo {
    width: 66px;
    height: 66px;
  }
}

.insideRight {
  :global {
    display: flex;
    flex-direction: column;
    width: calc(100% - 110px);
    justify-content: space-around;
  }
}

.more {
  :global {
    cursor: pointer;
  }
}

.procurementName {
  :global {
    color: #888b98;
    width: 84px;
    height: 44px;
    margin-top: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }
}

.stepItem {
  display: flex;
  flex-direction: column;
  height: 110px;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 8px;
}

.companyName {
  margin-left: 7px;
}

.insideBottom {
  :global {
    display: flex;
    width: 100%;
    height: 55px;
    background: #fff;
    border-radius: 8px;
  }
}

.flowName {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.progress {
  display: flex;
  width: 100%;
  justify-content: center;
  position: relative;
  align-items: center;
}

.progressLine {
  width: 100%;
  position: absolute;
  border: none;
  border-top: 1px solid #ccc;
  left: 50%;
}

.planStatus1,
.planStatus2,
.planStatus3,
.planStatus4,
.planStatus5 {
  :global {
    display: block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    cursor: pointer;
  }
}

.planStatus1 {
  background: linear-gradient(180deg, #23f09d 0%, #**********%);
}

.planStatus2 {
  background: linear-gradient(180deg, #ffd16a 0%, #e07400 100%);
}

.planStatus3 {
  background: linear-gradient(180deg, #ffb2b2 0%, #e00000 100%);
}

.planStatus4 {
  background: linear-gradient(180deg, #f1f1f1 0%, #bfbfbf 100%);
}

.planStatus5 {
  background: linear-gradient(180deg, #bcbcbc 0%, #1e1e1e 100%);
}

.progressItem {
  :global {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 50px;
    flex: 1;
    justify-content: space-between;
  }
}

.editBtn {
  :global {
    display: none;
    width: 100%;
    height: 40px;
    line-height: 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    cursor: pointer;
    border-radius: 0 0 18px 18px;
    text-align: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    backdrop-filter: blur(30px);
  }
}

.outSide:hover .editBtn {
  :global {
    display: block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1000;
    cursor: pointer;
    border-radius: 0 0 18px 18px;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    text-align: center;
  }
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}

@media (width <=1105px) {
  .ant-modal {
    :global {
      max-width: calc(100vw - 16px);
      margin: 0;
    }
  }
}

.small {
  &.outSide {
    padding: 6px 10px;

    .insideLeft {
      width: 90px;

      .logo {
        width: 55px;
        height: 55px;
      }

      .title,
      .more,
      .procurementName {
        font-size: 10px;
      }

      .procurementName {
        height: 32px;
      }
    }

    .insideRight {
      width: calc(100% - 90px);

      .stepItem {
        height: 68px;
        padding: 2px 0;
      }

      .companyName {
        font-size: 8px;
      }

      .insideBottom {
        height: 40px;
      }

      .flowName {
        font-size: 8px;
      }

      .progressItem {
        min-width: 35px;
        height: 38px;
      }

      .planStatus1,
      .planStatus2,
      .planStatus3,
      .planStatus4,
      .planStatus5 {
        :global {
          width: 12px;
          height: 12px;
        }
      }
    }

    .noData {
      font-size: 10px;
    }
  }

  &.outSide:hover .editBtn {
    font-size: 12px;
    height: 36px;
    line-height: 36px;
  }
}
