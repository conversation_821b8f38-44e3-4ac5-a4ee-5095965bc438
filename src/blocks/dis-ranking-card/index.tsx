import { useState, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import { message } from 'antd';
import { isFunction } from 'lodash';
import { getOrderDisRankingList } from '@/blocks/public/apis';
import type { GetOrderDisRankingListOption } from '@/blocks/public/apis';
import { IEchBlockCard } from '@/blocks/interface';
import { formatPrice } from '@/blocks/public/utils/utils';
import useElSize from '@/hooks/use-el-size';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import CardContext from '../public/components/card-context';
import styles from './index.module.less';

export type DisRankingCardProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};

function DisRankingCard({ appName, logo, navigate }: DisRankingCardProps) {
  const { t } = useTranslation();
  const rankImages = [
    `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722657052788254.png`,
    `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722657057193688.png`,
    `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722657060054722.png`,
  ];
  const [rankingData, setRankingData] = useState<GetOrderDisRankingListOption[]>([]);
  const [isPermission, setIsPermission] = useState(true);
  const [showLoading, setShowLoading] = useState(false);

  // 获取当前卡片的DOM元素
  const echBlockDisRankingCardRef = useRef<HTMLDivElement>(null);

  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockDisRankingCardRef, 324);

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const getMonthTimeStamp = () => {
    const firstDayOfMonthTimestampMs = dayjs().startOf('month').valueOf();
    const lastDayOfMonthTimestampMs = dayjs().endOf('month').valueOf();
    return {
      start: firstDayOfMonthTimestampMs,
      end: lastDayOfMonthTimestampMs,
    };
  };

  const getDisRankData = () => {
    const monthDate = getMonthTimeStamp();
    setShowLoading(true);
    getOrderDisRankingList({
      statisticType: 4,
      sortType: 1,
      pageNo: 1,
      pageSize: 3,
      startDate: monthDate.start || 0,
      endDate: monthDate.end || 0,
      ifError: 1,
    })
      .then((res) => {
        setRankingData(res.list);
      })
      .catch((err) => {
        if (err.code === 5) {
          setIsPermission(false);
        } else {
          message.error(err.message);
        }
      })
      .finally(() => {
        setShowLoading(false);
      });
  };

  const onToRank = () => {
    onNavTo(`/distribution/home?desktopCardOperateType=openRankPopup`);
  };

  useEffect(() => {
    getDisRankData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={showLoading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName ?? t('desk_disCheck_defaultName')}
      cardName={t('desk_disRanking_title')}
    >
      <div
        ref={echBlockDisRankingCardRef}
        className={classNames(styles.DisRankingCard, { [styles.small]: isSmall })}
      >
        <div className={styles.title}>
          <img alt="" src={logo} className={styles.titleLogo} />
          <span className={styles.titleName}>{appName}</span>
          <span className={styles.titleName}>{t('desk_disRanking_title')}</span>
        </div>
        {rankingData.length > 0 && (
          <div className={styles.content}>
            {rankingData?.map((item, index) => (
              <div
                className={styles.rankItem}
                key={item.companyId}
                role="presentation"
                onClick={onToRank}
              >
                {index < 3 && (
                  <img src={rankImages[index]} alt="" className={styles.rankItemImage} />
                )}
                <div className={styles.rankItemName} title={item.companyName}>
                  {item.companyName}
                </div>
                <div className={styles.rankItemPrice} title={`￥${formatPrice(item.totalAmount)}`}>
                  ￥{formatPrice(item.totalAmount)}
                </div>
              </div>
            ))}
          </div>
        )}

        {!rankingData.length && <div className={styles.empty}>{t('desk_disRanking_noData')}</div>}
      </div>
    </CardContext>
  );
}

DisRankingCard.defaultProps = {
  // appName: '藤蔓分销',
  appName: undefined,
  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img//1697441685114984095.png`,
};

export default DisRankingCard;
