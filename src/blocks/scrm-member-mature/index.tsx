import { useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';

import { IEchBlockCard } from '@/blocks/interface';
import useElSize from '@/hooks/use-el-size';
import SelectionCondition, {
  getCurrentQuarter,
} from '@/blocks/public/components/selection-condition';
import styles from './index.module.less';
import CardContext from '../public/components/card-context';
import getMemberMatureCard, {
  MemberMatureCardType,
  MemberInfoType,
} from './api/get-member-mature-card';
import postQueryConditionSave from './api/post-queryCondition-save';

function ScrmMemberMature({ navigate, blockId }: IEchBlockCard) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  const [editBtnShow, setEditBtnShow] = useState(false); // 编辑按钮
  const [editBoxShow, setEditBoxShow] = useState(false); // 编辑盒子
  const [maskShow, setMaskShow] = useState(false); // 蒙版显隐

  const [quarter, setQuarter] = useState<number[]>(getCurrentQuarter([])); // 季度

  const [memberId, setMemberId] = useState<number[]>([]); // 成员id
  const [memberInfoList, setMemberInfoList] = useState<MemberInfoType[] | null>(null); // 成员信息列表

  const [memberRefundData, setMemberRefundData] = useState<MemberMatureCardType | null>(null); // 卡片数据

  // 获取当前卡片的DOM元素
  const echBlockScrmMemberMatureRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmMemberMatureRef, 150);

  // tag的颜色
  const tagColorClass = (type: number, goalRate: number) => {
    // goalRate 占目标倍率
    // type 1-商机总金额 2-成熟商机总金额
    if (goalRate < 1) {
      return styles.tagRed;
    }
    if ((type === 1 && goalRate < 1.2) || (type === 2 && goalRate < 4)) {
      return styles.tagYellow;
    }
    return styles.tagGreen;
  };

  const handleMouseEnter = () => {
    if (maskShow) return;
    setEditBtnShow(true);
  };

  const handleMouseLeave = () => {
    if (maskShow) return;
    setEditBtnShow(false);
  };

  const getMemberMatureData = () => {
    getMemberMatureCard(blockId?.toString() || '')
      .then((res: MemberMatureCardType) => {
        if (!res.deptStatisticsQuarter.length || !res.userStatisticsList.length) {
          setMaskShow(true);
          return;
        }
        // 保存当前查询条件
        setMemberRefundData(res);
        setMemberInfoList(res.memberList);
        setQuarter(res.deptStatisticsQuarter);
        setMemberId(res.userStatisticsList);
        setEditBoxShow(false);
      })
      .finally(() => {
        setLoading(false);
        setEditBoxShow(false);
      });
  };

  // 完成
  const handleSuccess = (quarters: number[], memberIds: number[]) => {
    setLoading(true);
    postQueryConditionSave(quarters, memberIds, blockId?.toString() || '').finally(() =>
      getMemberMatureData()
    );
  };

  // 首次渲染时获取卡片数据
  useEffect(() => {
    getMemberMatureData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 跳转进入数据分析页面
  const onJumpPage = () => {
    if (maskShow) return;
    if (navigate) {
      navigate(`/scrm/client/report-forms?quarter=${memberRefundData?.deptStatisticsQuarter}`);
    }
  };

  return (
    <CardContext loading={loading} isPermission spinWrapperClassName={styles.spinWrap}>
      <div
        ref={echBlockScrmMemberMatureRef}
        className={classNames(styles.echBlockScrmMemberMature, { [styles.small]: isSmall })}
        role="button"
        tabIndex={0}
        onClick={onJumpPage}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {!editBoxShow ? (
          <>
            {/* 员工logo+员工名称 */}
            <div className={styles.headBox}>
              <img
                src={
                  memberInfoList?.length
                    ? memberInfoList[0].avatar
                    : `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1723186307558261.png`
                }
                alt=""
                className={styles.headImg}
              />
              {memberInfoList && memberInfoList[0].memberName.length > 12 ? (
                <Tooltip title={memberInfoList[0]?.memberName}>
                  {memberInfoList[0]?.memberName || '--'}
                </Tooltip>
              ) : (
                <span className={styles.headName}>
                  {(memberInfoList && memberInfoList[0]?.memberName) || '--'}
                </span>
              )}
            </div>

            {/* 内容 */}
            <div className={styles.returnMoneyBox}>
              <div className={styles.totalAmount}>
                <div className={styles.money}>
                  {t('desk_scrmMemberMature_quarterOpportunity', { quarter: quarter[0] })}
                </div>
                <div
                  className={classNames(
                    styles.goalRate,
                    tagColorClass(2, Number(memberRefundData?.opportunityMoney.goalRate))
                  )}
                >
                  {memberRefundData?.opportunityMoney.goalRate}
                </div>
              </div>
              <div className={styles.matureTotalAmount}>
                <div className={styles.money}>{t('desk_scrmMemberMature_matureOpportunity')}</div>
                <div
                  className={classNames(
                    styles.goalRate,
                    tagColorClass(1, Number(memberRefundData?.matureOpportunityMoney.goalRate))
                  )}
                >
                  {memberRefundData?.matureOpportunityMoney.goalRate}
                </div>
              </div>
            </div>

            {/* 编辑按钮 */}
            {editBtnShow && (
              <div
                className={styles.editBtn}
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  setEditBoxShow(true);
                }}
              >
                {t('desk_scrmMemberMature_edit')}
              </div>
            )}
          </>
        ) : (
          <SelectionCondition
            size={echBlockScrmMemberMatureRef.current?.offsetWidth}
            isSmall={isSmall}
            className={styles.editBox}
            memberName={t('desk_scrmMemberMature_member')}
            radioQuarter
            visible={editBoxShow}
            quartered={quarter}
            memberType={2}
            radioMember
            memberIded={memberId}
            onSuccess={(quarters, memberIds) => {
              handleSuccess(quarters, memberIds || []);
            }}
          />
        )}
        {/* 蒙版 */}
        {maskShow && (
          <div className={styles.mask}>
            <div className={styles.maskTitle}>{t('desk_scrmMemberMature_coverageRate')}</div>
            <div
              className={styles.maskBtn}
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setEditBoxShow(true);
                setMaskShow(false);
              }}
            >
              {t('desk_scrmMemberMature_setConditions')}
            </div>
          </div>
        )}
      </div>
    </CardContext>
  );
}
export default ScrmMemberMature;
