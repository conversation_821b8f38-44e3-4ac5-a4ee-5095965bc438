.echBlockSystemCard {
  color: #040919;
  width: 100%;
  height: 100%;
  position: relative;
  font-family: '苹方-简', sans-serif;
}

.echBlockSystemCardHeader {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  padding: 12px 0;
  background: linear-gradient(180deg, #fff 0%, #ebebeb 100%);
}

.echBlockSystemCardContent {
  min-height: 206px;
  padding: 14px;
  background-color: #fff;

  .echBlockSystemCardItem {
    display: flex;
    align-items: center;
    margin-bottom: 14px;

    span {
      color: #888b98;
      font-family: '阿里巴巴普惠体 3.0', sans-serif;
      font-size: 16px;
      font-weight: 700;
      font-variation-settings: 'opsz' auto;
    }

    &:nth-of-type(1) {
      span {
        color: #ea1c26;
      }
    }

    &:nth-of-type(2) {
      span {
        color: #f9ae08;
      }
    }

    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      margin-left: 8px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.small {
  .echBlockSystemCardHeader {
    font-size: 10px;
    padding: 8px 0;
  }

  .echBlockSystemCardContent {
    min-height: 206px;
    padding: 6px 12px;

    .echBlockSystemCardItem {
      margin-bottom: 2px;

      span {
        font-size: 8px;
      }

      p {
        font-size: 8px;
        margin-left: 6px;
      }
    }
  }
}
