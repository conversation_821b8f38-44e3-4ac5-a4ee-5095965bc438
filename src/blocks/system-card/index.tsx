import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import NoDataCard from '@/blocks/public/components/no-data-card';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import { IEchBlockCard } from '../interface';
import styles from './index.module.less';
import getSystemCardInfo, { SystemListType } from './apis';

function SystemCard({ navigate }: IEchBlockCard) {
  const { t } = useTranslation();
  // 制度列表
  const [systemList, setSystemList] = useState<SystemListType[]>([]);
  // 获取当前卡片的DOM元素
  const echBlockSystemCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockSystemCardRef, 150);
  // 卡片尺寸
  const [size, setSize] = useState<number>();

  // 链接跳转
  const handleNavigate = (id: number) => {
    if (navigate) {
      navigate(`/institution/detail/${id}`);
    }
  };

  useEffect(() => {
    getSystemCardInfo({}).then((res) => {
      setSystemList(res.list);
      setSize(echBlockSystemCardRef.current?.offsetWidth);
    });
  }, []);

  return (
    <div
      ref={echBlockSystemCardRef}
      className={classNames(styles.echBlockSystemCard, { [styles.small]: isSmall })}
      role="presentation"
      onClick={() => {
        if (navigate) {
          navigate(`/institution`);
        }
      }}
    >
      <div className={styles.echBlockSystemCardHeader}>{t('desk_systemCard_title')}</div>
      <ul className={styles.echBlockSystemCardContent}>
        {systemList.length ? (
          systemList.slice(0, 5).map((item, index) => (
            <li
              className={styles.echBlockSystemCardItem}
              key={item.id}
              role="presentation"
              onClick={(e) => {
                e.stopPropagation();
                handleNavigate(item.id);
              }}
            >
              <span>{index + 1}</span>
              <p>{item.title}</p>
            </li>
          ))
        ) : (
          <NoDataCard isSmall={isSmall} size={size} />
        )}
      </ul>
    </div>
  );
}

export default SystemCard;
