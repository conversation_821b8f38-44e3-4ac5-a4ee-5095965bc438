.financeUpstreamDownstream {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 12px;
  background-image: url('./images/finance-icon-two.png');
  background-size: cover;
}

.body {
  flex: 1;
  display: flex;
  justify-content: space-around;
}

.bodyLeft {
  display: flex;
  min-width: 90px;
  justify-content: flex-end;
  flex-direction: column;
}

.titleTop {
  color: #fff;
  font-size: 14px;
  opacity: 0.7;
}

.titleButton {
  color: #fff;
  font-size: 12px;
}

.bodyRight {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.bodyRightItem {
  display: flex;
  padding: 6px;
  align-items: center;
  background-color: rgb(255 255 255 / 10%);
  border-radius: 8px;
}

.bodyRightItemRight {
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.price {
  font-size: 12px;
}

.title {
  font-size: 10px;
}

.small {
  &.financeUpstreamDownstream {
    padding: 8px;

    .bodyLeft {
      min-width: 42px;
    }

    .titleTop {
      font-size: 8px;
    }

    .titleButton {
      font-size: 6px;
    }

    .bodyRightItem {
      padding: 3px;

      img {
        width: 14px !important;
        height: 14px !important;
        margin-right: 4px !important;
      }
    }

    .price {
      font-size: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }

    .title {
      font-size: 6px;
    }
  }
}
