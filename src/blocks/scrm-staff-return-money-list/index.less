.ech-block-scrm-staff-return-money-list {
  color: #fff;
  width: 100%;
  height: 100%;
  position: relative;
  font-family: '苹方-简', sans-serif;
  cursor: pointer;

  .container {
    display: flex;
    width: 100%;
    height: 100%;
    padding: 12px;
    background: rgb(255 255 255 / 70%);
    backdrop-filter: blur(25px);
    overflow: hidden;
    flex-direction: column;
  }

  .dis {
    background: none;
    backdrop-filter: blur(0);
  }

  .headBox {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 22px;
    align-items: center;

    .imgBox {
      width: 22px;
      height: 22px;
      margin-right: 6px;
      border-radius: 50%;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 10%);
    }

    .headName {
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 显示省略号 */
    }
  }

  //
  .table {
    color: black;
    display: flex;
    margin-top: 6px;
    overflow: hidden;
    flex: 1;
    flex-direction: column;
  }

  .tableHead {
    color: #888b98;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    padding: 8px 16px;
    border-radius: 13px;
    background: rgba(198 204 216 / 30%);
    backdrop-filter: blur(19px);
  }

  .chanceAndPhase {
    display: flex;
    width: 39%;
    justify-content: left;
    align-items: center;

    .chanceHeader,
    .phaseHeader {
      width: 50%;
    }

    .chanceItem,
    .phaseItem {
      width: 50%;
      padding: 0 5px 0 0;
    }
  }

  .refundList {
    display: flex;
    width: 61%;
    justify-content: space-between;
    align-items: center;

    .refundHeader {
      display: flex;
      width: 27%;
      justify-content: center;
      align-items: center;
    }

    .refundItem {
      display: flex;
      width: 27%;
      justify-content: center;
      align-items: center;
    }
  }

  .ant-spin-nested-loading {
    flex: 1;
    overflow-y: auto;
    // 修改Firefox滚动条样式
    scrollbar-width: none;
  }

  .tableBody {
    height: 100%;
    padding: 0 16px 2px;
  }

  // 修改edge | chrome滚动条样式
  ::-webkit-scrollbar {
    width: 0;
  }

  .tableROw {
    font-size: 12px;
    display: flex;
    padding: 8px 0;
    border-bottom: 0.5px solid rgb(0 25 109 / 10%);
    word-break: break-all;
  }

  .editBtn {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  }

  .onCursor {
    cursor: pointer;
  }

  .editArea {
    height: 100%;
    backdrop-filter: blur(0);
  }

  .editbox {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
    background-color: #fff;
  }

  .nullData {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  // 没保存条件时的蒙版
  .mask {
    display: flex;
    width: 100%;
    height: 100%;
    position: absolute;
    flex-direction: column;
    align-items: center;
    top: 0;
    left: 0;
    z-index: 999;
    background: rgba(255 255 255 / 90%);
    box-sizing: border-box;
    backdrop-filter: blur(20px);
    border-radius: 18px;
    /* stylelint-disable property-no-vendor-prefix */
    /* stylelint-disable-next-line value-no-vendor-prefix */
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);

    .maskTitle {
      color: black;
      font-size: 20px;
      width: 100%;
      padding: 16px 0 0 16px;
      letter-spacing: 0.13em;
    }

    .maskBtn {
      color: #008cff;
      font-size: 18px;
      padding: 4px 10px;
      letter-spacing: 0.13em;
      position: absolute;
      top: 50%;
      left: 50%;
      border-radius: 532px;
      box-sizing: border-box;
      transform: translate(-50%, -50%);
      background: rgb(255 255 255 / 20%);
      border: 0.5px solid #008cff;
    }
  }
}

.small {
  &.ech-block-scrm-staff-return-money-list {
    .headBox {
      font-size: 10px;
      height: 22px;
      line-height: 22px;

      .imgBox {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }

      .headName {
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
    }

    .tableHead {
      font-size: 8px;
      padding: 8px 12px;
    }

    .tableBody {
      height: 100%;
      margin-top: 4px;
      padding: 0 12px 2px;
    }

    .tableROw {
      font-size: 8px;
      padding: 8px 0;
    }

    .editBtn {
      font-size: 12px;
      height: 36px;
    }

    .nullData {
      font-size: 8px;
      width: 100%;
      height: 100%;
    }

    // 没保存条件时的蒙版
    .mask {
      .maskTitle,
      .maskBtn {
        font-size: 12px;
      }
    }
  }
}
