.ech-block-scrm-dataAnalysis {
  color: #fff;
  font-size: 14px;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 12px;
  justify-content: center;
  position: relative;
  font-family: '苹方-简', sans-serif;
  cursor: pointer;
  background-image: url('https://static.huahuabiz.com/static/img/1722516825728870.png');
  background-size: 100% 100%;
  flex-direction: column;

  &.dis {
    background: none;
  }

  .titileName {
    font-weight: bold;
    margin-bottom: 4px;
    padding-left: 16px;
  }

  .schedule {
    display: flex;
    width: 100%;
    padding: 0 16px;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .typeAmount {
    width: calc(50% - 10px);

    &:nth-child(n + 3) {
      margin-top: 10px;
    }
  }

  .title {
    font-size: 12px;
    display: flex;
    height: 20px;
    line-height: 20px;
    align-items: center;

    .tag {
      width: max-content;
      min-width: 32px;
      min-height: 20px;
      line-height: 20px;
      margin-left: 12px;
      padding: 0 4px;
      text-align: center;
      border-radius: 4px;
    }

    .tagGreen {
      background-color: #05d380;
    }

    .tagYellow {
      background-color: #ff9201;
    }

    .tagRed {
      background-color: #ff5219;
    }
  }

  .amount {
    margin-top: 6px;

    .money {
      font-size: 18px;
      font-weight: 500;
      line-height: 1;
    }

    .unit {
      font-size: 12px;
      margin-left: 12px;
    }
  }

  .progressBar {
    .ant-progress-inner {
      background-color: rgba(#fff, 0.2);
    }

    &.ant-progress {
      height: 8px;
      line-height: 8px;
      box-sizing: border-box;
    }
  }

  .compare {
    display: flex;
    margin-top: 16px;
    padding: 0 16px;
    justify-content: space-between;
  }

  .itemType {
    font-weight: 500;
    display: flex;
    height: 52px;
    justify-content: space-between;
    flex-direction: column;
  }

  .placeholder {
    padding: 7px 8px;
  }

  .compareChange {
    display: flex;
    align-items: center;
  }

  .imgIcon {
    width: 14px;
    height: 16px;
    margin: 0 5px;
  }

  .month {
    margin-right: 4px;
  }

  .editBtn {
    color: #040919;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
    align-items: center;
    background: linear-gradient(0deg, rgb(214 231 255 / 30%), rgb(214 231 255 / 30%)),
      rgb(255 255 255 / 20%);
    border-top: 1px solid rgb(255 255 255 / 30%);
    backdrop-filter: blur(30px);
    box-shadow: 0 -2px 5px 0 rgb(0 9 44 / 3%);
  }

  // 没保存条件时的蒙版
  .mask {
    display: flex;
    width: 100%;
    height: 100%;
    position: absolute;
    flex-direction: column;
    align-items: center;
    top: 0;
    left: 0;
    z-index: 999;
    background: rgba(0 0 0 / 40%);
    box-sizing: border-box;
    backdrop-filter: blur(20px);

    .maskTitle {
      color: #fff;
      font-size: 20px;
      width: 100%;
      padding: 16px 0 0 16px;
      letter-spacing: 0.13em;
    }

    .maskBtn {
      color: #fff;
      font-size: 18px;
      padding: 4px 10px;
      letter-spacing: 0.13em;
      position: absolute;
      top: 50%;
      left: 50%;
      border-radius: 532px;
      box-sizing: border-box;
      transform: translate(-50%, -50%);
      background: rgb(255 255 255 / 20%);
      border: 0.5px solid rgb(255 255 255 / 50%);
    }
  }
}

.small {
  &.ech-block-scrm-dataAnalysis {
    .titileName {
      font-size: 10px;
      margin-bottom: 2px;
      padding-left: 10px;
    }

    .schedule {
      padding: 0 10px;

      .typeAmount {
        width: calc(50% - 4px);

        &:nth-child(n + 3) {
          margin-top: 6px;
        }
      }
    }

    .title {
      font-size: 8px;

      .tag {
        font-size: 8px;
        height: 14px;
        min-height: 14px;
        line-height: 14px;
        margin-left: 10px;
      }
    }

    .amount {
      display: flex;
      height: 24px;
      margin-top: 0;
      justify-content: space-between;
      flex-direction: column;

      .money {
        font-size: 12px;
        font-weight: 500;
      }

      .unit {
        font-size: 8px;
        margin-left: 8px;
      }

      .progressBar {
        .ant-progress-bg {
          height: 6px !important;
          line-height: 6px !important;
        }
      }
    }

    .compare {
      margin-top: 10px;
      padding: 0 12px;
    }

    .itemType {
      font-size: 8px;
      height: 26px;
    }

    .editBtn {
      font-size: 12px;
      height: 36px;
    }

    // 没保存条件时的蒙版
    .mask {
      .maskTitle,
      .maskBtn {
        font-size: 12px;
      }
    }
  }
}
