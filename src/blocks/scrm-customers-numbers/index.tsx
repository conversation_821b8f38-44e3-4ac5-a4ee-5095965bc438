import { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import useElSize from '@/hooks/use-el-size';
import { useTranslation } from 'react-i18next';
import { IEchBlockCard } from '../interface';
import CardContext from '../public/components/card-context';
import getCustomerTotalCountCard from './api/get-customer-total-count';
import styles from './index.module.less';

function ScrmCustomersNumbers({ navigate, blockId }: IEchBlockCard) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [customerNum, setCustomerNum] = useState(0);

  // 获取当前卡片的DOM元素
  const echBlockScrmOpportunityTotalAmountRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockScrmOpportunityTotalAmountRef, 150);

  const getTotalAmount = useMemo(() => `${customerNum}`, [customerNum]);

  useEffect(() => {
    if (blockId) {
      setLoading(true);
      getCustomerTotalCountCard(blockId)
        .then((res) => {
          setCustomerNum(res.customerTotalCount);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [blockId]);

  return (
    <CardContext
      size={echBlockScrmOpportunityTotalAmountRef.current?.offsetWidth}
      isSmall={isSmall}
      loading={loading}
      isPermission
      spinWrapperClassName={styles.spinWrapper}
    >
      <div
        ref={echBlockScrmOpportunityTotalAmountRef}
        role="button"
        tabIndex={0}
        onClick={(e) => {
          e.stopPropagation();
          if (navigate) {
            navigate('/scrm/client/firm-client');
          }
        }}
        className={classNames(styles.scrm_opportunity_total_amount, { [styles.small]: isSmall })}
      >
        <div
          className={classNames(styles.numbers, {
            [styles.smallNum]: getTotalAmount.length > 6,
          })}
          title={getTotalAmount.length > 8 ? getTotalAmount : undefined}
        >
          {getTotalAmount || '-'}
        </div>
        <div className={styles.labelName}>{t('desk_scrmCustomers_currentCustomers')}</div>
      </div>
    </CardContext>
  );
}

export default ScrmCustomersNumbers;
