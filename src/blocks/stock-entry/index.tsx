import { useTranslation } from 'react-i18next';
import { IEchBlockCard } from '@/blocks/interface';
import { isFunction } from 'lodash';
import './index.less';

const stocks = [
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578514948963.png`,
    titleKey: 'desk_stockEntry_warehouseList',
    url: '/stock/warehouse',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578640854538.png`,
    titleKey: 'desk_stockEntry_warehouseQuery',
    url: '/stock/query',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578669761308.png`,
    titleKey: 'desk_stockEntry_queryReport',
    url: '/stock/batch',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578708185220.png`,
    titleKey: 'desk_stockEntry_stockWarning',
    url: '/stock/warn',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578734785603.png`,
    titleKey: 'desk_stockEntry_replenishment',
    url: '/stock/replenish',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722578734785603.png`,
    titleKey: 'desk_stockEntry_backendManagement',
    url: '/stock/backstage/node-approval',
  },
  {
    src: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/172257891978414.png`,
    titleKey: 'desk_stockEntry_locationManagement',
    url: '/stock/slotting-setting',
  },
];

function StockEntry({ navigate }: IEchBlockCard) {
  const { t } = useTranslation();

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  return (
    <div className="ech-block-stock-entry">
      <div className="ech-block-stock-entry-title">
        <img
          className="ech-block-stock-entry-logo"
          src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1722570373495411.png`}
          alt=""
        />
        <span>{t('desk_stockEntry_title')}</span>
      </div>
      <div className="ech-block-stock-entry-content">
        {stocks.map((item) => (
          <div
            role="button"
            tabIndex={0}
            className="ech-block-stock-entry-item"
            key={item.titleKey}
            onClick={() => onNavTo(item.url)}
          >
            <img className="ech-block-stock-entry-item-img" src={item.src} alt="" />
            <div className="ech-block-stock-entry-item-text">{t(item.titleKey)}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default StockEntry;
