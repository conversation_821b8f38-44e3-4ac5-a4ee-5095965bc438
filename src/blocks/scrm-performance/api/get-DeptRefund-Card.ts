import { createHttpRequest } from '@echronos/core';

/**
 * 获取销售团队回款数据分析卡片
 */

interface DeptRefundListType {
  deptId: number; // 部门ID
  deptName: string; // 部门名称
  targetMoney: string; // 目标金额
  opportunityMoney: string; // 商机总金额
  returnedMoney: string; // 回款总金额
}

export interface DeptRefundCardType {
  deptRefundQuarterList: number[];
  deptRefundList: DeptRefundListType[];
}

function getDeptRefundCard(blockId: string): Promise<DeptRefundCardType> {
  return createHttpRequest('ech-crm-new')('/v2/home/<USER>/getDeptRefundCard', {
    method: 'GET',
    autoToast: false,
    params: {
      cardUuid: blockId,
    },
  });
}

export default getDeptRefundCard;
