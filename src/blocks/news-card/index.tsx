import { useEffect, useRef, useState } from 'react';
import { useUser } from '@echronos/react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import NoDataCard from '@/blocks/public/components/no-data-card';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import { IEchBlockCard } from '../interface';
import styles from './index.module.less';
import getNewsCardInfo, { NewsListTypeItem } from './apis';

function NewsCard({ navigate }: IEchBlockCard) {
  const { t } = useTranslation();
  const [user] = useUser();
  // 新闻卡片信息
  const [newsList, setNewsList] = useState<NewsListTypeItem[]>([]);
  // 公司logo
  const [logo, setLogo] = useState<string>('');
  // 更新时间
  const [time, setTime] = useState<string>('');
  // 获取当前卡片的DOM元素
  const echBlockNewsCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockNewsCardRef, 150);
  // 获取卡片尺寸
  const [size, setSize] = useState<number>();

  // 跳转链接
  const handleNavigate = (id: number) => {
    if (navigate) {
      navigate(`/news/detail/${id}?companyId=${user.companyId}`);
    }
  };

  const toHome = () => {
    if (navigate) {
      navigate(`/news/home`);
    }
  };

  const getTime = () => {
    // 获取当前时间
    const now = dayjs();
    // 格式化时间为指定的格式
    const formattedDate = now.format('HH:mm:ss');

    setTime(formattedDate);
  };

  useEffect(() => {
    getTime();
    setSize(echBlockNewsCardRef.current?.offsetWidth);
    getNewsCardInfo({ companyId: user.companyId, searchKey: '', pageNo: 1, pageSize: 3 }).then(
      (res) => {
        if (res.list && res.list.length > 0) {
          setLogo(res.list[0].companyLogo);
          setNewsList(res.list);
        }
      }
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div
      ref={echBlockNewsCardRef}
      className={classNames(styles.echBlockNewsCard, { [styles.small]: isSmall })}
      role="button"
      tabIndex={0}
    >
      {/* 卡片头部 */}
      <div className={styles.echBlockNewsCardHeader} role="button" tabIndex={0} onClick={toHome}>
        <div className={styles.echBlockNewsCardHeaderLeft}>
          <div className={styles.echBlockNewsCardHeaderLeftTitle}>
            <img
              src={
                logo || `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1727265734277306.png`
              }
              alt=""
              className={styles.echBlockNewsCardHeaderLogo}
            />
            {t('desk_news_title')}
          </div>
          <div className={styles.echBlockNewsCardHeaderLeftTime}>
            {t('desk_news_updateTime', { time })}
          </div>
        </div>

        {/* <div className={styles.echBlockNewsCardHeaderRight}>
          <span className={styles.echBlockNewsCardHeaderRightWeather}>30°</span>
          <span className={styles.echBlockNewsCardHeaderRightCity}>深圳市</span>
        </div> */}
      </div>
      {/* 卡片内容 */}
      <ul className={styles.echBlockNewsCardContentList}>
        {newsList.length ? (
          newsList.map((item) => (
            <li
              className={styles.echBlockNewsCardContentListItem}
              role="presentation"
              key={item.id}
              onClick={() => {
                handleNavigate(item.id);
              }}
            >
              <p className={styles.newTitle}>{item.title}</p>
              <div className={styles.echBlockNewsCardContentListItemImgBox}>
                <img
                  src={item.coverImgList[0]}
                  alt=""
                  className={styles.echBlockNewsCardContentListItemImg}
                />
              </div>
            </li>
          ))
        ) : (
          <NoDataCard isSmall={isSmall} size={size} />
        )}
      </ul>
    </div>
  );
}

export default NewsCard;
