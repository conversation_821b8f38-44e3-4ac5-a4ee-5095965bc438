.echBlockNewsCard {
  color: #040919;
  width: 100%;
  height: 100%;
  padding: 15px;
  position: relative;
  font-family: '苹方-简', sans-serif;
  background-color: rgb(255 255 255 / 70%);
}

.echBlockNewsCardHeader {
  display: flex;
  justify-content: space-between;
}

.echBlockNewsCardHeaderLeftTitle {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;

  .echBlockNewsCardHeaderLogo {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
}

.echBlockNewsCardHeaderLeftTime {
  color: #888b98;
  font-size: 12px;
  font-weight: 400;
  min-height: 12px;
  line-height: 12px;
  margin-top: 4px;
}

.echBlockNewsCardHeaderRightWeather {
  font-size: 24px;
  font-weight: 600;
  line-height: 26px;
}

.echBlockNewsCardHeaderRightCity {
  font-size: 12px;
  font-weight: 400;
  line-height: 12px;
  margin-left: 1px;
}

.echBlockNewsCardContentList {
  min-height: 165px;
  margin-top: 25px;

  .echBlockNewsCardContentListItem {
    display: flex;
    margin-top: 16px;
    justify-content: space-between;

    .newTitle {
      font-size: 14px;
      font-weight: 600;
      display: -webkit-box;
      max-width: 154px;
      max-height: 34px;
      line-height: 17px;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      text-overflow: ellipsis;
    }

    .echBlockNewsCardContentListItemImgBox {
      width: 65px;
      height: 44px;
    }

    .echBlockNewsCardContentListItemImg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
}

.small {
  padding: 10px;

  .echBlockNewsCardHeaderLeftTitle {
    font-size: 8px;
    line-height: 12px;

    .echBlockNewsCardHeaderLogo {
      width: 12px;
      height: 12px;
      margin-right: 3px;
    }
  }

  .echBlockNewsCardHeaderLeftTime {
    font-size: 7px;
    min-height: 12px;
    line-height: 12px;
    margin-top: 2px;
  }

  .echBlockNewsCardContentList {
    min-height: 104px;
    margin-top: 0;

    .echBlockNewsCardContentListItem {
      display: flex;
      margin-top: 16px;
      justify-content: space-between;

      .newTitle {
        font-size: 8px;
        font-weight: 600;
        display: -webkit-box;
        width: 88px;
        max-width: 88px;
        max-height: 18px;
        line-height: 9px;
        overflow: hidden;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        text-overflow: ellipsis;
      }

      .echBlockNewsCardContentListItemImgBox {
        width: 36px;
        height: 24px;
      }

      .echBlockNewsCardContentListItemImg {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
      }
    }
  }
}
