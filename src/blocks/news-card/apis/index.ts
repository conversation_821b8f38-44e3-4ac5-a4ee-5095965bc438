import { createHttpRequest, PaginationResponse } from '@echronos/core';

/**
 * 获取新闻卡片信息
 */

interface paramsType {
  topicId?: number; // 栏目id
  companyId: number; // 公司id
  searchKey: string; // 搜索关键词
  pageNo: number; // 页码
  pageSize: number; // 大小
}

export interface NewsListTypeItem {
  mainEventsId: number;
  id: number;
  title: string;
  coverImg: string;
  coverImgList: string[];
  companyLogo: string;
}

export interface IFile {
  uid: string; // uid

  companyId: number; // 公司id

  businessId: number; // 关联业务id

  businessType: number; // 内容附件类型,10:新闻附件,20:公告附件,30:制度附件

  fileFormat: string; // 文件格式后缀

  fileName: string; // 文件名称

  fileSize: number; // 文件大小

  filePath: string; // 附件地址

  id: number; // ID

  createUser: number; // 创建人id

  createTime: string; // 创建时间

  updateUser: number; // 修改者id

  updateTime: string; // 更新时间
}

interface NewsEventsVO {
  companyId: number; // 公司id
  title: string; // 新闻标题
  content: string; // 内容
  shortContent: string; // 简写新闻
  topicId: number; // 栏目id
  isHot: number; // 是否热点:0否1是
  isTop: number; // 是否置顶:0否1是
  coverImg: string; // 封面图片url
  coverImgList: string[]; // 封面图片url集合 ,String
  displayScope: number; // 权限设置：,0、公开（所有人可见）,10、企业内部（仅同事可见）
  hotTime: number; // 热点时间
  topTime: number; // 置顶时间
  id: number; // ID
  createUser: number; // 创建人id
  createTime: number; // 创建时间
  updateUser: number; // 修改者id
  updateTime: number; // 更新时间
  publishStatus: number; // 发布状态,0、草稿,10、已发布
  publishStatusName: string; // 发布状态,0、草稿,10、已发布
  publishIp: string; // 发布地ip地址
  ipAddress: string; // ip所在地
  fileList: IFile[]; // 新闻附件列表
  recommendList: NewsEventsVO[]; //
  createUserName: string; // 作者
  topicName: string; // 栏目
  mainEventsId: number; // 主新闻id
  companyName: string; // 公司名称
  companyLogo: string; // 公司logo
}

function getNewsCardInfo(params: paramsType): PaginationResponse<NewsEventsVO> {
  return createHttpRequest('ech-cms')('/v1/cms/newsEvents/pageForManager', {
    method: 'GET',
    params,
  });
}

export default getNewsCardInfo;
