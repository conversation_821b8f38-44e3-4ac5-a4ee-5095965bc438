import { useTranslation } from 'react-i18next';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import type { VendorRecruitmentProp } from '../uoum-card/api/get-uoum-vendor-recruitment';
import { getUoumVendorRecruitment } from '../uoum-card/api/get-uoum-vendor-recruitment';
import Cart from '../uoum-card/components/cart-title';
import SendTime from '../uoum-card/components/send-time';
import styles from './index.module.less';

function UoumVendorRecruitmentCard() {
  const { t } = useTranslation();
  // 获取当前卡片的DOM元素
  const uoumVendorRecruitmentCardRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(uoumVendorRecruitmentCardRef, 324);
  // 获取元素的宽度
  const domWidth = uoumVendorRecruitmentCardRef.current?.offsetWidth;

  const [vendorData, setVendorData] = useState<VendorRecruitmentProp[]>([]);

  const { run, loading } = useRequest(
    () =>
      // categoryIdList
      getUoumVendorRecruitment({
        moduleTypes: [80],
        pageNo: 1,
        pageSize: 3,
        tenantId: '425f6cec-1a50-49f8-9be3-c97ab72874b0',
        topicId: 5389,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        setVendorData(res.list);
      },
    }
  );

  useEffect(() => {
    run();
  }, [run]);

  return (
    <div ref={uoumVendorRecruitmentCardRef} style={{ width: '100%', height: '100%' }}>
      {!loading && (
        <Cart
          url="community/5389"
          type="VendorRecruitment"
          title={t('desk_uoumVendor_title')}
          isSmall={isSmall}
          size={domWidth}
        >
          <div className={classNames(styles.cartItem, { [styles.small]: isSmall })}>
            {vendorData &&
              vendorData?.map((item) => (
                <div className={styles.cartItemContent} key={item.id}>
                  <a
                    target="_blank"
                    href={`${import.meta.env.BIZ_UM_SITE}/um/community/5389/${item.id}`}
                    className={styles.cartMainTitle}
                    rel="noreferrer"
                  >
                    {item.title}
                  </a>
                  <SendTime time={item.createTime} />
                </div>
              ))}
          </div>
        </Cart>
      )}
    </div>
  );
}

export default UoumVendorRecruitmentCard;
