import { createHttpRequest, PaginationResponse } from '@echronos/core';

/**
 * 获取公告卡片信息
 */

interface paramsType {
  categoryId?: number;
  searchKey?: string;
  pageNo?: number;
  pageSize?: number;
}

export interface NoticeListType {
  id: number;
  title: string;
  content: string;
  cover: string;
}

function getNoticeCardInfo(params: paramsType): PaginationResponse<NoticeListType> {
  return createHttpRequest('ech-cms')('/v1/notice/list/to/desk', {
    method: 'GET',
    params,
  });
}

export default getNoticeCardInfo;
