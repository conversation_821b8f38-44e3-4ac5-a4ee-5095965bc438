.ech-block-order-entry {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: url('https://static.huahuabiz.com/static/img/1722838960957988.png');
  background-size: cover;
  background-repeat: no-repeat;
  transition: all 0.3s;
}

.ech-block-order-entry-title {
  color: #fff;
  font-weight: 600;
  display: flex;
  padding: 12px;
  align-items: center;
}

.ech-block-order-entry-logo {
  width: 22px;
  height: 22px;
  margin-right: 4px;
}

.ech-block-order-entry-content {
  display: flex;
  flex-wrap: wrap;
}

.ech-block-order-entry-item {
  width: 33.33%;
  height: 68px;
  text-align: center;
  cursor: pointer;
}

.ech-block-order-entry-item-img {
  width: 42px;
  height: 42px;
  margin-bottom: 4px;
}

.ech-block-order-entry-item-text {
  color: rgb(255 255 255 / 80%);
  font-size: 12px;
}

// 小尺寸的样式代码
.small {
  .ech-block-order-entry-title {
    color: #fff;
    font-size: 8px;
    font-weight: 600;
    display: flex;
    padding: 12px;
    align-items: center;
  }

  .ech-block-order-entry-logo {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }

  .ech-block-order-entry-item {
    height: 42px;
  }

  .ech-block-order-entry-item-img {
    width: 22px;
    height: 22px;
    margin-bottom: 2px;
  }

  .ech-block-order-entry-item-text {
    font-size: 6px;
  }
}
