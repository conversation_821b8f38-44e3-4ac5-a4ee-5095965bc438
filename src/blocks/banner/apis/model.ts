export interface IMyAppContent {
  appId: number; // 应用主键
  coding: string; // 功能code
  display: number; // 显示设置: 0 web端  1 APP端 2 都显示
  funcDesc: string; // 功能描述
  id: number; // 功能主键
  isCharge: number; // 是否购买：0 未购买， 1已购买
  isCompanyStop: number; // 是否公司应用级：0 启用， 1停用
  isOpen: number; // 是否打开新页面：0否 1是
  isPermission: number; // 是否有权限：0 无， 1有
  level: number; // 是否企业级固定应用： 0否 1是
  name: string; // 功能名称
  photo: string; // 图标
  updateTime: number; // 更新时间
  url: string; // 路由
  useStatus: number; // 平台级停用状态：0 未启用， 1 启用中
  sort: number; //	排序
  externalApp: number; // 是否外部app
  // 应用分类 1：内部 2：外部 3：小程序
  classify: 1 | 2 | 3;
  echAppId: string;
}

export interface IMyAppData extends IMyAppContent {
  content: any | null;
  coding: string; // 功能code
  type: number; // 1 图片 2 功能性组件 3 应用 4 文件夹
}

export type IMyApp = IMyAppData;
