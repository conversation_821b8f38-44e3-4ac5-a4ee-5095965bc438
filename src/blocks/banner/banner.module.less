.echBlockBanner {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 18px;
  background-repeat: no-repeat;
  background-position: left;
  background-size: cover;

  :global {
    .ant-carousel {
      overflow: hidden;
      border-radius: 18px;
      box-shadow: 0 4px 12px rgba(0 0 0 / 5%);
    }

    .ant-carousel .slick-dots {
      margin-right: 0;
      margin-left: 0;
    }

    .ant-carousel .slick-dots li {
      width: 10px;
      height: 14px;
    }

    .ant-carousel .slick-dots li button {
      color: transparent;
      display: block;
      width: 100%;
      padding: 0;
      transition: all 0.5s;
      opacity: 1;
      background: transparent;
      border-radius: 1px;
      outline: none;
      cursor: pointer;
    }

    .slick-dots li button::before {
      //   content: '•';
      //   color: #fff;
      font-size: 8px;
      //   width: 20px;
      //   height: 20px;
      //   line-height: 20px;
      //   position: absolute;
      //   top: 0;
      //   left: 0;
      //   font-family: slick, sans-serif;
      //   opacity: 0.25;
      //   text-align: center;
      //   -webkit-font-smoothing: antialiased;
      //   -moz-osx-font-smoothing: grayscale;
    }

    .slick-dots li.slick-active button::before {
      color: #fff;
    }
  }
}

.bannerBgItem {
  width: 100%;
  min-height: 258px;
  max-height: calc(100% - 24px);
  border-radius: 18px;
  background-repeat: no-repeat;
  background-position: left;
  background-size: cover;
}

.companyName {
  color: #fff;
  font-size: 20px;
  font-weight: normal;
  width: 100%;
  height: 66px;
  line-height: 64px;
  padding-left: 48px;
  position: absolute;
  bottom: -2px;
  left: 0;
  background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 41%) 100%);
  border-radius: 20px;
}

.isUrlFnStyle {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

// 小尺寸的卡片样式
.small {
  :global {
    .ant-spin-nested-loading,
    .ant-spin-container,
    .ant-carousel,
    .slick-slider {
      height: 100%;
    }
  }

  .bannerBgItem {
    width: 100%;
    min-height: 168px;
    max-height: calc(100% - 24px);
  }

  .companyName {
    font-size: 12px;
    line-height: 88px;
    padding-left: 32px;
  }
}
