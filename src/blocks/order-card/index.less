.ech-block-order-card {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  position: relative;
  background: linear-gradient(65deg, #1b38fa 2%, #1b87fa 97%);
}

.ech-block-order-card-title {
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
  align-items: center;
}

.ech-block-order-card-title-text {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
}

.ech-block-order-card-title-add {
  color: #fff;
  font-size: 12px;
  width: 56px;
  height: 24px;
  line-height: 22px;
  text-align: center;
  border-radius: 6px;
  border: 1px solid #fff;
  cursor: pointer;
}

.ech-block-order-card-title-add-icon {
  margin-right: 4px;
}

.ech-block-order-card-content {
  padding: 0 0 20px;
}

.ech-block-order-card-content-text {
  color: #fff;
  font-size: 48px;
  font-weight: 700;
  cursor: pointer;
}

.ech-block-order-card-content-tip {
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}

.ech-block-order-card-footer {
  display: flex;
}

.ech-block-order-card-status {
  color: #fff;
  position: relative;
  flex: 1;
  cursor: pointer;

  &-tip-icon {
    position: absolute;
    top: -1px;
    left: 84px;
  }

  &:first-child {
    .ech-block-order-card-status-tip-icon {
      left: 42px;
    }
  }

  &:not(:first-child) {
    text-align: center;
  }

  &:not(:last-child) {
    &::after {
      content: '';
      width: 1px;
      height: 24px;
      position: absolute;
      top: 10px;
      right: 0;
      background-color: rgb(216 216 216 / 20%);
    }
  }
}

.ech-block-order-card-status-text {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
}

.ech-block-order-card-status-tip {
  position: relative;
}

.small {
  .ech-block-order-card-title {
    padding: 16px 0;
    padding-bottom: 10px;
  }

  .ech-block-order-card-title-text {
    font-size: 18px;
  }

  .ech-block-order-card-title-add {
    font-size: 10px;
    width: 54px;
    height: 22px;
    line-height: 20px;
  }

  .ech-block-order-card-title-add-icon {
    font-size: 10px !important;
  }

  .ech-block-order-card-content-text {
    font-size: 28px;
  }

  .ech-block-order-card-content-tip {
    font-size: 10px;
    display: flex;
    align-items: center;
  }

  .ech-block-order-card-content {
    padding: 0 0 8px;

    .echos-icon {
      font-size: 14px !important;
    }
  }

  .ech-block-order-card-status {
    .ech-block-order-card-status-tip-icon {
      position: absolute;
      top: -3px;
      left: 50px;
    }

    &:first-child {
      .ech-block-order-card-status-tip-icon {
        left: 30px;
      }
    }

    &:not(:last-child) {
      &::after {
        content: '';
        width: 1px;
        height: 20px;
        position: absolute;
        top: 6px;
        right: 0;
        background-color: rgb(216 216 216 / 20%);
      }
    }
  }

  .ech-block-order-card-status-text {
    font-size: 12px;
  }

  .ech-block-order-card-status-tip {
    font-size: 10px;

    .echos-icon {
      font-size: 14px !important;
    }
  }
}
