import { createHttpRequest } from '@echronos/core';

/**
 * @description 查询订单数量统计
 */

interface OrderCardParams {
  ordType: number;
  ifError: number;
}

export interface OrderCardData {
  total: number;
  pendingNum: number;
  processingNum: number;
  completedNum: number;
  closeNum: number;
}

function getOrderCardApi(params: OrderCardParams): Promise<OrderCardData> {
  return createHttpRequest('ech-order')('/v1/order/statistice/query/salepur/ord/num', {
    params,
    autoToast: false,
  });
}

export default getOrderCardApi;
