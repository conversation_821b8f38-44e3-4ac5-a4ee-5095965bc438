import { useEffect, useRef, useState } from 'react';
import Icon from '@echronos/echos-icon';
import { useRequest } from 'ahooks';
import { isFunction } from 'lodash';
import { useTranslation } from 'react-i18next';
import { IEchBlockCard } from '@/blocks/interface';
import NoPermissionCard from '@/blocks/public/components/no-permission-card';
import useElSize from '@/hooks/use-el-size';
import classNames from 'classnames';
import getOrderCardApi, { OrderCardData } from './get-order-card-api';
import './index.less';

const orderNums = [
  {
    title: 'desk_order_total',
    type: 'total',
    url: (val: number) => `/indent/list?orderStatus=0&region=${val}&open=0`,
  },
  {
    title: 'desk_order_processing',
    type: 'processingNum',
    url: (val: number) => `/indent/list?orderStatus=2&region=${val}&open=0`,
  },
  {
    title: 'desk_order_completed',
    type: 'completedNum',
    url: (val: number) => `/indent/list?orderStatus=80&region=${val}&open=0`,
  },
  {
    title: 'desk_order_closed',
    type: 'closeNum',
    url: (val: number) => `/indent/list?orderStatus=90&region=${val}&open=0`,
  },
];

const orders = [
  {
    type: 'buy',
    title: 'desk_order_purchase',
    orderType: 1,
    region: 0,
    code: 'R_001_002',
    codeAdd: 'R_001_010_001',
  },
  {
    type: 'sell',
    title: 'desk_order_sale',
    orderType: 0,
    region: 1,
    code: 'R_001_002',
    codeAdd: 'R_001_009_001',
  },
];

interface OrderCardProps extends IEchBlockCard {
  type: string;
}

function OrderCard({ type, navigate }: OrderCardProps) {
  const { t } = useTranslation();
  const [info, setInfo] = useState<OrderCardData>({
    closeNum: 0,
    completedNum: 0,
    pendingNum: 0,
    processingNum: 0,
    total: 0,
  });
  const [isPerm, setIsPerm] = useState(true);
  const orderInfo = orders.find((item) => item.type === type);

  // 获取当前DOM元素
  const echBlockOrderCardRef = useRef<HTMLDivElement>(null);

  // 判断是否为小尺寸
  const isSmall = useElSize(echBlockOrderCardRef, 324);

  const { run } = useRequest(getOrderCardApi, {
    manual: true,
    defaultParams: [{ ordType: orderInfo?.orderType || 0, ifError: 1 }],
    onSuccess: (res) => {
      setInfo(res);
    },
    onError: (err: any) => {
      if (err.code === 5) {
        setIsPerm(false);
      }
    },
  });

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const onCreate = () => {
    // if (!checkCharge(orderInfo?.codeAdd) || !checkPermission(orderInfo?.codeAdd)) {
    //   message.warn('暂无权限');
    //   return;
    // }
    onNavTo(`/indent/create?orderProperty=${orderInfo?.orderType}&orderType=1`);
  };

  useEffect(() => {
    run({ ordType: orderInfo?.orderType || 0, ifError: 1 });
  }, [orderInfo?.orderType, run]);

  return (
    <div
      ref={echBlockOrderCardRef}
      className={classNames('ech-block-order-card', { small: isSmall })}
    >
      <div className="ech-block-order-card-title">
        <div className="ech-block-order-card-title-text">
          {t(orderInfo?.title || 'desk_order_default')}
          {t('desk_order_suffix')}
        </div>
        <div
          role="button"
          tabIndex={0}
          className="ech-block-order-card-title-add"
          onClick={onCreate}
        >
          <Icon className="ech-block-order-card-title-add-icon" name="add_line" />
          {t('desk_order_create')}
        </div>
      </div>
      <div className="ech-block-order-card-content">
        <div className="ech-block-order-card-content-text">{info.pendingNum}</div>
        <div
          role="button"
          tabIndex={0}
          className="ech-block-order-card-content-tip"
          onClick={() => onNavTo(`/indent/list?orderStatus=1&region=${orderInfo?.region}&open=0`)}
        >
          {t('desk_order_pending', { type: t(orderInfo?.title || 'desk_order_default') })}
          <Icon name="right_arrow_line" size={16} />
        </div>
      </div>
      <div className="ech-block-order-card-footer">
        {orderNums.map((item) => (
          <div
            role="button"
            tabIndex={0}
            className="ech-block-order-card-status"
            key={item.type}
            onClick={() => onNavTo(item.url(orderInfo?.region || 0))}
          >
            {/*  @ts-ignore */}
            <div className="ech-block-order-card-status-text">{info[item.type]}</div>
            <div className="ech-block-order-card-status-tip">
              {t(item.title)}
              <Icon
                className="ech-block-order-card-status-tip-icon"
                name="right_arrow_line"
                size={16}
              />
            </div>
          </div>
        ))}
      </div>
      {!isPerm && (
        <NoPermissionCard
          appName={t('desk_order_management')}
          cardName={`${t(orderInfo?.title || 'desk_order_default')}${t('desk_order_suffix')}`}
          isSmall={isSmall}
          size={echBlockOrderCardRef.current?.offsetWidth}
        />
      )}
    </div>
  );
}

export default OrderCard;
