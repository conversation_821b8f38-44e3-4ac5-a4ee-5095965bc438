import { useEffect, useMemo, useRef, useState } from 'react';
import { isFunction } from 'lodash';
import { useRequest } from 'ahooks';
import BigNumber from 'big.js';
import type { RadioChangeEvent } from '@echronos/antd';
import { Radio } from '@echronos/antd';
import CardContext from '@/blocks/public/components/card-context';
import CardHeaderTitle from '@/blocks/public/components/card-header-title';
import { getFinanceHomePageFour, FinanceHomeFourTotal } from '@/blocks/public/apis';
import { formatPrice } from '@/blocks/public/utils/utils';
import { IEchBlockCard } from '@/blocks/interface';
import { useTranslation } from 'react-i18next';

import classNames from 'classnames';
import useElSize from '@/hooks/use-el-size';
import styles from './index.module.less';

export type FinanceCollectionPaymentProps = IEchBlockCard & {
  appName?: string;
  logo?: string;
};

function FinanceCollectionPayment({ appName, logo, navigate }: FinanceCollectionPaymentProps) {
  const { t } = useTranslation();
  const [isPermission, setIsPermission] = useState(true);
  const [radioVal, setRadioVal] = useState(1);
  // 获取当前卡片的DOM元素
  const financeCollectionPaymentRef = useRef<HTMLDivElement>(null);
  // 判断是否为小尺寸
  const isSmall = useElSize(financeCollectionPaymentRef, 150);

  const { data, loading, run } = useRequest(
    () =>
      getFinanceHomePageFour()
        .then((res) => res.list)
        .catch((err) => {
          if (err.code === 5) {
            setIsPermission(false);
          }
          return [
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 1,
              totalTypeStr: t('desk_financeCP_orderPayment'),
              type: 1,
              typeStr: '',
            },
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 2,
              totalTypeStr: t('desk_financeCP_orderPayment'),
              type: 2,
              typeStr: '',
            },
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 3,
              totalTypeStr: t('desk_financeCP_downstreamRecharge'),
              type: 1,
              typeStr: '',
            },
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 4,
              totalTypeStr: t('desk_financeCP_downstreamRecharge'),
              type: 2,
              typeStr: '',
            },
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 5,
              totalTypeStr: t('desk_financeCP_balanceWithdrawal'),
              type: 1,
              typeStr: '',
            },
            {
              count: 0,
              monthIndex: '',
              totalAmount: '0',
              totalType: 6,
              totalTypeStr: t('desk_financeCP_balanceWithdrawal'),
              type: 2,
              typeStr: '',
            },
          ];
        }),
    {
      manual: true,
    }
  );

  const radioOptionsList = [
    { label: t('desk_financeCP_waitCollection'), value: 1 },
    { label: t('desk_financeCP_waitPayment'), value: 0 },
  ];
  // 筛选首付款
  const dataListMemo = useMemo(() => {
    if (radioVal) {
      return data?.filter((item) => item.type === 1) || [];
    }
    return data?.filter((item) => item.type === 2) || [];
  }, [data, radioVal]);

  // 计算收付款总计
  const totalPrice = useMemo(() => {
    if (radioVal) {
      return dataListMemo?.reduce(
        (acc, cur) => new BigNumber(acc).plus(new BigNumber(cur.totalAmount)).toNumber(),
        0
      );
    }
    return dataListMemo?.reduce(
      (acc, cur) => new BigNumber(acc).plus(new BigNumber(cur.totalAmount)).toNumber(),
      0
    );
  }, [dataListMemo, radioVal]);

  // 计算收付款总计数量
  const totalCount = useMemo(() => {
    if (radioVal) {
      return dataListMemo?.reduce((acc, cur) => acc + Number(cur.count), 0);
    }
    return dataListMemo?.reduce((acc, cur) => acc + Number(cur.count), 0);
  }, [dataListMemo, radioVal]);

  const onNavTo = (val: string) => {
    if (isFunction(navigate)) {
      navigate(val);
    }
  };

  const JumpFinanceFn = (totalType: FinanceHomeFourTotal['totalType']) => {
    let url = '/finance-home';
    switch (totalType) {
      case 1: // 订单收款-收款
        url = '/finance-management/collection/order-payment?payStatus=1';
        break;
      case 3: // 充值收款-收款
        url = '/finance-management/collection/downstream-recharge?payStatus=1';
        break;
      case 5: // 余额提现-收款
        url = '/finance-management/collection/balance-withdrawal?payStatus=1';
        break;
      case 2: // 订单款项-付款
        url = '/finance-management/payment/order-payment?payStatus=0';
        break;
      case 4: // 充值付-付款
        url = '/finance-management/payment/recharge-payment?payStatus=0';
        break;
      case 6: // 余额-付款
        url = '/finance-management/payment/withdrawal-payment?payStatus=0';
        break;
      default:
        break;
    }
    onNavTo(url);
  };
  const onChange = ({ target: { value } }: RadioChangeEvent) => {
    setRadioVal(value);
  };

  useEffect(() => {
    run();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CardContext
      loading={loading}
      isPermission={isPermission}
      spinWrapperClassName={styles.spinWrap}
      logo={logo}
      appName={appName ?? t('desk_financeCP_defaultName')}
    >
      <div
        ref={financeCollectionPaymentRef}
        className={classNames(styles.financeCollectionPayment, { [styles.small]: isSmall })}
      >
        <CardHeaderTitle
          size={financeCollectionPaymentRef.current?.offsetWidth}
          isSmall={isSmall}
          logo={logo}
          cardName={appName ?? t('desk_financeCP_defaultName')}
          cardNameColor="#fff"
          headerRight={
            <div className={styles.headerRight}>
              <Radio.Group
                options={radioOptionsList}
                onChange={onChange}
                value={radioVal}
                optionType="button"
                buttonStyle="solid"
                size="small"
              />
            </div>
          }
        />
        <div className={styles.body} role="presentation" onClick={() => onNavTo('/finance-home')}>
          <div className={styles.bodyTop}>
            <div className={styles.collectionPrice}>¥{formatPrice(totalPrice)}</div>
            <div className={styles.collectionPayment}>
              {t('desk_financeCP_totalWait', {
                type: radioVal === 1 ? t('desk_financeCP_collection') : t('desk_financeCP_payment'),
                count: totalCount,
              })}
            </div>
          </div>
          <div className={styles.bodyBottom}>
            {dataListMemo?.map((item) => (
              <div
                className={styles.rankItem}
                key={item.totalTypeStr}
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  JumpFinanceFn(item.totalType as FinanceHomeFourTotal['totalType']);
                }}
              >
                <span className={styles.rankItemTitle}>
                  {t('desk_financeCP_itemTitle', { type: item.totalTypeStr, count: item.count })}
                </span>
                <span
                  className={styles.rankItemPrice}
                  title={Number(item.totalAmount) ? `¥${formatPrice(item.totalAmount)}` : ''}
                >
                  ¥{formatPrice(item.totalAmount)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </CardContext>
  );
}

FinanceCollectionPayment.defaultProps = {
  // appName: '财务管理',
  appName: undefined,
  logo: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1723021260207222.png`,
};

export default FinanceCollectionPayment;
