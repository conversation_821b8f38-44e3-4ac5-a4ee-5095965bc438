.financeCollectionPayment {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 12px;
  background-image: url('./images/finance-icon-one.png');
  background-size: cover;
}

.headerRight {
  :global {
    .ant-radio-group {
      box-shadow: 0 1px 1px 0 rgb(0 0 0 / 8%);
      white-space: nowrap;
    }

    .ant-radio-group-solid
      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: #008cff;
      background: #d9eeff;
    }

    .ant-radio-group-small .ant-radio-button-wrapper {
      color: #888b98;
      font-size: 12px;
      padding: 0 10px;
    }
  }
}

.body {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.bodyTop {
  text-align: center;
  padding-top: 36px;
}

.collectionPrice {
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0;
  font-family: D-DIN, sans-serif;
  background: linear-gradient(180deg, #fffeb9 0%, #ffd55e 100%);
  /* stylelint-disable */
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.collectionPayment,
.rankItemTitle {
  color: #fff;
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  font-family: '苹方-简', sans-serif;
  opacity: 0.8;
}

.bodyBottom {
  width: 100%;
}

.rankItem {
  display: flex;
  margin-bottom: 6px;
  justify-content: space-between;
  align-items: center;
}

.rankItemPrice {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  line-height: normal;
  font-family: D-DIN, sans-serif;
}

.price {
  font-family: D-DIN, sans-serif;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  text-align: center;
  letter-spacing: 0;
}

.small {
  padding: 10px;

  .headerRight {
    display: flex;
    align-items: center;

    :global {
      .ant-radio-button-wrapper {
        text-align: center;
        font-size: 6px;
        padding: 0 8px;
        height: 14px;
        width: 34px;
        line-height: 14px;
      }
    }
  }

  .bodyTop {
    padding-top: 18px;
  }

  .collectionPrice {
    font-size: 12px;
    font-weight: bold;
    line-height: 20px;
  }

  .collectionPayment,
  .rankItemTitle {
    color: #fff;
    font-size: 7px;
    font-weight: normal;
    line-height: 12px;
    font-family: '苹方-简', sans-serif;
    opacity: 0.8;
  }

  .rankItemPrice {
    color: #fff;
    font-size: 7px;
    font-weight: bold;
    line-height: 12px;
    font-family: D-DIN, sans-serif;
  }
}
