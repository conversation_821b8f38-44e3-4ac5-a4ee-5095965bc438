.cart {
  &Title {
    color: #040919;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: normal;
  }

  &MoreContent {
    font-size: 14px;
    display: flex;
    line-height: 20px;
    margin-left: auto;
    align-items: center;
    justify-content: center;
  }

  &Header {
    display: flex;
    border-bottom: #f2f2f2 solid 1px;
    align-items: center;
    height: 49px;
    padding: 0 1.2rem;
  }

  &Container {
    background: #fff;
    width: 100%;
    height: 100%;
  }

  &TitleContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  &Main {
    width: 100%;
    padding: 12px 22px;
  }

  &MainTitle {
    color: #333;
    font-size: 0.9rem;
    font-weight: bold;
    display: block;
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    flex: 1;
  }

  &Icon {
    width: 1.8rem;
    height: 1.8rem;
  }

  &Time {
    color: #008cff;
    border-radius: 2px;
    font-size: 0.8rem;
    background-color: #ddedff;
    padding: 0.2rem 0.4rem;
  }
}

// 小卡片尺寸
._324 {
  .cart {
    &Icon {
      width: 1rem;
      height: 1rem;
    }

    &Header {
      height: 38px;
    }

    &Title {
      font-size: 10px;
    }

    &TitleContainer {
      gap: 0.3rem;
    }

    &Main {
      padding: 9px 20px;
    }

    &MoreContent {
      font-size: 10px;
      line-height: 16px;

      :global {
        .echos-icon {
          font-size: 14px !important;
        }
      }
    }
  }
}
