import { useTranslation } from 'react-i18next';
import Icon from '@echronos/echos-icon';
import React from 'react';
import classNames from 'classnames';
import styles from './cart-title.module.less';

interface CartProp {
  title: string;
  url: string;
  type: 'ProcurementNtion' | 'VendorRecruitment' | 'IntentSource';
  children: React.ReactNode;
  isSmall?: boolean;
  size?: number;
}
// 卡片图标
function Cart({ title, url, type, children, isSmall, size }: CartProp) {
  const { t } = useTranslation();

  return (
    <section className={classNames(styles.cartContainer, { [styles[`_${size}`]]: isSmall })}>
      <a
        className={styles.cartHeader}
        href={`${import.meta.env.BIZ_UM_SITE}/um/${url}`}
        target="_blank"
        rel="noreferrer"
        onMouseDown={(e) => {
          e.preventDefault();
        }}
      >
        <div className={styles.cartTitleContainer}>
          {type === 'IntentSource' && (
            <img
              className={styles.cartIcon}
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1728633789622369.png`}
              alt="优盟意向寻源"
            />
          )}
          {type === 'VendorRecruitment' && (
            <img
              className={styles.cartIcon}
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/172863375578354.png`}
              alt="优盟供应商招募"
            />
          )}
          {type === 'ProcurementNtion' && (
            <img
              className={styles.cartIcon}
              src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1728633735201842.png`}
              alt="优盟采购公告"
            />
          )}
          <span className={styles.cartTitle}>{title}</span>
        </div>
        <p className={styles.cartMoreContent}>
          <span style={{ color: '#14317A' }}>{t('desk_uoumCardTitle_viewMore')}</span>
          <Icon name="right_arrow_line" size={20} color="#14317A" />
        </p>
      </a>
      <main className={styles.cartMain}>{children}</main>
    </section>
  );
}

Cart.defaultProps = {
  isSmall: false,
  size: 324,
};

export default Cart;
