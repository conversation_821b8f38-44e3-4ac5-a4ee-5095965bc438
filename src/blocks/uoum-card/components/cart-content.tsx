import { useTranslation } from 'react-i18next';
import { formatTime } from '@/pages/home/<USER>/utils';
import classNames from 'classnames';
import type { ProcurementNtionProp } from '../api/get-uoum-procurement-notice';
import styles from './cart-content.module.less';
import SendTime from './send-time';

interface CartContentPorps {
  data: ProcurementNtionProp[];
  type: 'notion' | 'source';
  url: string;
  isSmall?: boolean;
  size?: number;
}

function CartContent({ data, type, url, isSmall, size }: CartContentPorps) {
  const { t } = useTranslation();

  return (
    <div className={classNames(styles.cartMainContent, { [styles[`_${size}`]]: isSmall })}>
      {data &&
        data.map((item) => (
          <div className={styles.cartItem} key={item.id}>
            <a
              className={styles.cartTitle}
              href={`${import.meta.env.BIZ_UM_SITE}/um/${url}/${item.id}`}
              target="_blank"
              rel="noreferrer"
            >
              {item.title}
            </a>
            <div className={styles.cartTime}>
              {item.endTime > new Date().getTime() ? (
                <p className={styles.cartTimeText}>
                  {t('desk_uoumCard_inProgress')} | {formatTime(item.endTime, true)}{' '}
                  {t('desk_uoumCard_deadline')}
                </p>
              ) : (
                <p className={styles.cartTimeOutText}>
                  {t('desk_uoumCard_ended')} | {t('desk_uoumCard_timeExpired')}
                </p>
              )}
              {type === 'notion' && (
                <>
                  {item.categoryList.map((category) => (
                    <span className={styles.cartTap} key={category.id}>
                      {category.name}
                    </span>
                  ))}
                </>
              )}
            </div>
            <div className={styles.cartMessageContent}>
              <p
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '4px',
                }}
              >
                <img
                  src={`${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1728633885938167.png`}
                  alt={t('desk_uoumCard_company')}
                  style={{ width: '16px', height: '16px' }}
                />
                <span className={styles.cartCompanyName}>{item.companyName}</span>
              </p>
              <SendTime time={item.issuedTime} />
            </div>
          </div>
        ))}
    </div>
  );
}

CartContent.defaultProps = {
  isSmall: false,
  size: 324,
};

export default CartContent;
