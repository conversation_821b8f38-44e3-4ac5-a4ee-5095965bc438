// eslint-disable-next-line import/no-cycle
import { Grid } from './grid-layout';

let dragGrid: Grid | null = null;

// 是否在拖拽状态
let isDraging = false;

// 是否在拖拽状态的移动
let isDragMoving = false;

// 设置拖拽状态的定时器
let setDragStatusTimer: any = null;

let gridCopyEle: HTMLDivElement | null = null;

// 鼠标位置距离网格原点的位置
let rectFromTarget = { top: 0, left: 0 };

export const setDragGrid = (grid: Grid | null) => {
  dragGrid = grid;
};

export const getDragGrid = () => dragGrid;

export const updateDragStatus = (
  status: boolean,
  updateDragStatusTime: number,
  // eslint-disable-next-line no-shadow, no-unused-vars
  cb?: (status: boolean) => void
) => {
  if (status) {
    setDragStatusTimer = setTimeout(() => {
      isDraging = true;
      if (cb) {
        cb(isDraging);
      }
    }, updateDragStatusTime);
  } else {
    if (setDragStatusTimer) {
      clearTimeout(setDragStatusTimer);
    }
    isDraging = false;
    if (cb) {
      cb(isDraging);
    }
  }
};

export const getDragStatus = () => isDraging;

export const setMovingStatus = (status: boolean) => {
  isDragMoving = status;
};

export const getMovingStatus = () => isDragMoving;

export const setGridCopyEle = (ele: HTMLDivElement | null) => {
  gridCopyEle = ele;
};

export const getGridCopyEle = () => gridCopyEle;

export const setRectFromTarget = (rect: { top: number; left: number }) => {
  rectFromTarget = rect;
};

export const getRectFromTarget = () => rectFromTarget;

export default class DragService {
  dragStopTimer: any = null;

  // 缓存移动之前的网格布局数据
  cacheBeforeMovingGrids: Grid[] = [];

  // 布局容器的rect
  layoutContainerRect = null as unknown as DOMRect;

  setDragStopTimer = (timer: any) => {
    this.dragStopTimer = timer;
  };

  getDragStopTimer = () => this.dragStopTimer;

  setCacheBeforeMovingGrids = (grids: Grid[]) => {
    this.cacheBeforeMovingGrids = grids;
  };

  getCacheBeforeMovingGrids = () => this.cacheBeforeMovingGrids;

  setLayoutContainerRect = (rect: DOMRect) => {
    this.layoutContainerRect = rect;
  };

  getLayoutContainerRect = () => this.layoutContainerRect;
}
