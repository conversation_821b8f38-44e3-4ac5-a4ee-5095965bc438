import { HTMLAttributes, PropsWithChildren } from 'react';
import styles from './app-card.module.less';

export interface AppCardProps extends HTMLAttributes<HTMLDivElement> {
  isAdded: boolean;
  add: () => void;
}

function AppCard({ children, isAdded, add, ...props }: PropsWithChildren<AppCardProps>) {
  return (
    <div className={styles.appCard} {...props}>
      {children}
      {isAdded ? (
        <div className={styles.added} />
      ) : (
        // eslint-disable-next-line jsx-a11y/control-has-associated-label
        <div
          className={styles.addBtn}
          role="button"
          tabIndex={0}
          onClick={(e) => {
            e.stopPropagation();
            add();
          }}
        />
      )}
    </div>
  );
}

export default AppCard;
