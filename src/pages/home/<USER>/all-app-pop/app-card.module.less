.appCard {
  display: flex;
  width: 154px;
  max-width: 100%;
  height: 130px;
  margin: 0 8px 16px;
  justify-content: center;
  float: left;
  position: relative;
  border-radius: 18px;
  background: rgb(255 255 255 / 50%);
  backdrop-filter: blur(25px);
  align-items: center;
  cursor: pointer;

  &:hover {
    box-shadow: -8px 8px 24px 0 rgb(0 0 0 / 16%);
  }

  .addBtn {
    width: 32px;
    height: 18px;
    position: absolute;
    top: 8px;
    right: 8px;
    border-radius: 9px;
    background: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689838539303851.png');

    &:hover {
      background: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689838589560696.png');
    }
  }

  .added {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 74px;
    right: 38px;
    border: 1px solid #fff;
    border-radius: 50%;
    background-size: 100% 100%;
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689841346402472.png');
  }
}
