import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON>emo, useState } from 'react';
import { Button, Col, Input, Row } from 'antd';
import { useRequest, useThrottleFn } from 'ahooks';
import { useTranslation } from 'react-i18next';
import { useUserinfo } from '@echronos/react';
import { CLIENT, uuid } from '@echronos/core';
import { IApp, getDesktopAllApp, addOrRemoveApp } from '@/apis';
import EmptyTips from '@/components/empty-tips/empty-tips';
import Left from '@/components/icon/left';
import Search from '@/components/icon/search';
import SpinBox from '@/components/spin-box';
import { message } from '@/components/message';
import addCustomApp from '@/containers/custom-app';
import useToApp from '@/hooks/use-to-app';
import { verifyApp } from '../../utils/verify';
import DrawerPop from '../drawer/drawer';
import AppCard from './app-card';
import App from '../apps/application/application';
import styles from './all-app-pop.module.less';

export interface AllAppPopProps {
  isGuide: boolean;
  close: () => void;
  closeGuide?: () => void;
  // eslint-disable-next-line no-unused-vars
  added: (id: number) => void;
}

function Guide({ close }: Pick<AllAppPopProps, 'close'>) {
  // 多语言导入
  const { t } = useTranslation();
  return (
    <div className={styles.guide}>
      <img
        className={styles.guideImg}
        src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023817/1692265355980870.png"
        alt=""
      />
      <div role="button" tabIndex={0} className={styles.knowBtn} onClick={close}>
        {t('desk_allApp_know')}
      </div>
    </div>
  );
}

function AllAppPopContent({ isGuide, close, closeGuide, added }: AllAppPopProps) {
  const [isClodeGuide, setIsClodeGuide] = useState(false);
  // const [apps, setApps] = useState<IApp[]>(null as unknown as IApp[]);
  const userInfo = useUserinfo();
  const [toApp] = useToApp();
  const {
    data: apps = [],
    loading,
    run,
    refresh,
  } = useRequest(
    (search: string) =>
      getDesktopAllApp({ search }).then(
        (res) => res?.list.filter((item) => item.coding !== 'ech4yi0e5a9bjg917o') || []
      ),
    {
      defaultParams: [''],
      debounceWait: 500,
    }
  );

  // 多语言导入
  const { t } = useTranslation();
  const showGuide = useMemo(() => isGuide && !isClodeGuide, [isGuide, isClodeGuide]);
  const onAddCustomApp: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      addCustomApp().then(() => {
        close();
        if (CLIENT) {
          window.microApp?.setGlobalData({ type: 'desktop.reload' });
        }
      });
      e.stopPropagation();
    },
    [close]
  );
  const onSearch: ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      run(e.target.value);
    },
    [run]
  );

  const { run: addApp } = useThrottleFn(
    (item: IApp) => {
      addOrRemoveApp({
        classify: item.classify === 3 ? 3 : undefined,
        appId: item.classify === 3 ? String(item.echAppId) : undefined,
        funcId: item.id,
        flag: 0,
      }).then((res) => {
        message.success(`${t('desk_allApp_add')}`);
        refresh();
        added(res.id);
      });
    },
    { wait: 2500, trailing: false }
  );

  return (
    <SpinBox loading={loading}>
      <div
        className={styles.allAppPopContent}
        onContextMenuCapture={(e) => {
          e.preventDefault();
        }}
      >
        <div className={styles.header}>
          <Left size={18} className={styles.back} onClick={close} />
          <Button type="link" size="small" className={styles.customAdd} onClick={onAddCustomApp}>
            {t('desk_allApp_cusAdd')}
          </Button>
          <span className={styles.title}>{t('')}</span>
        </div>
        <div className={styles.searchBox}>
          <Input
            prefix={<Search className={styles.searchIcon} />}
            allowClear
            // suffix={
            //   searchWords && <Icon name="close-circle" className={styles.closeIcon} onClick={clear} />
            // }
            placeholder={t('public_search')}
            bordered={false}
            className={styles.searchIpt}
            onChange={onSearch}
            size="large"
          />
        </div>
        <div className={styles.body}>
          <Row>
            {apps &&
              apps.map((item) => (
                <Col key={uuid()} span={12}>
                  <AppCard
                    isAdded={item.exist === 1}
                    onClick={() => {
                      if (!verifyApp(item)) {
                        return;
                      }
                      toApp(item.url, {
                        id: item.id,
                        icon: item.photo,
                        name: item.name,
                        code: item.coding,
                        isOpen: item.isOpen,
                        externalApp: item.externalApp,
                        shopId: userInfo.shop.id,
                        classify: item.classify,
                        url: item.url,
                      });
                    }}
                    add={() => {
                      addApp(item);
                    }}
                  >
                    <App data={item} isDeskTop={false} />
                  </AppCard>
                </Col>
              ))}
          </Row>
          {apps && apps.length === 0 && (
            <EmptyTips tipsTitle={t('desk_allApp_noTitle')} tipsText={t('desk_allApp_noText')} />
          )}
        </div>

        {showGuide && (
          <Guide
            close={() => {
              if (closeGuide) {
                closeGuide();
              }
              setIsClodeGuide(true);
            }}
          />
        )}
      </div>
    </SpinBox>
  );
}

AllAppPopContent.defaultProps = {
  closeGuide: () => {},
};

function AllAppPop() {
  function open({
    isGuide,
    added,
    closeGuide,
  }: {
    isGuide: boolean;
    // eslint-disable-next-line no-unused-vars
    added: (id: number) => void;
    closeGuide?: () => void;
  }) {
    const instance = DrawerPop();
    // @ts-ignore
    instance.open({
      mask: true,
      maskStyle: {
        background: 'transparent',
      },
      onCancel: () => {
        instance.close();
        if (closeGuide) {
          closeGuide();
        }
      },
      content: (
        <AllAppPopContent
          isGuide={isGuide}
          closeGuide={closeGuide}
          added={added}
          close={() => {
            instance.close();
            if (closeGuide) {
              closeGuide();
            }
          }}
        />
      ),
    });
  }
  return {
    open,
  };
}

export default AllAppPop;
