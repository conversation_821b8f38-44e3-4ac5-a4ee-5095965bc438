@import '@echronos/react/less/index';

.allAppPopContent {
  display: flex;
  flex-direction: column;
  height: 100%;
  // position: relative;

  .title {
    color: #040919;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }

  .searchBox {
    margin-bottom: 8px;
    padding: 0 24px;
  }

  .searchIpt {
    border-radius: 18px;
    background: rgb(118 118 128 / 12%);
    font-size: 14px;

    :global {
      .ant-input {
        background: none;

        &::placeholder {
          color: #888b98;
        }
      }

      .ant-input-lg {
        font-size: 14px;
      }
    }
  }

  .body {
    margin-bottom: 16px;
    padding: 8px 16px 0;
    overflow: auto;
    flex: 1;
  }

  .searchIcon {
    color: #999eb2;
    margin-right: 2px;
  }

  .closeIcon {
    color: rgb(0 0 0 / 30%);
    cursor: pointer;
  }

  .guide {
    width: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    bottom: 0;
  }

  .guideImg {
    width: 100%;
    max-height: fit-content;
  }

  .knowBtn {
    color: #fff;
    font-size: 14px;
    width: 229px;
    height: 38px;
    line-height: 38px;
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translate(-50%);
    border-radius: 10px;
    background: linear-gradient(261deg, #00c6ff 0%, #008cff 100%);
    text-align: center;
    cursor: pointer;
  }
}

.header {
  padding: 25px 20px;
  overflow: hidden;
  text-align: center;
  position: relative;
}

.back {
  color: #999eb2;
  font-size: 24px;
  left: 15px;
}

.customAdd {
  font-size: @font-size-base;
  min-width: auto;
  padding: 2px 0;
  right: 15px;
}

.back,
.customAdd {
  position: absolute;
  top: 15px;
}
