import Modal from '@/components/modal';
import ModalContent from './modal-content';
import styles from './all-card.modal.module.less';

function AllCardModal() {
  // eslint-disable-next-line no-unused-vars
  function open({ added }: { added: (id: number) => void }) {
    const { open: open$, close } = Modal.getInstance();

    open$({
      content: <ModalContent added={added} close={close} />,
      centered: true,
      mask: false,
      footer: '',
      title: '',
      width: 1000,
      closable: false,
      wrapClassName: styles.wrapper,
      onCancel: close,
    });
  }
  return {
    open,
  };
}

export default AllCardModal;
