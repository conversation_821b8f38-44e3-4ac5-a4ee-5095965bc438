.container {
  display: flex;
  flex-direction: row;
  backdrop-filter: blur(25px);
  background: rgb(255 255 255 / 60%);

  .listBox {
    padding: 20px 17px 22px 16px;
  }

  .contentBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: rgb(255 255 255 / 50%);
    padding: 20px 24px 24px;
  }
}

.listBoxTitle {
  user-select: none;

  .item {
    font-size: 16px;
    font-weight: 500;
    height: 24px;
    margin-left: 13px;
  }
}

.listBoxDivider {
  width: 100%;
  margin-top: 12px;
  margin-bottom: 12px;
  padding-right: 8px;
  padding-left: 6px;

  .divider {
    border-bottom: 1px solid rgb(0 0 0 / 10%);
  }
}

.listBoxList {
  height: 568px;
  overflow: hidden;
}

.contentBoxTop {
  display: flex;
  width: 100%;
  height: 38px;
  line-height: 38px;
  justify-content: space-between;

  .searchBox {
    .searchIpt {
      border-radius: 18px;
      background: #f3f3f3;
      font-size: 14px;

      &:hover {
        background: rgb(255 255 255 / 50%);
        box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
      }

      :global {
        .ant-input {
          background: none;

          &::placeholder {
            color: #888b98;
          }
        }

        .ant-input-lg {
          font-size: 14px;
        }
      }
    }
  }

  .right {
    color: #008cff;
    display: flex;
    gap: 15px;

    &:hover {
      color: #006eff;
    }

    .more {
      display: flex;
      justify-content: center;
      cursor: pointer;
      gap: 4px;
    }

    .close {
      cursor: pointer;
    }
  }
}

.contentBoxDesTitle {
  color: rgb(4 9 25 / 60%);
  font-size: 12px;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  margin-top: 29px;
  margin-bottom: 20px;
}

// .hidden {
//   opacity: 0;
// }

.contentBoxContent {
  height: 516px;
  max-height: 516px;
  overflow: hidden auto;
}

.searchBg {
  background-image: url('https://static.huahuabiz.com/static/img/1723095616599268.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
