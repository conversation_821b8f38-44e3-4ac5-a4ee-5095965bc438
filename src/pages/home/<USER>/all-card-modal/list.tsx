import styles from './list.module.less';

interface ItemType {
  appName: string;
  iconUrl: string;
}

interface ListProps {
  current: string;
  onClick: SimpleParamFn<string>;
  data: ItemType[];
}

function List({ current, onClick, data }: ListProps) {
  return (
    <div className={styles.container}>
      {data.map(({ appName, iconUrl }) => (
        <div
          tabIndex={0}
          key={appName}
          role="button"
          className={`${styles.box} ${appName === current ? styles.action : ''}`}
          onClick={() => onClick(appName)}
        >
          <div className={styles.icon}>
            <img src={iconUrl} alt="图标" />
          </div>
          <div className={styles.name}>{appName}</div>
        </div>
      ))}
    </div>
  );
}

export default List;
