.container {
  display: flex;
  width: 221px;
  height: 236px;
  padding: 14px;
  justify-content: center;
  box-sizing: border-box;
  flex-direction: column;
  align-items: center;

  .imgbox {
    display: flex;
    width: 200px;
    height: 100px;
    justify-content: center;
    align-items: center;

    .imgCard {
      position: relative;
    }

    .img {
      display: block;
      max-width: 200px;
      max-height: 100px;
      object-fit: cover;
      -webkit-user-drag: none;
      border-radius: 7.03px;
      box-shadow: 0 1px 1px 0 rgb(5 6 52 / 10%), 0 4px 12px 0 rgb(0 7 55 / 8%);
    }

    .addIconBox {
      display: flex;
      width: 20px;
      height: 20px;
      justify-content: center;
      position: absolute;
      top: -9px;
      left: -9px;
      z-index: 10;
      align-items: center;
      border-radius: 50%;
      background-color: #05d380;
      cursor: pointer;
    }
  }

  .tilte {
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    margin-top: 16px;
    text-align: center;
  }

  .brief {
    color: #888b98;
    font-size: 12px;
    display: -webkit-box;
    height: 40px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
