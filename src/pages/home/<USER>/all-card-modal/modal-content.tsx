import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Col, Row } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { Input } from 'antd';
import classNames from 'classnames';
import Search from '@/components/icon/search';
import { useNavigation } from '@/hooks';
import SpinBox from '@/components/spin-box';
import { Card, getDesktopAllCard } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './modal-content.module.less';
import List from './list';
import CardContainer from './card-container';
import Empty from './empty';

interface ModalContentProps {
  // eslint-disable-next-line no-unused-vars
  added: (id: number) => void;
  close: SimpleParamFn;
}

function ModalContent({ added, close }: ModalContentProps) {
  const [cards, setCards] = useState<Card[]>([]);
  const [search, setSearch] = useState('');
  const [currentName, setCurrentName] = useState('');
  const navigate = useNavigation();
  // 多语言导入
  const { t } = useTranslation();
  const { run: getCards, loading } = useRequest(getDesktopAllCard, {
    manual: true,
    debounceWait: 200,
    onSuccess: ({ list }) => {
      if (search.trim()) {
        const searchItem: Card = {
          appName: t('desk_allApp_all'),
          iconUrl: cards[0].iconUrl,
          blockList: [],
          appId: '',
        };
        list.forEach((card) => {
          searchItem.blockList.push(...card.blockList);
        });
        setCards([searchItem, ...list]);
        setCurrentName(searchItem?.appName);
      } else {
        setCards(list);
        setCurrentName(list[0]?.appName);
      }
    },
  });

  const setListName = (name: string) => {
    if (name !== currentName) {
      setCurrentName(name);
    }
  };

  const getDes = () => {
    const current = cards.find((card) => card.appName === currentName);
    const des =
      currentName === `${t('desk_allApp_Widget')}` ? `${t('desk_allApp_get')}` : currentName;

    if (current?.appName === `${t('desk_allApp_all')}` && !current.blockList.length) {
      return '';
    }
    return `${des} ${
      current?.appName === `${t('desk_allApp_all')}`
        ? `(${current.blockList.length}${t('desk_allApp_all')})`
        : ''
    }`;
  };

  useEffect(() => {
    getCards({ search });
  }, [getCards, search]);

  return (
    <div className={styles.container}>
      <div className={styles.listBox}>
        <div className={styles.listBoxTitle}>
          <div className={styles.item}>{t('desk_addCard')}</div>
        </div>
        <div className={styles.listBoxDivider}>
          <div className={styles.divider} />
        </div>

        <div className={styles.listBoxList}>
          <List current={currentName} onClick={(name) => setListName(name)} data={cards} />
        </div>
      </div>
      <div
        className={classNames(
          styles.contentBox,
          search.trim() &&
            !cards.find((e) => e.appName === currentName)?.blockList.length &&
            styles.searchBg
        )}
      >
        <div className={styles.contentBoxTop}>
          <div className={styles.searchBox}>
            <Input
              prefix={<Search className={styles.searchIcon} />}
              allowClear
              placeholder={t('desk_allApp_searchWid')}
              bordered={false}
              value={search}
              className={styles.searchIpt}
              onChange={(value) => setSearch(value.target.value)}
              size="large"
            />
          </div>
          <div className={styles.right}>
            <div
              tabIndex={0}
              role="button"
              className={styles.more}
              onClick={() => {
                close();
                navigate('/appstore');
              }}
            >
              <Icon name="more_categories_line" size={16} />
              <div>{t('desk_allApp_moreApp')}</div>
            </div>
            <Icon
              className={styles.close}
              name="close_line"
              size={20}
              color="#999EB2"
              onClick={() => close()}
            />
          </div>
        </div>
        <div className={styles.contentBoxDesTitle}>{getDes()}</div>
        <div className={classNames(styles.contentBoxContent)}>
          <SpinBox loading={loading}>
            {cards.find((e) => e.appName === currentName)?.blockList.length ? (
              <Row gutter={[44, 44]}>
                {cards
                  .find((e) => e.appName === currentName)
                  ?.blockList.map((item) => (
                    <Col span={8} key={item.id}>
                      <CardContainer
                        added={added}
                        id={String(item.id)}
                        blockName={item.blockName as string}
                        brief={item.brief as string}
                        backgroundImage={item.backgroundImage}
                      />
                    </Col>
                  ))}
              </Row>
            ) : (
              <Empty />
            )}
          </SpinBox>
        </div>
      </div>
    </div>
  );
}

export default ModalContent;
