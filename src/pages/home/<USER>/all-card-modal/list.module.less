@border-color: rgba(0, 140, 255, 0.5);
@shadow: 0px 2px 6px 0px rgba(21, 72, 191, 0.1), inset 0px 0px 3px 0px #ffffff;
@bg-color: rgba(255, 255, 255, 0.5);

.container {
  display: flex;
  width: 168px;
  height: 100%;
  overflow-y: scroll;
  flex-direction: column;
  gap: 8px;

  &::-webkit-scrollbar {
    display: none;
  }

  .box {
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    width: 168px;
    height: 40px;
    border-radius: 10px;
    cursor: pointer;
    border: 1px solid transparent;

    .icon {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      margin-left: 7px;
      overflow: hidden;
      border-radius: 10px;

      img {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    .name {
      flex: 1;
      line-height: 22px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &:hover {
      border: 1px solid @border-color !important;
      box-shadow: @shadow;
      background-color: @bg-color;
    }
  }
}

.action {
  border: 1px solid @border-color !important;
  box-shadow: @shadow;
  background-color: @bg-color;
}
