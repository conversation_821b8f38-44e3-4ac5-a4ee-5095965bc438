import Icon from '@echronos/echos-icon';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Tooltip } from '@echronos/antd';
import { message } from '@/components/message';
import { addOrRemoveApp } from '@/apis';
import styles from './card-container.module.less';

interface CardContainerProps {
  id: string;
  blockName: string;
  brief: string;
  backgroundImage: string;
  // eslint-disable-next-line no-unused-vars
  added: (addEdId: number) => void;
}

function CardContainer({ id, blockName, brief, backgroundImage, added }: CardContainerProps) {
  // 多语言导入
  const { t } = useTranslation();
  const addCrd = () => {
    addOrRemoveApp({ funcId: Number(id), flag: 0, classify: 4 }).then((res) => {
      message.success(`${t('desk_allApp_adds')}`);
      added(res.id);
    });
  };

  const [show, setShow] = useState(false);

  return (
    <div className={styles.container}>
      <div className={styles.imgbox}>
        <div
          className={styles.imgCard}
          role="button"
          tabIndex={0}
          onClick={addCrd}
          onMouseEnter={() => {
            setShow(true);
          }}
          onMouseLeave={() => {
            setShow(false);
          }}
        >
          <img className={styles.img} src={backgroundImage} alt="小卡片" />
          {show ? (
            <div className={styles.addIconBox}>
              <Icon name="add_line" size={16} className={styles.add} color="white" />
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles.tilte}>{blockName}</div>
      <Tooltip title={brief} overlayStyle={{ maxWidth: 224 }}>
        <div className={styles.brief}>{brief}</div>
      </Tooltip>
    </div>
  );
}

export default CardContainer;
