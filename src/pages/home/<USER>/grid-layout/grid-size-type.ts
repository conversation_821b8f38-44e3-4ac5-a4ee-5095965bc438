import { GridSizeType } from '../../types';

const gridSize: Record<GridSizeType, [number, number]> = {
  s: [1, 1],
  m: [1, 2],
  l: [2, 2],
  xl: [3, 2],
  xxl: [4, 2],
  xxxl: [6, 2],
};

const mobileGridSize: Record<GridSizeType, [number, number]> = {
  s: [1, 1],
  m: [2, 4],
  l: [4, 4],
  xl: [4, 4],
  xxl: [4, 4],
  xxxl: [4, 4],
};

const getGridSize = (size: GridSizeType, containerWidth: number) => {
  if (containerWidth < 560) {
    return mobileGridSize[size];
  }
  if (containerWidth < 840) {
    return { ...gridSize, xxxl: [4, 2] }[size];
  }
  return gridSize[size];
};

export default getGridSize;
