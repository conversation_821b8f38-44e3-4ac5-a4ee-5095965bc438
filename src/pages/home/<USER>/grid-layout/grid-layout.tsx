import { isArray } from 'lodash';
import {
  useEffect,
  PropsWithChildren,
  RefAttributes,
  useRef,
  useState,
  cloneElement,
  ReactNode,
} from 'react';
import cloneDeep from 'lodash/cloneDeep';
import { Layout, ViewCols } from '../../types';
import { isCanLocate } from './utils';
import { getGridLayoutSiteByEle, getColsCount, getGridSize } from '../../utils/utils';
import './grid-layout.less';

interface GridLayoutProps extends RefAttributes<unknown> {
  // 分割的列数
  cols: ViewCols;
  layouts: Layout[];
}

interface LayoutExPostion extends Layout {
  left?: number;
  top?: number;
}

function GridLayout({ children, cols, layouts }: PropsWithChildren<GridLayoutProps>) {
  const gridLayoutEl = useRef(null as unknown as HTMLDivElement);
  const [containerWidth, setContainerWidth] = useState(0);
  const [renderChildren, setRenderChildren] = useState<ReactNode>(null);

  const gridLayoutSite = useRef<Array<Array<string>>>([]);

  const recordGridLayoutSite = (startSite: [number, number], lay: Layout) => {
    for (let i = 0; i < lay.h; i += 1) {
      gridLayoutSite.current[startSite[0] + i].splice(
        startSite[1],
        lay.w,
        ...new Array(Number(lay.w)).fill(lay.id)
      );
    }
  };

  // 计算每个卡片网格坐标
  const computeGridLayoutSiteByLayouts = (lays: Layout[]) => {
    const layoutsExPostion: LayoutExPostion[] = cloneDeep(lays);

    layoutsExPostion.forEach((item) => {
      for (let i = 0, l = gridLayoutSite.current.length; i < l; i += 1) {
        let flag = false;
        for (let x = 0, y = gridLayoutSite.current[i].length; x < y; x += 1) {
          if (gridLayoutSite.current[i][x] === 'null') {
            if (isCanLocate(gridLayoutSite.current, item, [i, x])) {
              const gridSize = getGridSize(gridLayoutEl.current.clientWidth);
              // eslint-disable-next-line no-param-reassign
              item.top = i * gridSize.h;
              // eslint-disable-next-line no-param-reassign
              item.left = x * gridSize.w;
              flag = true;
              recordGridLayoutSite([i, x], item);
              break;
            }
          }
        }
        if (flag) {
          break;
        }
      }
    });
    return layoutsExPostion;
  };

  const createGridStyle = (lay: LayoutExPostion) => {
    const gridSize = getGridSize(gridLayoutEl.current.clientWidth);
    return {
      position: 'absolute',
      width: `${lay.w * gridSize.w}px`,
      height: `${lay.h * gridSize.h}px`,
      top: `${lay.top || 0}px`,
      left: `${lay.left || 0}px`,
    };
  };

  useEffect(() => {
    setTimeout(() => {
      const gridSize = getGridSize(gridLayoutEl.current.clientWidth);
      setContainerWidth(getColsCount(gridLayoutEl.current.clientWidth, cols) * gridSize.w);

      gridLayoutSite.current = getGridLayoutSiteByEle(gridLayoutEl.current, cols);

      const layoutsIdMap: Record<string, LayoutExPostion> = {};
      computeGridLayoutSiteByLayouts(layouts).forEach((item) => {
        layoutsIdMap[item.id] = item;
      });

      if (isArray(children)) {
        const arr = children.map((element) =>
          cloneElement(element, {
            style: createGridStyle(layoutsIdMap[element.props.id]),
            key: element.props.id,
          })
        );

        setRenderChildren(arr);
      }

      if (typeof children === 'string') {
        setRenderChildren(children);
      }
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [layouts]);

  return (
    <div className="grid-layout-container" ref={gridLayoutEl}>
      <div
        className="container"
        style={{
          width: `${containerWidth}px`,
        }}
      >
        {renderChildren}
      </div>
    </div>
  );
}

export default GridLayout;
