import { Layout } from '../../types';

// 判断是否可以定位
export const isCanLocate = (
  currentGridLayoutSite: Array<Array<string>>,
  lay: Layout,
  site: [number, number]
) => {
  let arr: string[] = [];
  const x = site[0];
  const y = site[1];
  for (let i = 0, j = lay.h; i < j; i += 1) {
    if (currentGridLayoutSite[x + i]) {
      arr = arr.concat(currentGridLayoutSite[x + i].slice(y, y + lay.w));
    }
  }
  const isExitUnNull = arr.filter((str) => str !== 'null').length;
  if (arr.length !== lay.w * lay.h || isExitUnNull) {
    return false;
  }
  return true;
};

export const gradSize = 140;
