import { HTMLAttributes, useMemo, useRef } from 'react';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import { Popover } from 'antd';
import styles from './app.module.less';
import './app.less';

export interface ApplicationData {
  name: string;
  icon?: string;
  photo?: string;
  isCharge?: number | boolean;
  isPermission?: number | boolean;
  classify?: number;
  componentType?: string;
}

export interface ApplicationProps extends HTMLAttributes<HTMLDivElement> {
  data: ApplicationData;
  onMousemove?: () => void;
  isDeskTop?: boolean;
}

interface TipItem {
  icon: string;
  tips: string;
}

function Application({ data, isDeskTop, className, onMousemove, ...props }: ApplicationProps) {
  const { t } = useTranslation();
  const tipsMap = useRef<(TipItem | null)[]>([
    null,
    {
      icon: 'noPermission',
      // tips: '申请权限',
      tips: `${t('desk_apps_noPermission')}`,
    },
    {
      icon: 'noBuy',
      // tips: '过期请购买',
      tips: `${t('desk_apps_noBuy')}`,
    },
  ]);
  const tip = useMemo(() => {
    if (data.componentType !== 'app' || data.classify !== 2) {
      return tipsMap.current[(!data.isCharge && 2) || (!data.isPermission && 1) || 0];
    }
    return null;
  }, [data]);

  return (
    <div
      {...props}
      className={classNames(styles.appIcon, className, 'app-icon')}
      onMouseMove={onMousemove}
    >
      <div
        className={classNames(styles.imgBox, 'app-icon-img', 'allow-merge-card', {
          [styles.deskTopBoxShadow]: isDeskTop,
        })}
      >
        <img src={data.photo || data.icon} alt={data.name} className={styles.img} />
        {tip && (
          <div className={styles.tipsMask}>
            <div className={classNames(styles.tipsIcon, styles[tip.icon])} />
            <div className={styles.tipsText}>
              <Popover content={tip.tips}>
                <span
                  style={{
                    display: 'inline-block',
                    maxWidth: '100%', // 根据父容器宽度调整
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {tip.tips}
                </span>
              </Popover>
            </div>
          </div>
        )}
      </div>
      <div className={classNames(styles.name, 'name', { [styles.deskTopStyle]: isDeskTop })}>
        {data.name}
      </div>
    </div>
  );
}

Application.defaultProps = {
  isDeskTop: true,
  onMousemove: () => {},
};

export default Application;
