@import '@echronos/react/less/index';

.appIcon {
  display: inline-block;
  cursor: pointer;

  .tipsMask {
    display: none;
    justify-content: center;
    position: absolute;
    inset: 0;
    background: rgb(0 0 0 / 40%);
    border: 0.5px solid rgb(0 0 0 / 0%);
    backdrop-filter: blur(5px);
    align-items: center;
    flex-direction: column;
    cursor: pointer;
  }

  &:hover {
    .tipsMask {
      display: flex;
    }
  }
}

.imgBox {
  width: 68px;
  height: 68px;
  margin: 0 auto 2px;
  overflow: hidden;
  position: relative;
  border-radius: 16px;
}

.img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.name {
  color: #040919;
  font-weight: normal;
  width: 92px;
  line-height: 22px;
  margin: 0 auto;
  padding: 2px 4px;
  text-align: center;
  .text-overflow();
}

.deskTopBoxShadow {
  box-shadow: 0 4px 12px rgba(0 0 0 / 5%);
}

.deskTopStyle {
  color: #fff;
  text-shadow: 0 0 8px rgba(0 0 0 / 30%);
}

.tipsIcon {
  width: 20px;
  height: 20px;
  margin-bottom: 4px;
}

.noPermission {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689851982755421.png');
  background-size: 100% 100%;
}

.noBuy {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689852915033604.png');
  background-size: 100% 100%;
}

.tipsText {
  color: #fff;
  font-size: 12px;
  text-align: center;
}

:global {
  .m-mobile {
    .app-icon {
      .app-icon-img {
        width: 60px;
        height: 60px;
        margin: 0 auto 6px;
      }

      .name {
        font-size: 12px;
      }
    }
  }
}
