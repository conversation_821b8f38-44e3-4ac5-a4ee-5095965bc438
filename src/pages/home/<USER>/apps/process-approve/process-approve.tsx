import classnames from 'classnames';
import { HTMLAttributes, useEffect, useState, memo } from 'react';
import dayjs from 'dayjs';
import { CommentsInfo } from '@/apis';
import { useTranslation } from 'react-i18next';
import Steps from './steps';
import styles from './process-approve.module.less';
import './process-approve.less';

export interface ProcessApproveProps extends HTMLAttributes<HTMLDivElement> {
  data: {
    coding: string;
    componentType: string;
    content: {
      headImgUrl: string;
      intermediateNodeVo: CommentsInfo['intermediateNodeVo'];
      processNo: string;
      workflowEndTime: number;
      workflowMemberName: string;
      workflowProcessInstanceId: string;
      workflowProcessName: string;
      workflowStartTime: string;
      workflowStatus: number;
    }[];
    id: number;
    level: number;
    sort: number;
    type: number;
  };
}

interface TaskInfo {
  processName: string;
  createTime: string;
  nodeTitle: string;
  steps: {
    nodeTitle: string;
    nodeContent: string;
  }[];
}

function ProcessApprove({ data, ...props }: ProcessApproveProps) {
  const [task, setTask] = useState<TaskInfo>(null as unknown as TaskInfo);
  // 多语言导入
  const { t } = useTranslation();
  useEffect(() => {
    if (!data.content || !data.content[0]) {
      return;
    }
    const content = data.content[0];

    const steps = [];
    if (content.intermediateNodeVo.length > 0) {
      steps.push({
        nodeTitle: content.intermediateNodeVo[0].nodeTitle,
        nodeContent: content.intermediateNodeVo[0].staffInfoList[0].memberName,
      });
      const approveIndex = content.intermediateNodeVo.findIndex((item) => item.isArrive === 1);
      if (approveIndex >= 0) {
        steps.push({
          nodeTitle: content.intermediateNodeVo[approveIndex].nodeTitle,
          nodeContent: content.intermediateNodeVo[approveIndex].staffInfoList
            .map((item: { memberName: string }) => `${item.memberName}${t('desk_apps_aprv')}`)
            .join('、'),
        });
      }
      const next = approveIndex + 1;
      if (content.intermediateNodeVo[next]) {
        let checkPersonsStr = '';
        if (content.intermediateNodeVo[next]?.staffInfoList.length > 0) {
          if (content.intermediateNodeVo[next].staffInfoList.length > 1) {
            checkPersonsStr = `${content.intermediateNodeVo[2].staffInfoList[0].memberName}${t(
              'desk_apps_and'
            )}${content.intermediateNodeVo[next].staffInfoList.length}${t('desk_apps_person')}`;
          } else {
            checkPersonsStr = `${content.intermediateNodeVo[2].staffInfoList[0].memberName}`;
          }
        }
        steps.push({
          nodeTitle: content.intermediateNodeVo[next].nodeTitle,
          nodeContent: checkPersonsStr,
        });
      }
    }
    setTask({
      processName: content.workflowProcessName,
      createTime: dayjs(content.intermediateNodeVo[0].createTime).format(t('desk_apps_time')),
      nodeTitle: content.intermediateNodeVo[1].nodeTitle,
      steps,
    });
  }, [data]); //eslint-disable-line

  const empty = () => (
    <div className={styles.empty}>
      <div className={styles.emptyTitle}>
        <span className={styles.emptyIcon} />
        {t('desk_apps_nowEv')}
      </div>
      <div className={styles.emptyDes}>{t('desk_apps_emptyDes')}</div>
    </div>
  );

  return (
    <div className={classnames(styles.processApprove, 'm-process-approve')} {...props}>
      <div className={classnames(styles.header, 'header')}>
        <div className={classnames(styles.icon, 'icon')} />
        <span className={classnames(styles.title, 'title')}>{t('desk_apps_procAprv')}</span>
      </div>
      {task ? (
        <>
          <div className={styles.line}>
            <div className={classnames(styles.processTitle, 'process-title')}>
              {task.processName}
            </div>
            <div className={styles.status}>{t('desk_apps_status')}</div>
          </div>
          <div className={classnames(styles.time, 'time')}>
            {task.createTime}
            {t('public_submit')}
          </div>
          <Steps
            current={1}
            data={
              task.steps.length > 2
                ? [
                    {
                      content: `${task.steps[0].nodeTitle} ${task.steps[0].nodeContent}`,
                      id: 1,
                    },
                    {
                      content: (
                        <div>
                          <div className={classnames(styles.approveNode, 'approve-node')}>
                            {task.steps[1].nodeTitle}
                          </div>
                          <div className={classnames(styles.approvePerson, 'approve-person')}>
                            {task.steps[1].nodeContent}
                          </div>
                        </div>
                      ),
                      id: 2,
                    },
                    {
                      content: `${task.steps[2].nodeTitle} ${task.steps[2].nodeContent}`,
                      id: 3,
                    },
                  ]
                : [
                    {
                      content: `${task.steps[0].nodeTitle} ${task.steps[0].nodeContent}`,
                      id: 1,
                    },
                    {
                      content: (
                        <div>
                          <div className={classnames(styles.approveNode, 'approve-node')}>
                            {task.steps[1].nodeTitle}
                          </div>
                          <div className={classnames(styles.approvePerson, 'approve-person')}>
                            {task.steps[1].nodeContent}
                          </div>
                        </div>
                      ),
                      id: 2,
                    },
                  ]
            }
          />
        </>
      ) : (
        empty()
      )}
    </div>
  );
}

export default memo(ProcessApprove, () => true);
