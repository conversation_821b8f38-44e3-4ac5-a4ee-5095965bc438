import classNames from 'classnames';
import { ReactNode } from 'react';
import style from './steps.module.less';

export interface StepsProps {
  current: number;
  data: Array<{ content: ReactNode | string; id: number }>;
}

function Steps({ data, current }: StepsProps) {
  const getStatusClassName = (index: number) => {
    if (current > index) {
      return 'finish';
    }
    if (current === index) {
      return 'process';
    }
    return 'wait';
  };

  return (
    <div className={style.stepsBox}>
      {data.map((item, index) => (
        <div className={classNames(style.stepItem, 'step-item')} key={item.id}>
          <div className={style.colBox}>
            <div
              className={classNames(style.status, 'status-icon', style[getStatusClassName(index)])}
            />
            {index < data.length - 1 && (
              <div
                className={classNames(
                  style.dashedLine,
                  'dashed-line',
                  index < current && style.active
                )}
              />
            )}
          </div>
          <div className={classNames(style.colBox, 'col-box', style.contentBox)}>
            {item.content}
          </div>
        </div>
      ))}
    </div>
  );
}

export default Steps;
