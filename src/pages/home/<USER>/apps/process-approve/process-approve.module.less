@import '@/styles/mixins/mixins.less';

.processApprove {
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(25px);
  border-radius: 18px;
  padding: 12px;
  box-sizing: border-box;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0 0 0 / 5%);

  :global {
    .ant-steps.ant-steps-dot .ant-steps-item-content,
    :where(.css-dev-only-do-not-override-14wwjjs).ant-steps.ant-steps-dot.ant-steps-small
      .ant-steps-item-content {
      min-height: auto;
    }

    .ant-steps.ant-steps-small .ant-steps-item-description {
      font-size: 12px;
    }

    .ant-steps.ant-steps-vertical > .ant-steps-item {
      padding-bottom: 16px;
    }

    .ant-steps.ant-steps-vertical > .ant-steps-item .ant-steps-item-description {
      padding: 0;
    }
  }
}

.header {
  margin-bottom: 20px;
}

.icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690166758247902.png');
  background-size: 100% 100%;
}

.title {
  color: #040919;
  font-size: 14px;
  font-weight: 600;
  margin-left: 4px;
  vertical-align: super;
}

.line {
  display: flex;
  margin-bottom: 4px;
  justify-content: space-between;
}

.processTitle {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  max-width: 160px;
  .text-overflow();
}

.status {
  color: #f9ae08;
  font-size: 12px;
  line-height: 12px;
  padding: 4px;
  background: #fef3da;
  border-radius: 4px;
}

.time {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 16px;
}

.dot {
  width: 12px;
  height: 12px;
}

.finish {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181169298328.png');
  background-size: 100% 100%;
}

.process {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181239596297.png');
  background-size: 100% 100%;
}

.wait {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690181258334846.png');
  background-size: 100% 100%;
}

.approveNode {
  color: #040919;
  font-size: 14px;
  margin-bottom: 3px;
}

.approvePerson {
  color: #f9ae08;
}

.empty {
  margin-top: 70px;
}

.emptyIcon {
  display: inline-block;
  width: 28px;
  height: 24px;
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202383/1691050919843370.png');
  background-size: 100% 100%;
  vertical-align: middle;
}

.emptyTitle {
  color: #008cff;
  font-size: 16px;
  margin-bottom: 12px;
  text-align: center;
}

.emptyDes {
  color: #999eb2;
  font-size: 14px;
  text-align: center;
}
