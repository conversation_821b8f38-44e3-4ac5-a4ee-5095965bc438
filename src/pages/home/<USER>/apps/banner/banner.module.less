.banner {
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  position: relative;
  border-radius: 18px;
  background-repeat: no-repeat;
  background-position: left;
  background-size: cover;

  :global {
    .ant-carousel {
      overflow: hidden;
      border-radius: 18px;
      box-shadow: 0 4px 12px rgba(0 0 0 / 5%);
    }

    .ant-carousel .slick-dots {
      margin-left: 0;
    }

    .ant-carousel .slick-dots li {
      width: 8px;
      height: 14px;
    }

    .ant-carousel .slick-dots li button {
      opacity: 1;
      background: transparent;
    }

    .slick-dots li button::before {
      font-size: 8px;
    }

    .slick-dots li.slick-active button::before {
      opacity: 1;
    }
  }
}

.bannerBgItem {
  width: 100%;
  min-height: 258px;
  max-height: calc(100% - 24px);
  border-radius: 18px;
  background-repeat: no-repeat;
  background-position: left;
  background-size: cover;
}

.welcomeImg {
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023728/1690506729724846.png');
  background-repeat: no-repeat;
  width: 120px;
  height: 36px;
  background-size: contain;
  position: absolute;
  bottom: 54px;
  left: 48px;

  &.noName {
    bottom: 20px;
  }
}

.companyName {
  color: #fff;
  font-size: 20px;
  font-weight: normal;
  width: 100%;
  height: 66px;
  line-height: 64px;
  padding-left: 48px;
  position: absolute;
  bottom: -2px;
  left: 0;
  background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 41%) 100%);
  border-radius: 20px;
}

.helloImg {
  width: 98px;
  height: 32px;
  position: absolute;
  right: 30px;
  bottom: 20px;
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023728/1690507655900866.png');
  background-size: contain;
}

.isUrlFnStyle {
  cursor: pointer;
}
