import { memo, HTMLAttributes, useRef, useMemo } from 'react';
import { Carousel } from '@echronos/antd';
import classNames from 'classnames';

import styles from './banner.module.less';
import './mobile.less';

interface BannerPictureType {
  id: string;
  url: string;
  link: {
    label: string;
    type: string;
    url: string;
  };
}

export type BannerProps = HTMLAttributes<HTMLDivElement> & {
  data: {
    content: {
      abbreviation: string;
      companyName: string;
      companyNature: number;
      id: number;
      isAdministrator: number;
      isDeleted: number;
      logoUrl: string;
      picture: string;
      status: number;
    };
  };
};

const defaultBannerUrl = `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/169294497619674.png`;

function Banner({ data, ...props }: BannerProps) {
  const ref = useRef(null);

  const bannerList = useMemo<BannerPictureType[]>(() => {
    let JsonBannar = [];
    if (data.content.picture) {
      try {
        JsonBannar = JSON.parse(data.content.picture);
      } catch (error) {
        JsonBannar = [{ id: '1', url: defaultBannerUrl, link: { type: '', url: '', label: '' } }];
      }
      return JsonBannar;
    }
    return [{ id: '1', url: defaultBannerUrl, link: { type: '', url: '', label: '' } }];
  }, [data.content.picture]);

  const isShowCompanyName = useMemo(
    () => data.content.abbreviation || data.content.companyName,
    [data.content.abbreviation, data.content.companyName]
  );

  const isUrlFn = (item: BannerPictureType) => {
    const isUrl =
      item.link?.url.indexOf('http://') === 0 || item.link?.url.indexOf('https://') === 0;
    return item.link?.type === 'web' && item.link?.url && isUrl;
  };

  return (
    <div
      {...props}
      ref={ref}
      className={classNames(styles.banner, 'm-banner')}
      // style={{
      //   backgroundImage: `url('${bannerList[0].url}')`,
      // }}
    >
      <Carousel autoplay={bannerList.length > 1}>
        {bannerList.map((item) => (
          <div
            key={item.id}
            role="button"
            tabIndex={0}
            className={classNames({ [styles.isUrlFnStyle]: isUrlFn(item) })}
            onClick={() => {
              if (isUrlFn(item)) {
                window.open(item.link.url);
              }
            }}
          >
            <div
              className={styles.bannerBgItem}
              style={{
                backgroundImage: `url('${item.url}')`,
              }}
            />
          </div>
        ))}
      </Carousel>
      {bannerList.length === 1 && bannerList[0].url === defaultBannerUrl && isShowCompanyName && (
        <div className={classNames(styles.companyName, 'company-name')}>
          {data.content.abbreviation || data.content.companyName}
        </div>
      )}
      {/* <div className={classNames(styles.helloImg, 'hello-img')} /> */}
    </div>
  );
}

export default memo(
  Banner,
  (prev, next) => prev.data.content.companyName === next.data.content.companyName
);
