.m-calendar {
  width: calc(100% - 24px);
  height: calc(100% - 24px);

  .header {
    display: flex;
    padding: 12px 12px 0;
    justify-content: space-between;
    flex-direction: row;

    .calendar-icon {
      display: inline-block;
      width: 22px;
      height: 22px;
      background: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023719/1689737497636876.png');
      background-size: 100% 100%;
    }

    .title {
      color: #040919;
      font-size: 14px;
      font-weight: 600;
      margin-left: 4px;
      vertical-align: super;
    }
  }

  .change-icon {
    font-size: 18px;
    vertical-align: sub;
  }

  .ant-picker-content {
    margin: 0 auto;
    transform: scale(0.95);
  }

  .ant-picker-calendar-mini {
    border-radius: 18px;
    overflow: hidden;
    background: rgb(255 255 255 / 70%);
    backdrop-filter: blur(25.6px);
    box-shadow: 0 4px 12px rgba(0 0 0 / 5%);
  }

  .ant-picker-calendar .ant-picker-panel {
    background: none;
    border-top: none;
  }

  .ant-picker-calendar .ant-picker-cell .ant-picker-cell-inner {
    width: 22px;
    min-width: 22px;
    height: 22px;
    line-height: 22px;
    border-radius: 50%;
  }

  .ant-picker-calendar
    .ant-picker-cell-in-view.ant-picker-cell-today
    .ant-picker-cell-inner::before {
    border: none;
  }

  .ant-picker-calendar-mini .ant-picker-content {
    height: 200px;
  }

  .ant-picker-calendar .ant-picker-cell {
    padding: 0;
  }
}
