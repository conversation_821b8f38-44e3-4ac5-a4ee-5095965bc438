import { Calendar } from 'antd';
import { HTMLAttributes, memo } from 'react';
import { useMemoizedFn } from 'ahooks';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import Left from '@/components/icon/left';
import Right from '@/components/icon/right';
import './calendar.less';
import './mobile.less';

export type CalendarCpProps = HTMLAttributes<HTMLDivElement>;

function CalendarCp({ ...props }: CalendarCpProps) {
  // 多语言导入
  const { t } = useTranslation();
  const headerRender = useMemoizedFn(({ value, onChange }) => (
    <div className="header" {...props}>
      <div>
        <div className="calendar-icon" />
        <span className="title">{t('desk_apps_calendar')}</span>
      </div>
      <div className="change-time">
        <Left
          className="change-icon"
          onClick={(e) => {
            e.stopPropagation();
            const newValue = dayjs(value).add(-1, 'month');
            onChange(newValue);
          }}
        />
        {value.$M + 1}
        {t('desk_apps_mon')}
        <Right
          className="change-icon"
          onClick={(e) => {
            e.stopPropagation();
            const newValue = dayjs(value).add(1, 'month');
            onChange(newValue);
          }}
        />
      </div>
    </div>
  ));

  return (
    <div className="m-calendar" {...props}>
      <Calendar fullscreen={false} headerRender={headerRender} />
    </div>
  );
}

export default memo(CalendarCp, () => true);
