import { Row, Col } from 'antd';
import { HTMLAttributes, memo } from 'react';
import { openURL } from '@echronos/core';
import { useNavigation } from '@/hooks';
import { OrgBusinessApp } from '@/apis/model';
import { useTranslation } from 'react-i18next';
import App from '../application/application';
import './share-app.less';

export interface ShareAppProps extends HTMLAttributes<HTMLDivElement> {
  data: { companyName: string; apps: { name: string; photo: string }[] };
}

function ShareApp({
  data,
  ...props
}: {
  data: {
    name: string;
    orgBusiness: OrgBusinessApp;
  };
}) {
  const navigate = useNavigation();
  // 多语言导入
  const { t } = useTranslation();
  return (
    <div className="m-share-app" {...props}>
      <div className="app-box">
        <Row>
          {data.orgBusiness.nycbngAppFunctionList?.map((app) => (
            <Col span={8} key={app.name} className="app-col">
              <App
                data={app as any}
                className="org-app"
                onClick={(e) => {
                  e.stopPropagation();
                  const url = `${app.url}?_shareOrgId=${data.orgBusiness.orgId}&_shareCompanyId=${data.orgBusiness.companyId}`;
                  if (app.isOpen) {
                    openURL(url, '_blank');
                  } else {
                    navigate(url);
                  }
                }}
              />
            </Col>
          ))}
        </Row>
      </div>
      <div className="org-box">
        {t('desk_folderName', { name: '' })}
        <span className="company-name">{data.name}</span>
      </div>
    </div>
  );
}

export default memo(ShareApp, () => true);
