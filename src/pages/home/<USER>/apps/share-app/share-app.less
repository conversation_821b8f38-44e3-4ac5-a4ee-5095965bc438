@import '@/styles/mixins/mixins.less';

.m-share-app {
  display: flex;
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  justify-content: space-between;
  background: rgb(*********** / 60%);
  backdrop-filter: blur(20px);
  flex-direction: column;
  border-radius: 18px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0 0 0 / 5%);

  .app-box {
    padding: 16px 10px 0;
  }

  .org-box {
    font-size: 12px;
    padding: 18px 20px;

    .company-name {
      color: #008cff;
      display: inline-block;
      max-width: 160px;
      vertical-align: bottom;
      .text-overflow();
    }
  }

  .ant-col-8 {
    text-align: center !important;
  }

  .org-app {
    margin: 0 auto;

    .app-icon-img {
      width: 60px;
      height: 60px;
    }

    .name {
      width: 60px;
    }
  }
}

.m-mobile {
  .m-share-app {
    .ant-col-8 {
      text-align: center;
    }

    .company-name {
      max-width: 220px;
    }
  }
}
