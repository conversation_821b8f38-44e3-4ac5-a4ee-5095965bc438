import {
  HTMLAttributes,
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  WheelEventHandler,
} from 'react';
import Slick from 'react-slick';
import { isArray } from 'lodash';
import classNames from 'classnames';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import './slider.less';

let scrollLock = false;
function Slider({ children, className, ...props }: HTMLAttributes<HTMLDivElement>) {
  const sliderRef = useRef(null as unknown as Slick);
  const scrollTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const onScroll: WheelEventHandler = useCallback((e) => {
    if (scrollTimer.current) {
      clearTimeout(scrollTimer.current as any);
    }

    if (!scrollLock) {
      if (e.deltaY > 0) {
        sliderRef.current?.slickNext();
      } else {
        sliderRef.current?.slickPrev();
      }
    }
    scrollLock = true;

    scrollTimer.current = setTimeout(() => {
      scrollLock = false;
    }, 100);
  }, []);

  const appendDots = useCallback(
    (dots: ReactNode[]) => (
      <ul style={{ margin: '0px' }}>
        {dots.map((it, index) => (
          // eslint-disable-next-line jsx-a11y/control-has-associated-label
          <li
            className="dot-box"
            // eslint-disable-next-line jsx-a11y/no-noninteractive-element-to-interactive-role
            role="button"
            tabIndex={0}
            key={(it as any).key}
            onClick={() => {
              sliderRef.current.slickGoTo(index);
            }}
          >
            <div className={`dot ${(it as any).props.className === 'slick-active' && 'active'}`} />
          </li>
        ))}
      </ul>
    ),
    []
  );

  const len = isArray(children) ? children.length : 0;
  useEffect(() => {
    const el = len && sliderRef.current.innerSlider?.list;
    if (el) {
      const activeSide = el.querySelector('.slick-active');

      if (!activeSide) {
        sliderRef.current.slickGoTo(len - 1);
      }
    }
  }, [len]);

  return (
    <div {...props} className={classNames('m-slider-container', className)} onWheel={onScroll}>
      <Slick
        ref={sliderRef}
        dots
        speed={500}
        infinite={false}
        arrows={false}
        slidesToShow={1}
        slidesToScroll={1}
        appendDots={appendDots}
        className="slider-box"
      >
        {children}
      </Slick>
    </div>
  );
}

export default Slider;
