.m-slider-container {
  width: 100%;
  height: 100%;
  transition: all ease-in 0.2s;

  &.hide {
    opacity: 0;
  }

  .slider-box {
    width: 100%;
    height: 100%;
  }

  .slick-list {
    height: 100%;
  }

  .slick-track {
    height: 100%;
  }

  .slick-slide > div {
    height: 100%;
  }

  .slick-slide img {
    display: inline-block;
  }

  .slick-dots li button::before {
    color: rgb(255 255 255 / 80%);
  }

  .slick-dots li.slick-active button::before {
    color: #fff;
  }

  .slick-dots li {
    margin: 0 3px;
  }

  .dot-box {
    text-align: center;
    margin: 0 !important;
  }

  .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    opacity: 0.3;
    background: #fff;
    border-radius: 50%;

    &.active {
      opacity: 1;
    }
  }
}
