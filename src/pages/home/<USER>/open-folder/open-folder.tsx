import ReactDOM from 'react-dom';
import { useEffect, useRef, useState } from 'react';
import cloneDeep from 'lodash/cloneDeep';
import { useMemoizedFn, useRequest, useUnmount } from 'ahooks';
import classNames from 'classnames';
import { useNavigation } from '@/hooks';
import App from '../apps/application/application';
import Slider from '../slider/slider';
import { Layout } from '../../types';
import GridLayout from '../grid-layout/grid-layout';
import { isCanLocate } from '../grid-layout/utils';
import { getColsCount, getGridLayoutSiteByEle, getGridSize } from '../../utils/utils';
import styles from './open-folder.module.less';
import './open-folder.less';

interface AppData {
  id: string;
  name: string;
  icon: string;
  url: string;
}

function Folder({
  apps,
  folderName,
  close,
}: {
  apps: {
    id: string;
    name: string;
    icon: string;
    url: string;
  }[];
  folderName: string;
  close: () => void;
}) {
  const cols = { xs: 3, sm: 4, md: 6, lg: 8, xl: 8 };
  const folderRef = useRef(null as unknown as HTMLDivElement);
  const containerRef = useRef(null as unknown as HTMLDivElement);
  const [containerStyle, setContainerStyle] = useState(
    {} as unknown as { width: string; height: string }
  );
  const [splitLayoutsAndAppsData, setSplitLayoutsAndApps] = useState<{
    layouts: Array<Array<Layout>>;
    apps: Array<Array<AppData>>;
  }>({
    layouts: [],
    apps: [],
  });
  const navigate = useNavigation();
  const allGridLayoutSite = useRef<Array<Array<Array<string>>>>([]);
  const cacheLayouts = cloneDeep(
    apps.map((app) => ({
      id: app.id,
      w: 1,
      h: 1,
    }))
  );

  const renderApp = useMemoizedFn((index) =>
    splitLayoutsAndAppsData.apps[index].map((it) => (
      <div id={it.id} className="grid" key={it.id}>
        <App
          data={it}
          onClick={() => {
            navigate(it.url);
          }}
        />
      </div>
    ))
  );

  const recordGridLayoutSite = (
    currentGridLayoutSite: Array<Array<string>>,
    startSite: [number, number],
    lay: Layout
  ) => {
    for (let i = 0; i < lay.h; i += 1) {
      currentGridLayoutSite[startSite[0] + i].splice(
        startSite[1],
        lay.w,
        ...new Array(Number(lay.w)).fill(lay.id)
      );
    }
  };

  // 计算网格每屏分页的坐标
  const computeGridPageLayoutSite = (page: number) => {
    const allGridLayoutSiteData = allGridLayoutSite.current;
    if (!allGridLayoutSiteData[page]) {
      allGridLayoutSiteData[page] = getGridLayoutSiteByEle(containerRef.current, cols);
    }
    for (let n = 0; n < cacheLayouts.length; n += 1) {
      const item = cacheLayouts[n];
      for (let i = 0, l = allGridLayoutSiteData[page].length; i < l; i += 1) {
        let flag = false;
        for (let x = 0, y = allGridLayoutSiteData[page][i].length; x < y; x += 1) {
          if (allGridLayoutSiteData[page][i][x] === 'null') {
            if (isCanLocate(allGridLayoutSiteData[page], item, [i, x])) {
              flag = true;
              recordGridLayoutSite(allGridLayoutSiteData[page], [i, x], item);
              cacheLayouts.splice(n, 1);
              n -= 1;
              break;
            }
          }
        }
        if (flag) {
          break;
        }
      }
    }
    if (cacheLayouts.length > 0) {
      computeGridPageLayoutSite(page + 1);
    } else {
      allGridLayoutSite.current = [...allGridLayoutSiteData];
    }
  };

  function splitLayoutsAndApps() {
    const appsSplitIds: Array<string[]> = new Array(allGridLayoutSite.current.length);
    allGridLayoutSite.current.forEach((item, index) => {
      let arr: string[] = [];
      item.forEach((it) => {
        arr = arr.concat(arr, it);
      });
      appsSplitIds[index] = [...new Set(arr)].filter((id) => id !== 'null');
    });
    const appsData: Array<AppData[]> = new Array(allGridLayoutSite.current.length);
    apps.forEach((app) => {
      appsSplitIds.forEach((appArr, index) => {
        if (!appsData[index]) {
          appsData[index] = [];
        }
        if (appArr.includes(app.id)) {
          appsData[index].push(app);
        }
      });
    });

    const layoutsData: Array<Array<Layout>> = new Array(allGridLayoutSite.current.length);
    const layouts = apps.map((app) => ({
      id: app.id,
      w: 1,
      h: 1,
    }));
    layouts.forEach((lay) => {
      appsSplitIds.forEach((appArr, index) => {
        if (!layoutsData[index]) {
          layoutsData[index] = [];
        }
        if (appArr.includes(lay.id)) {
          layoutsData[index].push(lay);
        }
      });
    });

    setSplitLayoutsAndApps({
      layouts: layoutsData,
      apps: appsData,
    });
  }

  const watchWindowSize = () =>
    Promise.resolve().then(() => {
      const colCount = getColsCount(folderRef.current.clientWidth, cols);
      const rowCount = Math.ceil(apps.length / colCount);
      const gridSize = getGridSize(folderRef.current.clientWidth);
      setContainerStyle({
        width: `${colCount * gridSize.w + 40}px`,
        height: `${rowCount * gridSize.h + 40}px`,
      });
      allGridLayoutSite.current = [];
      setTimeout(() => {
        computeGridPageLayoutSite(0);
        splitLayoutsAndApps();
      });
    });

  const { run } = useRequest(watchWindowSize, {
    throttleWait: 500,
    manual: true,
  });

  useEffect(() => {
    run();
    window.addEventListener('resize', run);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useUnmount(() => {
    window.removeEventListener('resize', run);
  });

  return (
    <div
      className={styles.marks}
      role="button"
      tabIndex={0}
      onClick={() => {
        close();
      }}
    >
      <div
        className={classNames(styles.folder, 'm-folder')}
        ref={folderRef}
        role="button"
        tabIndex={0}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className={styles.folderName}>{folderName}</div>
        <div className={styles.container} ref={containerRef} style={containerStyle}>
          <div className={styles.sliderBox}>
            <div className={styles.sliderPadding}>
              <Slider>
                {splitLayoutsAndAppsData.layouts.map((item, index) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <GridLayout cols={cols} layouts={item} key={index}>
                    {renderApp(index)}
                  </GridLayout>
                ))}
              </Slider>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function OpenFolder() {
  const desktopEle = document.getElementById('desktop');
  let container = document.createElement('div');

  function destroy() {
    if (container) {
      ReactDOM.unmountComponentAtNode(container);
      desktopEle?.removeChild(container);
      container = null as unknown as HTMLDivElement;
    }
  }

  function close() {
    destroy();
  }

  function open({
    apps,
    folderName,
    onClose,
  }: {
    apps: AppData[];
    folderName: string;
    onClose?: () => void;
  }) {
    if (desktopEle) {
      desktopEle.appendChild(container);
    }
    ReactDOM.render(
      <Folder
        apps={apps}
        folderName={folderName}
        close={() => {
          close();
          if (onClose) {
            onClose();
          }
        }}
      />,
      container
    );
  }

  return {
    open,
  };
}
export default OpenFolder;
