.marks {
  position: absolute;
  inset: 0;
}

.folder {
  position: absolute;
  top: 50%;
  right: 100px;
  left: 100px;
  transform: translateY(-50%);
}

.folderName {
  color: #3d3d3d;
  font-size: 28px;
  margin-bottom: 12px;
  text-align: center;
}

.container {
  margin: 0 auto;
  border-radius: 36px;
  overflow: hidden;
}

.sliderBox {
  background: rgb(255 255 255 / 60%);
  backdrop-filter: blur(20px);
  width: 100%;
  height: 100%;
  padding: 20px 0;
}

.sliderPadding {
  width: 100%;
  height: calc(100%);
}
