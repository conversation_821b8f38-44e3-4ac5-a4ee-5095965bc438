import { useMount, useSize, useUnmount } from 'ahooks';
import classNames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import Slider, { SliderInstance } from './components/slider/slider';
// eslint-disable-next-line import/no-cycle
import DragService, {
  setDragGrid,
  getDragGrid,
  updateDragStatus,
  getDragStatus,
  setMovingStatus,
  getMovingStatus,
  getGridCopyEle,
  setGridCopyEle,
  setRectFromTarget,
  getRectFromTarget,
} from './drag';
import './grid-layout.less';
import cloneElementWithoutEvents from './utils';

export interface Grid {
  id: string;
  allowOverlay: boolean;
  // 宽格子数
  w: number;
  // 高格子数
  h: number;
  ext: any;
}

export interface GridPostion extends Grid {
  left?: number;
  top?: number;
}

export interface GridSize {
  width: number;
  height: number;
}

export interface GridLayoutProps {
  // eslint-disable-next-line no-unused-vars
  gridRender: (grid: Grid, getMouseDownGridId: (id: string) => void) => ReactNode;
  cols: number;
  // eslint-disable-next-line react/require-default-props
  gridSize?: GridSize;
  data: Array<Grid>;
  drag?: boolean;
  // eslint-disable-next-line no-unused-vars
  onSortChange: (ids: string[], grids: Grid[]) => void;
  // eslint-disable-next-line no-unused-vars
  onMouseupInOverlay?: (grid: Grid, coveredGrid: Grid) => void;
  longPressTime?: number;
  disable?: boolean;
  // eslint-disable-next-line no-unused-vars
  ondragedInOutside?: (grid: Grid) => void;
}

export const createRow = (cols: number) => new Array(cols).fill('null');

// 创建单页的坐标布局
export const createSinglePageGridSite = (
  containerSize: ReturnType<typeof useSize>,
  gridSize: GridSize,
  cols: number
) => {
  const site: Array<Array<string>> = [];

  const rows = containerSize ? Math.floor(containerSize.height / gridSize.height) : 1;

  for (let i = 0; i < rows; i += 1) {
    site[i] = createRow(cols);
  }

  return site;
};

// 判断是否可以定位
export const isCanLocate = (
  currentGridSite: Array<Array<string>>,
  lay: Grid,
  site: [number, number]
) => {
  let arr: string[] = [];
  const x = site[0];
  const y = site[1];
  for (let i = 0, j = lay.h; i < j; i += 1) {
    if (currentGridSite[x + i]) {
      arr = arr.concat(currentGridSite[x + i].slice(y, y + lay.w));
    }
  }
  const isExitUnNull = arr.filter((str) => str !== 'null').length;
  if (arr.length !== lay.w * lay.h || isExitUnNull) {
    return false;
  }
  return true;
};

const recordGridSite = (
  singPageGridSite: Array<Array<string>>,
  startSite: [number, number],
  grid: Grid
) => {
  for (let i = 0; i < grid.h; i += 1) {
    singPageGridSite[startSite[0] + i].splice(
      startSite[1],
      grid.w,
      ...new Array(Number(grid.w)).fill(grid.id)
    );
  }
};
export interface GridLayoutInstance {
  // eslint-disable-next-line no-unused-vars
  getGridSiteById: (id: string) => [number, number];
  // eslint-disable-next-line no-unused-vars
  slickGoTo: (num: number) => void;
}
// app悬停确定排序的时间
const hoverTime = 150;
const defaultGridSize = { width: 140, height: 140 };
const GridLayout = forwardRef<GridLayoutInstance, GridLayoutProps>(
  (
    {
      data,
      cols,
      gridSize,
      gridRender,
      drag,
      longPressTime,
      onSortChange,
      onMouseupInOverlay,
      ondragedInOutside,
      disable,
    },
    ref
  ) => {
    const dragServiceInstance = useRef(new DragService());
    const grids = useRef(cloneDeep(data));
    const colsCount = useRef(cols);
    const sliderRef = useRef(null as unknown as SliderInstance);
    const [layouts, setLayouts] = useState([] as unknown as Array<Array<GridPostion>>);
    const [dragId, setDragId] = useState<string | null>(null);
    const layoutContainer = useRef(null as unknown as HTMLDivElement);
    const layoutContainerSize = useSize(layoutContainer) || { width: 0, height: 0 };
    const sliderTimer = useRef(null as any);
    const sliderIndex = useRef(0);
    const gridSizeRef = useRef(gridSize);
    const longPressTimeRef = useRef(longPressTime);
    const disableRef = useRef(disable);
    const isDragInCurrentContainer = useRef(false);
    // 上一次的排序目标id
    const lastSortTargetIdRef = useRef('');
    // 是否网格覆盖着网格
    const isOverlay = useRef(false);
    const gridSiteData = useRef<Array<Array<Array<string>>>>([]);

    useEffect(() => {
      disableRef.current = disable;
    }, [disable]);

    useEffect(() => {
      gridSizeRef.current = gridSize;
    }, [gridSize]);

    useEffect(() => {
      longPressTimeRef.current = longPressTime;
    }, [longPressTime]);

    useEffect(() => {
      colsCount.current = cols;
    }, [cols]);

    useEffect(() => {
      grids.current = cloneDeep(data);
    }, [data]);

    useImperativeHandle(ref, () => ({
      getGridSiteById: (id: string) => {
        let x = -1;
        let y = -1;
        layouts.forEach((items, z) => {
          items.forEach((it, w) => {
            if (it.id === id) {
              x = z;
              y = w;
            }
          });
        });
        return [x, y];
      },
      slickGoTo: (num: number) => {
        sliderRef.current.slickGoTo(num);
      },
    }));

    if (!Array.isArray(data)) {
      throw new Error('The data parameter must be an array');
    }

    if ((gridSize && gridSize.width > layoutContainer.current?.offsetWidth) || 0) {
      console.warn('The width of the grid container cannot be greater than the parent container');
    }

    const computePagesGridPosition = () => {
      gridSiteData.current = [
        createSinglePageGridSite(
          {
            width: layoutContainer.current.offsetWidth,
            height: layoutContainer.current.offsetHeight,
          },
          gridSizeRef.current || defaultGridSize,
          colsCount.current
        ),
      ];

      const gridPostions: GridPostion[] = cloneDeep(grids.current);
      gridPostions.forEach((item) => {
        let isCanLocateFlag = false;
        for (let pageIndex = 0; pageIndex < gridSiteData.current.length; pageIndex += 1) {
          const singPageGridSite = gridSiteData.current[pageIndex];
          for (let i = 0, l = singPageGridSite.length; i < l; i += 1) {
            for (let x = 0, y = singPageGridSite[i].length; x < y; x += 1) {
              if (singPageGridSite[i][x] === 'null') {
                if (isCanLocate(singPageGridSite, item, [i, x])) {
                  // eslint-disable-next-line no-param-reassign
                  item.top = i;
                  // eslint-disable-next-line no-param-reassign
                  item.left = x;
                  isCanLocateFlag = true;
                  recordGridSite(singPageGridSite, [i, x], item);
                  break;
                }
              }
            }
            if (isCanLocateFlag) {
              break;
            }
          }
          if (isCanLocateFlag) {
            break;
          }
          if (pageIndex === gridSiteData.current.length - 1 && !isCanLocateFlag) {
            gridSiteData.current.push(
              createSinglePageGridSite(
                {
                  width: layoutContainer.current.offsetWidth,
                  height: layoutContainer.current.offsetHeight,
                },
                gridSize || defaultGridSize,
                colsCount.current
              )
            );
          }
        }
      });

      const gridPostionsMap: Record<string, GridPostion> = {};
      gridPostions.forEach((grid) => {
        gridPostionsMap[grid.id] = grid;
      });
      const pagesGridPostions: Array<Array<GridPostion>> = [];
      const pagesGridPostionsMarks: Array<Array<string>> = [];

      gridSiteData.current.forEach((pageGrids, index) => {
        pagesGridPostionsMarks[index] = [];
        pagesGridPostions[index] = [];
        pageGrids.forEach((row) => {
          row.forEach((gridId) => {
            if (!pagesGridPostionsMarks[index].includes(gridId)) {
              if (gridId !== 'null') {
                pagesGridPostionsMarks[index].push(gridId);
              }
              if (gridPostionsMap[gridId]) {
                pagesGridPostions[index].push(gridPostionsMap[gridId]);
              }
            }
          });
        });
      });
      grids.current = [];
      pagesGridPostions.forEach((singlePageGridPostions) => {
        singlePageGridPostions.forEach((g) => {
          grids.current.push({
            id: g.id,
            allowOverlay: g.allowOverlay,
            w: g.w,
            h: g.h,
            ext: g.ext,
          });
        });
      });

      return pagesGridPostions;
    };

    const setLayoutsData = (da?: Array<Array<GridPostion>>) => {
      setLayouts(da || computePagesGridPosition());
    };

    useEffect(() => {
      if (layoutContainerSize.width > 0 && layoutContainerSize.height > 0) {
        grids.current = cloneDeep(data);
        setLayoutsData(layoutContainer.current?.offsetHeight > 0 ? computePagesGridPosition() : []);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data, cols, gridSize, layoutContainerSize]);

    const getMouseDownGridId = (id: string) => {
      if (drag) {
        const g = grids.current.find((item) => item.id === id);
        setDragGrid(g || null);
        setDragId(id);
      }
    };

    const afterSliderChange = (index: number) => {
      sliderIndex.current = index;
    };

    const stopSliderChange = () => {
      if (sliderTimer.current) {
        clearInterval(sliderTimer.current);
      }
    };

    const sliderChangePrev = () => {
      sliderRef.current.slickPrev();
      stopSliderChange();
      if (!sliderRef.current) {
        clearInterval(sliderTimer.current);
      }
      sliderTimer.current = setInterval(() => {
        sliderRef.current.slickPrev();
      }, 1000);
    };

    const sliderChangeNext = () => {
      sliderRef.current.slickNext();
      stopSliderChange();
      if (!sliderRef.current) {
        clearInterval(sliderTimer.current);
      }
      sliderTimer.current = setInterval(() => {
        sliderRef.current.slickNext();
      }, 1000);
    };

    const cancelOverlayStatus = () => {
      isOverlay.current = false;
      document.querySelector('.grid-layout-overlay')?.classList.remove('grid-layout-overlay');
    };

    const cancelDrag = () => {
      const dragGridId = getDragGrid()?.id;

      if (dragGridId) {
        setTimeout(() => {
          document
            .querySelector('.ech-grid-layout-grid-shadow')
            ?.classList.remove('ech-grid-layout-grid-shadow');
        }, 200);
        setTimeout(() => {
          // 有的时候清除class清除不了 todo
          if (document.querySelector('.ech-grid-layout-grid-shadow')) {
            document
              .querySelector('.ech-grid-layout-grid-shadow')
              ?.classList.remove('ech-grid-layout-grid-shadow');
          }
        }, 300);

        const gridCopyEle = getGridCopyEle();
        if (gridCopyEle) {
          gridCopyEle.remove();
        } else {
          console.error('没有找到gridCopyEle');
        }

        if (getDragStatus() && onSortChange) {
          const g = grids.current.find((item) => item.id === dragGridId);

          if (!g) {
            if (isDragInCurrentContainer.current) {
              grids.current = dragServiceInstance.current.getCacheBeforeMovingGrids();
            } else {
              grids.current = [...grids.current, getDragGrid() as Grid];
            }

            setLayoutsData();
          }

          onSortChange(
            grids.current.map((item) => item.id),
            grids.current
          );
        }
      }
      cancelOverlayStatus();
      setDragGrid(null);
      setGridCopyEle(null);
      setDragId(null);
      updateDragStatus(false, longPressTimeRef.current || 0);
      isDragInCurrentContainer.current = false;
      setMovingStatus(false);
      dragServiceInstance.current.setCacheBeforeMovingGrids([]);
    };

    const getOverGridByPos = (pos: { x: number; y: number }) => {
      const gx =
        pos.x -
        layoutContainer.current.getBoundingClientRect().left -
        (layoutContainer.current.getBoundingClientRect().width -
          (gridSizeRef.current || defaultGridSize).width * colsCount.current) /
          2;
      const gy = pos.y - layoutContainer.current.getBoundingClientRect().top;

      const gxSite = Math.floor(gx / (gridSizeRef.current || defaultGridSize).width);
      const gySite = Math.floor(gy / (gridSizeRef.current || defaultGridSize).height);

      if (gySite < 0 || gxSite < 0) {
        return null;
      }

      let sortTargetId = null;
      let sortTarget = null;

      if (
        gridSiteData.current[sliderIndex.current] &&
        gridSiteData.current[sliderIndex.current][gySite] &&
        gridSiteData.current[sliderIndex.current][gySite][gxSite]
      ) {
        sortTargetId = gridSiteData.current[sliderIndex.current][gySite][gxSite];
        sortTarget = document.getElementById(sortTargetId);
      }
      return {
        target: sortTarget,
        targetId: sortTargetId,
      };
    };

    const mousedown = useCallback((e: MouseEvent) => {
      if (disableRef.current) {
        return;
      }
      const dragGridId = getDragGrid()?.id;
      if (dragGridId) {
        updateDragStatus(true, 0);
        isDragInCurrentContainer.current = true;
        const grid = document.getElementById(dragGridId);
        if (grid) {
          const gridCopy = cloneElementWithoutEvents(grid) as HTMLDivElement;

          if (gridCopy) {
            gridCopy.id += '-copy';
            gridCopy.style.opacity = '0';
            gridCopy.style.zIndex = '-1';
            gridCopy.className = gridCopy.className.replace('transition', '');
          }
          setGridCopyEle(gridCopy);
          const rect = grid.getBoundingClientRect();
          setRectFromTarget({
            top: e.clientY - rect.top,
            left: e.clientX - rect.left,
          });
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const mouseup = useCallback((e) => {
      if (disableRef.current) {
        return;
      }
      if (onMouseupInOverlay && isOverlay.current) {
        const overGrid = getOverGridByPos({
          x: e.clientX,
          y: e.clientY,
        });
        if (!overGrid) {
          return;
        }

        const sortTargetId = overGrid?.targetId;
        const coveredGrid = grids.current.find((d) => d.id === sortTargetId);
        if (coveredGrid) {
          onMouseupInOverlay(getDragGrid() as Grid, coveredGrid as Grid);
        }
      }

      cancelDrag();
      stopSliderChange();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const mousemove = useCallback((e: MouseEvent) => {
      if (disableRef.current) {
        return;
      }

      try {
        // 某种情况mouseup事件没有触发
        if (e.buttons === 0 && getDragStatus()) {
          cancelDrag();
          return;
        }
        const dragGridId = getDragGrid()?.id;

        if (!getDragStatus() && dragGridId) {
          cancelDrag();
          return;
        }
        if (getDragStatus() && dragGridId) {
          const gridCopy = getGridCopyEle();
          if (!getMovingStatus()) {
            if (gridCopy) {
              document.body.appendChild(gridCopy);
            }
            dragServiceInstance.current.setCacheBeforeMovingGrids(cloneDeep(grids.current));
            const gridsData = grids.current.filter((grid) => {
              if (grid.id === dragGridId) {
                return false;
              }
              return true;
            });
            grids.current = gridsData;
            setLayoutsData();
            // dragServiceInstance.current.setLayoutContainerRect(
            //   layoutContainer.current.getBoundingClientRect()
            // );
          }
          setMovingStatus(true);
          const top = e.clientY - getRectFromTarget().top;
          const left = e.clientX - getRectFromTarget().left;
          const layoutContainerRect = layoutContainer.current.getBoundingClientRect();

          if (left < layoutContainerRect.left) {
            sliderChangePrev();
          } else if (
            gridCopy &&
            // eslint-disable-next-line no-unsafe-optional-chaining
            left + gridCopy?.getBoundingClientRect().width >
              layoutContainerRect.left + layoutContainerRect.width + 20
          ) {
            sliderChangeNext();
          } else {
            stopSliderChange();
          }

          if (gridCopy) {
            gridCopy.style.position = 'fixed';
            gridCopy.style.top = `${top}px`;
            gridCopy.style.left = `${left}px`;
            gridCopy.style.zIndex = '9999';
            gridCopy.style.opacity = '1';

            if (dragServiceInstance.current.getDragStopTimer()) {
              clearTimeout(dragServiceInstance.current.getDragStopTimer());
            }
            const dragGridIndex = grids.current.findIndex((grid) => grid.id === dragGridId);
            if (dragGridIndex >= 0) {
              grids.current.splice(dragGridIndex, 1);
              setLayoutsData();
            }
            const timer = setTimeout(() => {
              const overGrid = getOverGridByPos({
                x: e.clientX,
                y: e.clientY,
              });

              const sortTargetId = overGrid?.targetId;
              const sortTarget = overGrid?.target;
              if (sortTargetId !== lastSortTargetIdRef.current) {
                cancelOverlayStatus();
              }

              if (sortTarget) {
                const rect = sortTarget.getBoundingClientRect();
                const index = grids.current.findIndex((grid) => grid.id === sortTarget.id);
                const dragGrid = getDragGrid();
                const baseLeftOffset = rect.width * 0.2;
                if (e.clientX < rect.left + baseLeftOffset) {
                  if (dragGrid) {
                    grids.current.splice(index, 0, dragGrid);
                  }
                  cancelOverlayStatus();
                } else if (e.clientX > rect.left + rect.width - baseLeftOffset) {
                  if (dragGrid) {
                    grids.current.splice(index + 1, 0, dragGrid);
                  }
                  cancelOverlayStatus();
                } else if (
                  e.clientX > rect.left + baseLeftOffset &&
                  e.clientX < rect.left + rect.width - baseLeftOffset
                ) {
                  if (!isOverlay.current && dragGrid?.allowOverlay) {
                    isOverlay.current = true;
                    sortTarget.classList.add('grid-layout-overlay');
                  }
                }
                setLayoutsData();
                setTimeout(() => {
                  document.getElementById(dragGridId)?.classList.add('ech-grid-layout-grid-shadow');
                });
              } else {
                cancelOverlayStatus();

                if (
                  e.clientX < layoutContainerRect.left ||
                  e.clientX > layoutContainerRect.left + layoutContainerRect.width ||
                  e.clientY < layoutContainerRect.top ||
                  e.clientY > layoutContainerRect.top + layoutContainerRect.height
                ) {
                  const dragGrid = getDragGrid();
                  if (ondragedInOutside && dragGrid) {
                    ondragedInOutside(dragGrid as Grid);
                  }
                }
              }
            }, hoverTime);
            dragServiceInstance.current.setDragStopTimer(timer);
          }
        }
      } catch (err) {
        cancelDrag();
        // eslint-disable-next-line no-console
        console.log(err);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useMount(() => {
      window.addEventListener('mousedown', mousedown);
      window.addEventListener('mouseup', mouseup);
      window.addEventListener('mousemove', mousemove);
    });

    useUnmount(() => {
      // cancelDrag();
      window.removeEventListener('mousedown', mousedown);
      window.removeEventListener('mouseup', mouseup);
      window.removeEventListener('mousemove', mousemove);
    });

    return (
      <div className="ech-grid-layout" ref={layoutContainer}>
        <Slider afterChange={afterSliderChange} ref={sliderRef}>
          {layouts.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div key={index} className="ech-grid-layout-slider-box">
              <div
                className="ech-grid-layout-conatiner"
                style={{
                  width: `${(gridSize?.width || 0) * cols}px`,
                }}
              >
                {(item as GridPostion[]).map((grid) => (
                  // eslint-disable-next-line jsx-a11y/no-static-element-interactions
                  <div
                    className={classNames(
                      dragId === grid.id && 'draging',
                      'ech-grid-layout-grid transition'
                    )}
                    onMouseUp={() => {
                      updateDragStatus(false, longPressTimeRef.current || 0);
                      isDragInCurrentContainer.current = false;
                      setDragGrid(null);
                    }}
                    id={grid.id}
                    key={grid.id}
                    style={{
                      width: `${grid.w * (gridSize || defaultGridSize).width}px`,
                      height: `${grid.h * (gridSize || defaultGridSize).height}px`,
                      top: `${(grid.top || 0) * (gridSize || defaultGridSize).height}px`,
                      left: `${(grid.left || 0) * (gridSize || defaultGridSize).width}px`,
                      position: 'absolute',
                    }}
                  >
                    {/* <span className="mark">{grid.id}</span> */}
                    {gridRender(grid, getMouseDownGridId)}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </Slider>
      </div>
    );
  }
);

GridLayout.defaultProps = {
  longPressTime: 200,
  gridSize: defaultGridSize,
  drag: false,
  onMouseupInOverlay: undefined,
  ondragedInOutside: undefined,
  disable: false,
};

export default GridLayout;
