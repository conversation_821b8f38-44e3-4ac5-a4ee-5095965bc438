/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useRef, useEffect, useState, useMem<PERSON>, <PERSON><PERSON><PERSON>, MouseEventHandler } from 'react';
import { useMemoizedFn, useRequest, useSize } from 'ahooks';
import { useUserinfo } from '@echronos/react';
import classNames from 'classnames';
import { useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CLIENT, EMPTY_FN, openURL } from '@echronos/core';
// import GridLayout, { GridLayoutInstance } from '@echronos/echos-ui/dist/grid-layout';
import '@echronos/echos-ui/dist/index.css';
import {
  getDesktopMyApp,
  IMyApp,
  addOrRemoveApp,
  getOrgBusinessApp,
  getHomeGuide,
  appletSort,
  getPermStatusList,
  addAppGroup,
  updateGroupInfo,
} from '@/apis';
import SpinBox from '@/components/spin-box';
import { message } from '@/components/message';
import useToApp from '@/hooks/use-to-app';
import { ItemType, useDesktopMenu } from '@/components/desktop-menu';
import editCustomApp from '@/containers/custom-app';
import useEventHandle from '@/hooks/use-event-handle';
import GridLayout, { GridLayoutInstance, Grid } from './grid-layout/grid-layout';
import { verifyApp } from './utils/verify';
import * as componentNodes from './components/apps';
import AllAppPop from './components/all-app-pop/all-app-pop';
import OpenFolder from './components/open-folder/open-folder';
import Guide from './components/guide/guide';
import Folder from './components/folder/folder';
import './desktop.less';
import findUserNicknameModify from '../../apis/user/find-user-nickname-modify';
import AllCardModal from './components/all-card-modal/all-card-modal';
import BlockContainer from '../../containers/block-container';
import DesktopCardDelete from './components/deaktop-card-delete';
// 容器尺寸类型（使用的时候要注意是网格还是屏幕）
export type ContainerSizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
export type GridSizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
export type PageGridColumns = Record<ContainerSizeType, number>;
export type GridPlace = [cols: number, rows: number];
export interface GridSize {
  width: number;
  height: number;
}
export type PageGridPlace = Record<ContainerSizeType, GridPlace>;

export type DesktopGrid = { ext: IMyApp } & Grid;

// 所有屏幕下的网格站位
const gridPlaceMapForAllSizeScreen: PageGridPlace[] = [
  {
    xs: [1, 1],
    sm: [4, 2],
    md: [4, 4],
    lg: [4, 4],
    xl: [4, 4],
    xxl: [4, 4],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [4, 2],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [6, 2],
  },
];

// 网格尺寸类型
const gridSizeType: GridSize[] = [
  { width: 87, height: 96 },
  { width: 140, height: 140 },
];

// 每种屏幕尺寸下每行的列数
const defaultPageGridColumns: PageGridColumns = { xs: 4, sm: 4, md: 6, lg: 8, xl: 10, xxl: 10 };

function Desktop() {
  const sliderContainerEl = useRef(null as unknown as HTMLDivElement);
  const gridLayoutRef = useRef(null as unknown as GridLayoutInstance);
  const selectAppRef = useRef<IMyApp | null>();
  const [, setHideApps] = useState(false);
  const appClickLock = useRef(false);
  const isGuide = useRef(false);
  const userInfo = useUserinfo();
  const [toApp, navigate] = useToApp();
  const [searchParams] = useSearchParams();
  const { getEventType, initDown, onMouseDown } = useEventHandle();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const elSize = useSize(sliderContainerEl) || { width: 0, height: 0 };
  const longPressTime = 80;
  const [dragAppId, setDragAppId] = useState<number | null>(null);
  const cacheLongPressTimer = useRef(null as any);
  const [showEditScreen, setShowEditScreen] = useState(false);
  const [isDisableGridLayout, setIsDisableGridLayout] = useState(false);
  const [renderApps, setRenderApps] = useState<DesktopGrid[]>([]);

  const renderAppsRef = useRef<DesktopGrid[]>([]);
  // 多语言导入
  const { t } = useTranslation();
  useEffect(() => {
    renderAppsRef.current = renderApps;
  }, [renderApps]);

  const gridLayoutInfo = useMemo(() => {
    if (!elSize.width) {
      return {
        cols: defaultPageGridColumns.xs,
        gridSize: gridSizeType[0],
      };
    }
    const { width } = elSize;
    if (width < 560) {
      return {
        cols: defaultPageGridColumns.xs,
        gridSize: gridSizeType[0],
      };
    }
    if (width < 840) {
      return {
        cols: defaultPageGridColumns.sm,
        gridSize: gridSizeType[1],
      };
    }
    if (width < 1120) {
      return {
        cols: defaultPageGridColumns.md,
        gridSize: gridSizeType[1],
      };
    }
    if (width < 1400) {
      return {
        cols: defaultPageGridColumns.lg,
        gridSize: gridSizeType[1],
      };
    }
    if (width < 1600) {
      return {
        cols: defaultPageGridColumns.xl,
        gridSize: gridSizeType[1],
      };
    }
    return {
      cols: defaultPageGridColumns.xxl,
      gridSize: gridSizeType[1],
    };
  }, [elSize]);

  const {
    data,
    loading,
    run: refresh,
  } = useRequest((cb?: () => void) =>
    Promise.all([getDesktopMyApp(), getOrgBusinessApp()]).then((result) => {
      if (!result) {
        return [];
      }

      const [{ list } = { list: [] }] = result;
      if (cb) {
        cb();
      }
      return list;
    })
  );

  const appSort = (ids: number[]) => appletSort(ids);

  const { run: runAppSort } = useRequest(appSort, {
    debounceWait: 300,
    manual: true,
  });

  const appSortFun = (ids: string[]) => {
    const newIds = [...ids];
    const groups: Record<string, IMyApp[]> = {};
    renderAppsRef.current.forEach((item) => {
      if (item.ext.type === 4) {
        groups[item.id] = item.ext.children;
      }
    });
    Object.keys(groups).forEach((id) => {
      const index = newIds.indexOf(id);
      if (index >= 0) {
        const childrenIds = groups[id].map((it) => `${it.relationId}`);
        newIds.splice(index, 1, ...childrenIds);
      }
    });
    runAppSort(newIds.map((id) => Number(id)));
  };

  const folderAppsSort = (folderId: string, appIds: string[]) => {
    const folder = renderAppsRef.current.find((app) => app.id === `${folderId}`);

    const appsMap: Record<string, IMyApp[]> = {};
    folder?.ext.children.forEach((app: IMyApp) => {
      // @ts-ignore
      appsMap[`${app.relationId}`] = app;
    });
    if (folder) {
      folder.ext.children = appIds.map((id) => appsMap[id]);
    }
    appSortFun(renderAppsRef.current.map((it) => it.id));
  };

  const toGridPlace: Record<string, GridSizeType> = {
    app: 'xs',
    folder: 'md',
    calendar: 'md',
    shareApp: 'md',
    processApprove: 'md',
    banner: 'xxl',
  };

  // 分组，将后台平级的数据返回，洗成适合前端展示的结构
  const grouping = (d: DesktopGrid[]) => {
    const groupId: number[] = [];
    const newApps: DesktopGrid[] = [];
    const groupsMap: Record<string, IMyApp[]> = {};
    if (!d) {
      return [];
    }
    d.forEach((item) => {
      if (item.ext.groupId === 0) {
        newApps.push(item);
      }
      if (item.ext.groupId > 0 && groupId.indexOf(item.ext.groupId) < 0) {
        groupId.push(item.ext.groupId);
        newApps.push({
          id: `${item.ext.groupId}`,
          allowOverlay: false,
          w: 2,
          h: 2,
          ext: {
            groupName: item.ext.groupName || `${t('desk_groupName')}`,
            type: 4,
            groupId: item.ext.groupId,
            children: [],
          } as any,
        });
      }
      if (item.ext.groupId > 0) {
        if (!groupsMap[`${item.ext.groupId}`]) {
          groupsMap[`${item.ext.groupId}`] = [item.ext];
        } else {
          groupsMap[`${item.ext.groupId}`].push(item.ext);
        }
      }
    });
    newApps.forEach((item) => {
      if (item.ext.groupId > 0) {
        // eslint-disable-next-line no-param-reassign
        item.ext.children = groupsMap[`${item.ext.groupId}`];
      }
    });
    return newApps;
  };

  useEffect(() => {
    const a = data?.map((item) => {
      const place =
        gridPlaceMapForAllSizeScreen[1][toGridPlace[item.componentType] || item.size || 'xs'];
      return {
        id: `${item.relationId}`,
        allowOverlay: item.classify !== 4,
        w: place[0],
        h: place[1],
        ext: item,
      };
    });
    setRenderApps(grouping(a as DesktopGrid[]));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const getSelectApp = (e: MouseEvent<HTMLElement>): IMyApp | null => {
    let el = e.target as HTMLElement | null;
    while (el && !el.getAttribute('data-id')) {
      el = el.parentElement;
    }

    if (el && data) {
      const id = Number(el.getAttribute('data-id'));
      const classify = Number(el.getAttribute('data-classify'));
      return (
        data.find(
          classify === 0
            ? (item) => item.id === id
            : (item) => item.id === id && item.classify === classify
        ) || null
      );
    }
    return null;
  };

  const onNavSet = () => {
    getPermStatusList({ permCodes: ['BP_001'] }).then((res) => {
      if (res.list && res.list.length) {
        const info = res.list[0];
        if (!info.isCharge) {
          navigate(`/appstore/app/${info.appId}/1`);
          return;
        }
        if (!info.isPermission) {
          message.warning(t('public_noPerm'));
          return;
        }
      }
      navigate('/settings/system-settings');
    });
  };

  useDesktopMenu(sliderContainerEl, {
    items: (value) => {
      const app = getSelectApp(value);

      selectAppRef.current = null;
      if (app) {
        if (app.componentType !== 'app' && app.classify !== 4) {
          return null;
        }
        selectAppRef.current = app;
        return [
          app.classify === 4
            ? null
            : {
                key: 'openSelf',
                langKey: 'desk_openSelf',
                label: '当前页面打开',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462092069885.png`,
              },
          app.classify === 4
            ? null
            : {
                key: 'openBlank',
                langKey: 'desk_openBlank',
                label: '新标签页打开',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462130354690.png`,
              },
          app.classify === 4
            ? null
            : {
                key: 'refresh',
                langKey: 'desk_refresh',
                label: '刷新',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724327838456666.png`,
              },

          app.classify === 2
            ? {
                key: 'edit',
                langKey: 'desk_edit',
                label: '重命名',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462332528373.png`,
              }
            : null,
          app.classify === 4
            ? {
                key: 'editScreen',
                langKey: 'desk_editScreen',
                label: '编辑主屏幕',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724402448525387.png`,
              }
            : null,
          // app.classify === 4 ? null : { type: 'divider' },
          { type: 'divider' },
          {
            key: 'delete',
            langKey: 'desk_delete',
            label: '删除',
            icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462170921508.png`,
          },
        ] as ItemType[];
      }
      return [
        {
          key: 'search',
          langKey: 'desk_search',
          label: '搜索',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724402475896363.png`,
        },
        {
          key: 'addApp',
          langKey: 'desk_addApp',
          label: '添加应用',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724320299367193.png`,
        },
        {
          key: 'addCard',
          langKey: 'desk_addCard',
          label: '添加小组件',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724402402644337.png`,
        },
        {
          key: 'refresh',
          langKey: 'desk_refresh',
          label: '刷新',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724327838456666.png`,
        },
        { type: 'divider' },
        {
          key: 'createFunc',
          langKey: 'desk_createFunc',
          label: '新建',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724824341869911.png`,
        },
        { type: 'divider' },
        {
          key: 'editScreen',
          langKey: 'desk_editScreen',
          label: '编辑主屏幕',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724402448525387.png`,
        },

        {
          key: 'set',
          langKey: 'desk_set',
          label: '设置',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724402496239152.png`,
        },
      ];
    },
    onSelect: ({ key }) => {
      if (key === 'addCard') {
        AllCardModal().open({
          added: (id: number) => {
            refresh(() => {
              setTimeout(() => {
                const page = gridLayoutRef.current.getGridSiteById(`${id}`)[0];
                if (page >= 0) {
                  gridLayoutRef.current.slickGoTo(page);
                }
              });
            });
          },
        });
      } else if (key === 'addApp') {
        AllAppPop().open({
          isGuide: isGuide.current,
          added: (id: number) => {
            refresh(() => {
              setTimeout(() => {
                const page = gridLayoutRef.current.getGridSiteById(`${id}`)[0];
                if (page >= 0) {
                  gridLayoutRef.current.slickGoTo(page);
                }
              });
            });
          },
          closeGuide: () => {
            isGuide.current = false;
          },
        });
      } else if (key === 'editScreen') {
        setShowEditScreen(true);
      } else if (key === 'search') {
        const microData = window.microApp?.getData();
        if (microData && microData.navigate) {
          // @ts-ignore
          microData?.searchDialogFunc({ navigate });
        }
      } else if (key === 'refresh') {
        refresh();
      } else if (key === 'set') {
        onNavSet();
      } else {
        const app = selectAppRef.current;
        if (!app) {
          return;
        }
        if (key === 'delete') {
          addOrRemoveApp({
            funcId: app.id,
            flag: 1,
            classify: app.classify,
            appId: app.classify === 3 ? app.echAppId : null,
          }).then(() => {
            message.success(t('desk_deleteSuccess'));
            refresh();
          });
        } else if (key === 'edit') {
          editCustomApp(app.id).then(() => {
            refresh();
          });
        } else if (verifyApp(app)) {
          if (key === 'openSelf') {
            if (/^(https?:)?\/\//.test(app.url)) {
              const link = document.createElement('a');
              link.href = app.url;
              link.rel = 'noreferrer';
              // @ts-ignore
              link.referrerpolicy = 'no-referrer';
              link.click();
              link.remove();
            } else {
              navigate(app.url);
            }
          } else {
            openURL(app.url);
          }
        }
      }
    },
    disable: () => !isGuide.current,
    userInfo: userInfo.user,
  });

  const onClickApp: MouseEventHandler<HTMLElement> = useMemoizedFn((e) => {
    e.stopPropagation();
    if (isGuide.current) {
      return;
    }
    if (appClickLock.current) {
      return;
    }
    const item = getSelectApp(e);
    if (!item) {
      return;
    }
    let toUrL = item.url;
    if (item.componentType === 'app') {
      if ([1, 3].includes(item.classify) && !verifyApp(item)) {
        return;
      }

      toUrL = item.url;
    } else if (item.componentType === 'shareApp') {
      setHideApps(true);
      const appsData = item.orgBusiness?.nycbngAppFunctionList?.map(
        (it: { name: string; photo: string; url: string; id: number }) => ({
          id: `${it.id}`,
          name: it.name,
          icon: it.photo,
          url: `${it.url}?_shareOrgId=${item.orgBusiness?.orgId}&_shareCompanyId=${item.orgBusiness?.companyId}`,
        })
      );

      OpenFolder().open({
        apps: appsData || [],
        folderName: `${t('desk_folderName', { name: item.name })}`,
        onClose: () => {
          setHideApps(false);
        },
      });
      return;
    } else if (item.componentType === 'calendar') {
      toUrL = `/user/calendar/home`;
    } else if (item.componentType === 'processApprove') {
      let processInstanceId = '';
      if (item.content && item.content[0] && item.content[0].workflowProcessInstanceId) {
        processInstanceId = item.content[0].workflowProcessInstanceId;
      }
      toUrL = `/admin/process/pending?processInstanceId=${processInstanceId}`;
    }
    toApp(toUrL, {
      id: item.id,
      icon: item.photo,
      name: item.name,
      code: item.coding,
      isOpen: item.isOpen,
      externalApp: item.externalApp,
      shopId: userInfo.shop.id,
      classify: item.classify,
      url: item.url,
    });
  });

  const mergeApps = (dragGrid: DesktopGrid, overedGrid: DesktopGrid) => {
    if (dragGrid.ext.type !== 3) {
      return;
    }
    // 合并入文件夹
    if (overedGrid.ext.type === 4) {
      const dragAppIndex = renderAppsRef.current.findIndex(
        (app) => app.id === `${dragGrid.ext.relationId}`
      );
      if (dragAppIndex >= 0) {
        renderAppsRef.current.splice(dragAppIndex, 1);
      }

      const overedAppIndex = renderAppsRef.current.findIndex((app) => app.id === overedGrid.id);
      // @ts-ignore
      renderAppsRef.current[overedAppIndex].ext.children.push({
        ...dragGrid.ext,
        groupId: renderAppsRef.current[overedAppIndex].ext.groupId,
        groupName: renderAppsRef.current[overedAppIndex].ext.groupName,
      });

      setRenderApps([...renderAppsRef.current]);
      addAppGroup({
        relationIds: [Number(dragGrid.id)],
        groupId: overedGrid.ext.groupId,
        identity: '',
        groupName: renderAppsRef.current[overedAppIndex].ext.groupName,
        operateType: 1,
      }).then(() => {
        appSortFun(renderAppsRef.current.map((it) => it.id));
      });
    } else if (overedGrid.ext.type === 3) {
      // 创建文件夹
      const uuid = new Date().getTime();
      const folder = {
        id: `${uuid}`,
        w: 2,
        allowOverlay: false,
        h: 2,
        ext: {
          type: 4,
          groupName: `${t('desk_groupName')}`,
          children: [overedGrid.ext, dragGrid.ext],
        } as any,
      };

      const overedAppIndex = renderAppsRef.current.findIndex((app) => app.id === overedGrid.id);

      renderAppsRef.current.splice(overedAppIndex, 1, folder);
      const dragAppIndex = renderAppsRef.current.findIndex((app) => app.id === dragGrid.id);
      if (dragAppIndex >= 0) {
        renderAppsRef.current.splice(dragAppIndex, 1);
      }

      setRenderApps([...renderAppsRef.current]);
      addAppGroup({
        relationIds: [Number(overedGrid.id), Number(dragGrid.id)],
        identity: `${uuid}`,
        groupName: `${t('desk_groupName')}`,
        operateType: 1,
      }).then((res) => {
        const folderIndex = renderAppsRef.current.findIndex((app) => app.id === `${uuid}`);
        renderAppsRef.current[folderIndex].ext.groupId = res.groupId;
        renderAppsRef.current[folderIndex].ext.children.forEach((item: IMyApp) => {
          // eslint-disable-next-line no-param-reassign
          item.groupId = res.groupId;
        });
        renderAppsRef.current[folderIndex].id = `${res.groupId}`;
        setRenderApps([...renderAppsRef.current]);
        appSortFun(renderAppsRef.current.map((it) => it.id));
      });
    }
  };

  const removeAppFromFloder = (removeGrid: DesktopGrid) => {
    const { groupId } = removeGrid.ext;
    const group = renderAppsRef.current.find((app) => app.id === `${groupId}`);
    const removeIndex = group?.ext.children.findIndex(
      (it: IMyApp) => it.relationId === removeGrid.ext.relationId
    );

    group?.ext.children.splice(removeIndex, 1);
    if (group?.ext.children.length === 0) {
      const groupIndex = renderAppsRef.current.findIndex((it: Grid) => it.id === group.id);
      renderAppsRef.current.splice(groupIndex, 1);
    }

    setRenderApps([...renderAppsRef.current]);
    addAppGroup({
      relationIds: [Number(removeGrid.id)],
      identity: ``,
      groupName: '',
      operateType: 2,
    }).then(() => {
      appSortFun(renderAppsRef.current.map((it) => it.id));
    });
  };

  const onMouseUp = (event: any) => {
    const eventType = getEventType(event);

    if (eventType === 'click') {
      onClickApp(event);
    }

    initDown();
  };

  const onClickContent = () => {
    setShowEditScreen(false);
  };

  const windowMouseUp = () => {
    if (cacheLongPressTimer.current) {
      clearInterval(cacheLongPressTimer.current);
    }
    setDragAppId(null);
  };

  const windowMousemove = () => {
    if (!dragAppId) {
      if (cacheLongPressTimer.current) {
        clearInterval(cacheLongPressTimer.current);
      }
      setDragAppId(null);
    }
  };

  const runSetDragAppId = (id: number) => {
    cacheLongPressTimer.current = setTimeout(() => {
      setDragAppId(id);
    }, longPressTime);
  };

  const updateGroupName = (id: number, name: string) => {
    renderAppsRef.current.forEach((item) => {
      if (item.id === `${id}`) {
        // eslint-disable-next-line no-param-reassign
        item.ext.groupName = name;
      }
    });

    setRenderApps([...renderAppsRef.current]);
    updateGroupInfo({
      groupId: id,
      groupName: name,
    });
  };

  useEffect(() => {
    let guideInstance = null as unknown as {
      // eslint-disable-next-line no-unused-vars
      start: (par: { added: () => void; closeGuide: () => void }) => void;
      destroy: () => void;
    };
    const guide = searchParams.get('isGuide');

    function startGuide() {
      guideInstance = Guide();
      guideInstance.start({
        added: refresh,
        closeGuide: () => {
          isGuide.current = false;
        },
      });
    }

    if ((userInfo.company as any).companyNature !== 1) {
      getHomeGuide().then((res) => {
        isGuide.current = !res.data;
        if (guide) {
          isGuide.current = guide === '1';
        }
        if (isGuide.current) {
          startGuide();
        }
      });
    } else if (guide) {
      isGuide.current = guide === '1';
      if (isGuide.current) {
        startGuide();
      }
    }

    const refreshDesktop =
      CLIENT && window.microApp
        ? (mainData: { type: string }) => {
            if (mainData.type === 'desktop.reload') {
              refresh();
            }
          }
        : null;

    if (refreshDesktop) {
      window.microApp?.addGlobalDataListener(refreshDesktop);
    }

    findUserNicknameModify().then((res) => {
      if (res.isModify) {
        navigate('/user/mycenter/userinfo?isModify=true');
      }
    });

    document.addEventListener('mouseup', windowMouseUp);
    document.addEventListener('mousemove', windowMousemove);

    return () => {
      if (refreshDesktop) {
        window.microApp?.removeGlobalDataListener(refreshDesktop);
      }

      if (isGuide.current && guideInstance) {
        guideInstance.destroy();
      }

      document.removeEventListener('mouseup', windowMouseUp);
      document.removeEventListener('mousemove', windowMousemove);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <div
      id="desktop"
      role="button"
      tabIndex={0}
      className={classNames('desktop')}
      ref={sliderContainerEl}
      onMouseUp={onMouseUp}
      onClick={onClickContent}
    >
      <SpinBox loading={loading}>
        {renderApps && renderApps.length > 0 && (
          <GridLayout
            ref={gridLayoutRef}
            data={renderApps}
            cols={gridLayoutInfo.cols}
            gridSize={gridLayoutInfo.gridSize}
            longPressTime={showEditScreen ? 0 : 150}
            drag
            disable={isDisableGridLayout}
            onMouseupInOverlay={(grid, coveredGrid) => {
              mergeApps(grid, coveredGrid);
              // openFolderPop({ apps: [grid.ext, coveredGrid.ext] });
            }}
            onSortChange={(ids: string[], grids: Grid[]) => {
              appSortFun(ids);
              setRenderApps([...grids]);
            }}
            // eslint-disable-next-line react/no-unstable-nested-components
            gridRender={(grid, getMouseDownGridId) => {
              const app = grid.ext;

              if (app.type === 4) {
                return (
                  <Folder
                    onFolderOpen={() => {
                      setIsDisableGridLayout(true);
                    }}
                    onFolderClose={() => {
                      setIsDisableGridLayout(false);
                    }}
                    onRemoveApp={(g) => {
                      removeAppFromFloder(g);
                    }}
                    name={app.groupName}
                    apps={app.children}
                    onUpdateGroupName={(name: string) => {
                      if (app.groupName === name) {
                        return;
                      }
                      updateGroupName(app.groupId, name);
                    }}
                    onMouseDown={(event: any) => {
                      runSetDragAppId(app.relationId);
                      getMouseDownGridId(grid.id);
                      onMouseDown(event, EMPTY_FN);
                    }}
                    onSortChange={(ids: string[]) => {
                      folderAppsSort(app.groupId, ids);
                    }}
                    onRefresh={() => {
                      refresh();
                    }}
                  />
                );
              }

              if (app.classify === 4) {
                return (
                  <div
                    key={app.relationId}
                    data-id={app.id}
                    data-classify={app.classify}
                    className={classNames('grid')}
                    onMouseDown={() => {
                      runSetDragAppId(app.relationId);
                      getMouseDownGridId(grid.id);
                    }}
                  >
                    {showEditScreen && (
                      <DesktopCardDelete
                        funcId={app.id}
                        classify={app.classify}
                        appId={app.echAppId}
                        onDeleteSuccess={refresh}
                      />
                    )}
                    <BlockContainer name={app.export} id={`${app.relationId}`} />
                  </div>
                );
              }
              // @ts-ignore
              const Component = componentNodes[app.componentType];

              if (!Component) {
                return (
                  <div
                    key={app.id}
                    className="grid"
                    onMouseDown={() => {
                      getMouseDownGridId(grid.id);
                    }}
                  />
                );
              }
              return (
                <div key={app.relationId} className={classNames('grid')}>
                  {showEditScreen && (
                    <DesktopCardDelete
                      funcId={app.id}
                      classify={app.classify}
                      appId={app.echAppId}
                      onDeleteSuccess={refresh}
                    />
                  )}
                  <Component
                    data={app}
                    data-id={app.id}
                    data-classify={app.classify}
                    onMouseDown={(event: any) => {
                      runSetDragAppId(app.relationId);
                      getMouseDownGridId(grid.id);
                      onMouseDown(event, EMPTY_FN);
                    }}
                  />
                </div>
              );
            }}
          />
        )}
      </SpinBox>
    </div>
  );
}

export default Desktop;
