import { IMyApp } from '@/apis';
import { Layout } from './types';

interface IState {
  apps: Array<IMyApp>;
  renderAllGridLayoutSite: Array<Array<Array<string>>>;
  renderSplitApps: Array<Array<IMyApp>>;
  renderSplitLayouts: Array<Array<Layout>>;
}

export const initState = {
  renderAllGridLayoutSite: [],
  renderSplitApps: [],
  renderSplitLayouts: [],
  apps: [],
};

type setAppsAction = {
  type: 'setApps';
  data: Array<IMyApp>;
};

type setAllGridLayoutSiteAction = {
  type: 'setAllGridLayoutSite';
  data: Array<Array<Array<string>>>;
};

type setSplitLayoutsAndAppsAction = {
  type: 'setSplitLayoutsAndApps';
  apps: Array<Array<IMyApp>>;
  layouts: Array<Array<Layout>>;
};

export type ActionType = setAppsAction | setAllGridLayoutSiteAction | setSplitLayoutsAndAppsAction;

function reducer(state: IState, action: ActionType) {
  switch (action.type) {
    // 设置所有网格坐标
    case 'setAllGridLayoutSite': {
      return {
        ...state,
        renderAllGridLayoutSite: action.data,
      };
    }
    // 设置所有分屏的布局信息和app数据
    case 'setSplitLayoutsAndApps': {
      return {
        ...state,
        renderSplitApps: action.apps,
        renderSplitLayouts: action.layouts,
      };
    }

    // 设置apps
    case 'setApps': {
      return {
        ...state,
        apps: action.data,
      };
    }

    default: {
      return { ...state };
    }
  }
}

export default reducer;
