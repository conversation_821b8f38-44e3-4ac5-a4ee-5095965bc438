.desktop {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;

  .ech-grid-layout-grid-shadow > div > div {
    backdrop-filter: blur(25px);
  }
}

.grid {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  position: relative;
  transition: all 0.3s;
  align-items: center;

  &-group {
    margin: 0 auto;
    position: relative;
  }

  &-group,
  &-container {
    width: 100%;
    height: 100%;
  }
}

@keyframes vibrate {
  0% {
    transform: translate(0);
  }

  20% {
    transform: translate(-2px, 2px);
  }

  40% {
    transform: translate(-2px, -2px);
  }

  60% {
    transform: translate(2px, 2px);
  }

  80% {
    transform: translate(2px, -2px);
  }

  100% {
    transform: translate(0);
  }
}

.vibrate {
  animation: vibrate 0.3s linear infinite;
}

.grid-layout-overlay {
  .allow-merge-card {
    border: 2px solid rgba(255, 255, 255, 0.82);
  }
}
