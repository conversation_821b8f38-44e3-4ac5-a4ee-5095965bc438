import Modal from '@/components/modal';
import { sendPermissionMessage } from '@/apis';
import i18n from 'i18next';
import { message } from '@/components/message';

export const verifyApp = (item: {
  display: number;
  name: string;
  isCharge: number;
  appId: number;
  isPermission: number;
  id: number;
  classify?: number;
  url: string;
}) => {
  if (item.classify === 3 && item.url === '') {
    message.info(i18n.t('desk_verifyApp_info'));
    return false;
  }
  if (item.classify !== 2) {
    if ([1].includes(item.display) || ([0, 2].includes(item.display) && item?.url === '')) {
      message.info(i18n.t('desk_verifyApp_infos', { name: item.name }));
      return false;
    }
    if (!item.isCharge) {
      window.location.href = `/appstore/app/${item.appId}/1?backPath=`;
      return false;
    }
    if (!item.isPermission) {
      Modal.confirm({
        content: <div>{i18n.t('desk_verifyApp_sureSendPerm', { name: item.name })}</div>,
        centered: true,
        icon: '',
        title: i18n.t('public_title'),
        okText: i18n.t('public_sure'),
        cancelText: i18n.t('public_cancel'),
        onOk: () => {
          sendPermissionMessage({ funcId: item.id }).then(() => {
            message.success(i18n.t('desk_verifyApp_sendPerm'));
          });
        },
      });

      return false;
    }
  }
  return true;
};

export const test = 1;
