.folderPopContent {
  opacity: 0;
  transition: all 0.2s;

  .folderName {
    text-align: center;
    margin-bottom: 16px;

    .nameIpt {
      width: 320px;
      margin: 0 auto;
      text-align: center;

      :global {
        color: #fff;
        font-size: 28px;
        font-weight: normal;
        line-height: 28px;
        text-align: center;
        background-color: transparent;
        border: none;
      }
    }
  }

  .contentBox {
    width: 64%;
    margin: 0 auto;
    padding: 36px 0;
    border-radius: 36px;
    background: rgb(255 255 255 / 40%);
    backdrop-filter: blur(25.6px);
  }
}
