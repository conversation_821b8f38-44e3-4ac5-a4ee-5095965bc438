import classNames from 'classnames';
import Modal from '@echronos/echos-ui/dist/modal';
import { useUserinfo } from '@echronos/react';
import { IMyApp } from '@/apis';
import useToApp from '@/hooks/use-to-app';
import { useTranslation } from 'react-i18next';
import { useRef } from 'react';
import FolderPopContent from './folderPopContent';
import { Grid } from '../../grid-layout';
import { verifyApp } from '../../utils/verify';
import styles from './folder.module.less';

export interface FolderProps {
  name: string;
  apps: IMyApp[];
  // eslint-disable-next-line no-unused-vars
  onMouseDown: (event: any) => void;
  onFolderOpen: () => void;
  onFolderClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onRemoveApp: (grid: Grid) => void;
  // eslint-disable-next-line no-unused-vars
  onUpdateGroupName: (name: string) => void;
  // eslint-disable-next-line no-unused-vars
  onSortChange: (ids: string[]) => void;
  onRefresh: () => void;
}

export const openFolderPop = ({
  apps,
  name,
  onClose,
  onRemoveApp,
  onUpdateGroupName,
  onSortChange,
  onRefresh,
}: {
  apps: IMyApp[];
  name: string;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onRemoveApp: (grid: Grid) => void;
  // eslint-disable-next-line no-unused-vars
  onUpdateGroupName: (n: string) => void;
  // eslint-disable-next-line no-unused-vars
  onSortChange: (ids: string[]) => void;
  onRefresh: () => void;
}) => {
  const instance = Modal.getInstance();
  let groupName = name;
  instance.open({
    content: (
      <FolderPopContent
        onRefresh={onRefresh}
        onRemoveApp={onRemoveApp}
        onSortChange={onSortChange}
        onChangeName={(val: string) => {
          groupName = val;
        }}
        name={name}
        apps={apps}
        onClose={() => {
          if (groupName) {
            onUpdateGroupName(groupName);
          }
          instance.close();
          onClose();
        }}
      />
    ),
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    getContainer: () => document.querySelector<HTMLDivElement>('#micro-root')!,
    footer: false,
    closable: false,
    width: '100%',
    onCancel: () => {
      if (groupName) {
        onUpdateGroupName(groupName);
      }
      instance.close();
      onClose();
    },
    maskStyle: {
      background: 'rgba(0, 0, 0, 0.1)',
      backdropFilter: 'blur(50px)',
    },
    wrapClassName: styles.folderPop,
  });
};

function Folder({
  name,
  apps,
  onMouseDown,
  onFolderOpen,
  onFolderClose,
  onRemoveApp,
  onUpdateGroupName,
  onSortChange,
  onRefresh,
}: FolderProps) {
  const { t } = useTranslation();
  const [toApp] = useToApp();
  const userInfo = useUserinfo();
  const openFolder = () => {
    onFolderOpen();
    openFolderPop({
      apps,
      name,
      onClose: onFolderClose,
      onRemoveApp,
      onUpdateGroupName,
      onSortChange,
      onRefresh,
    });
  };

  const clickApp = (app: IMyApp) => {
    if ([1, 3].includes(app.classify) && !verifyApp(app)) {
      return;
    }
    toApp(app.url, {
      id: app.id,
      icon: app.photo,
      name: app.name,
      code: app.coding,
      isOpen: app.isOpen,
      externalApp: app.externalApp,
      shopId: userInfo.shop.id,
      classify: app.classify,
      url: app.url,
    });
  };

  const tipsMap = useRef([
    null,
    {
      icon: 'noPermission',
      // tips: '申请权限',
      tips: `${t('desk_apps_noPermission')}`,
    },
    {
      icon: 'noBuy',
      // tips: '过期请购买',
      tips: `${t('desk_apps_noBuy')}`,
    },
  ]);

  const getTips = (data: IMyApp) => {
    if (data.classify !== 2) {
      return tipsMap.current[(!data.isCharge && 2) || (!data.isPermission && 1) || 0];
    }
    return null;
  };

  const renderApps = () => {
    if (apps.length <= 4) {
      return apps.map((app) => (
        <div className={styles.appBox} key={app.relationId}>
          <div
            className={styles.app}
            data-id={app.id}
            data-classify={app.classify}
            role="button"
            tabIndex={0}
            onClick={(e) => {
              e.stopPropagation();
              clickApp(app);
            }}
          >
            <img src={app.photo} alt="" className={styles.appImg} />
            {getTips(app) && (
              <div className={styles.tipsMask}>
                <div className={classNames(styles.tipsIcon, styles[getTips(app)?.icon || ''])} />
                <div className={styles.tipsText}>{getTips(app)?.tips}</div>
              </div>
            )}
          </div>
        </div>
      ));
    }
    return apps.slice(0, 3).map((app) => (
      <div className={styles.appBox} key={app.relationId}>
        <div
          className={styles.app}
          data-id={app.id}
          data-classify={app.classify}
          role="button"
          tabIndex={0}
          onClick={(e) => {
            e.stopPropagation();
            clickApp(app);
          }}
        >
          <img src={app.photo} alt="" className={styles.appImg} />
          {getTips(app) && (
            <div className={styles.tipsMask}>
              <div className={classNames(styles.tipsIcon, styles[getTips(app)?.icon || ''])} />
              <div className={styles.tipsText}>{getTips(app)?.tips}</div>
            </div>
          )}
        </div>
      </div>
    ));
  };

  const renderMoreApps = () => (
    <div className={styles.appBox}>
      <div className={styles.appsList}>
        {apps.slice(3, 7).map((app) => (
          <div className={styles.appBox} key={app.relationId}>
            <div className={styles.app}>
              <img src={app.photo} alt="" />
              {getTips(app) && (
                <div className={styles.tipsMask}>
                  <div className={classNames(styles.tipsIcon, styles[getTips(app)?.icon || ''])} />
                  <div className={styles.tipsText}>{getTips(app)?.tips}</div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div className={classNames(styles.folder)} onMouseDown={onMouseDown}>
      <div
        className={classNames(styles.folderContent, 'allow-merge-card')}
        role="button"
        tabIndex={0}
        onClick={openFolder}
      >
        {renderApps()}
        {apps.length > 4 && renderMoreApps()}
      </div>
      <div className={styles.folderName}>{name}</div>
    </div>
  );
}

export default Folder;
