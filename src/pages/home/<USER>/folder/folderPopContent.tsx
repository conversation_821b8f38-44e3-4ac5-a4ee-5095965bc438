import { Input } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useMount, useSize, useUnmount } from 'ahooks';
import classNames from 'classnames';
import { useUserinfo } from '@echronos/react';
import { openURL } from '@echronos/core';
import { IMyApp, addOrRemoveApp } from '@/apis';
import useToApp from '@/hooks/use-to-app';
import { ItemType, useDesktopMenu } from '@/components/desktop-menu';
import { message } from '@/components/message';
import editCustomApp from '@/containers/custom-app';
import GridLayout, { Grid } from '../../grid-layout';
import * as componentNodes from '../apps';
import { verifyApp } from '../../utils/verify';
import styles from './folderPopContent.module.less';

export interface FolderProps {
  name: string;
  apps: IMyApp[];
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onRemoveApp: (grid: Grid) => void;
  // eslint-disable-next-line no-unused-vars
  onChangeName: (name: string) => void;
  // eslint-disable-next-line no-unused-vars
  onSortChange: (ids: string[]) => void;
  onRefresh: () => void;
}
export type ContainerSizeType = 'xxs' | 'xs' | 'sm' | 'm' | 'md' | 'lg' | 'l' | 'xl' | 'xxl';
export type PageGridColumns = Record<ContainerSizeType, number>;
export interface GridSize {
  width: number;
  height: number;
}
// 网格尺寸类型
const gridSizeType: GridSize[] = [
  { width: 87, height: 96 },
  { width: 170, height: 110 },
];

function FolderPopContent({
  name,
  apps,
  onClose,
  onRemoveApp,
  onChangeName,
  onSortChange,
  onRefresh,
}: FolderProps) {
  const userInfo = useUserinfo();
  const [toApp, navigate] = useToApp();
  const sliderContainerEl = useRef(null as unknown as HTMLDivElement);
  const folderPopContentEl = useRef(null as unknown as HTMLDivElement);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const elSize = useSize(sliderContainerEl) || { width: 0, height: 0 };
  // 每种屏幕尺寸下每行的列数
  const defaultPageGridColumns: PageGridColumns = {
    xxs: 2,
    xs: 3,
    sm: 4,
    m: 5,
    md: 6,
    l: 7,
    lg: 8,
    xl: 10,
    xxl: 10,
  };
  const [dragAppId, setDragAppId] = useState<number | null>(null);
  const cacheLongPressTimer = useRef(null as any);
  const gridLayoutInfoBg = useRef({
    cols: defaultPageGridColumns.xs,
    gridSize: gridSizeType[0],
  });
  const [containerHeight, setContainerHeight] = useState(0);
  const [renderApps, setRenderApps] = useState<Grid[]>([]);
  const [folderPopContentStyle, setFolderPopContentStyle] = useState({});
  const longPressTime = 80;
  const selectAppRef = useRef<IMyApp | null>();

  useEffect(() => {
    const a = apps?.map((item) => ({
      id: `${item.relationId}`,
      allowOverlay: true,
      w: 1,
      h: 1,
      ext: item,
    }));
    setRenderApps(a);
  }, [apps]);

  const gridLayoutInfo = useMemo(() => {
    if (!elSize.width) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.xs,
        gridSize: gridSizeType[0],
      };
      return gridLayoutInfoBg.current;
    }
    const { width } = elSize;

    if (width > 1700) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.xl,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 1360) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.lg,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 1190) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.l,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 1020) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.md,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 850) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.m,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 680) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.sm,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    if (width > 510) {
      gridLayoutInfoBg.current = {
        cols: defaultPageGridColumns.xs,
        gridSize: gridSizeType[1],
      };
      return gridLayoutInfoBg.current;
    }
    gridLayoutInfoBg.current = {
      cols: defaultPageGridColumns.xxs,
      gridSize: gridSizeType[0],
    };
    return gridLayoutInfoBg.current;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [elSize]);

  const runSetDragAppId = (id: number) => {
    cacheLongPressTimer.current = setTimeout(() => {
      setDragAppId(id);
    }, longPressTime);
  };

  const windowMouseUp = () => {
    if (cacheLongPressTimer.current) {
      clearInterval(cacheLongPressTimer.current);
    }
    setDragAppId(null);
  };

  const windowMousemove = () => {
    if (!dragAppId) {
      if (cacheLongPressTimer.current) {
        clearInterval(cacheLongPressTimer.current);
      }
      setDragAppId(null);
    }
  };

  const clickApp = (app: IMyApp) => {
    if ([1, 3].includes(app.classify) && !verifyApp(app)) {
      return;
    }
    toApp(app.url, {
      id: app.id,
      icon: app.photo,
      name: app.name,
      code: app.coding,
      isOpen: app.isOpen,
      externalApp: app.externalApp,
      shopId: userInfo.shop.id,
      classify: app.classify,
      url: app.url,
    });
  };

  const getSelectApp = (e: React.MouseEvent): IMyApp | null => {
    let el = e.target as HTMLElement | null;
    while (el && !el.getAttribute('data-id')) {
      el = el.parentElement;
    }

    if (el && apps) {
      const id = Number(el.getAttribute('data-id'));
      const classify = Number(el.getAttribute('data-classify'));
      return (
        apps.find(
          classify === 0
            ? (item) => item.id === id
            : (item) => item.id === id && item.classify === classify
        ) || null
      );
    }
    return null;
  };

  useMount(() => {
    setTimeout(() => {
      const gridRows = Math.ceil(apps.length / gridLayoutInfoBg.current.cols);
      let height = gridRows * gridLayoutInfoBg.current.gridSize.height;
      if (height > window.innerHeight * 0.7) {
        height =
          Math.floor((window.innerHeight * 0.7) / gridLayoutInfoBg.current.gridSize.height) *
          gridLayoutInfoBg.current.gridSize.height;
      }
      setContainerHeight(height);
      setFolderPopContentStyle({ opacity: 1 });
    }, 200);
    document.addEventListener('mouseup', windowMouseUp);
    document.addEventListener('mousemove', windowMousemove);
  });

  useUnmount(() => {
    document.removeEventListener('mouseup', windowMouseUp);
    document.removeEventListener('mousemove', windowMousemove);
  });

  useDesktopMenu(folderPopContentEl, {
    items: (value) => {
      const app = getSelectApp(value);

      selectAppRef.current = null;
      if (app) {
        if (app.componentType !== 'app' && app.classify !== 4) {
          return null;
        }
        selectAppRef.current = app;
        return [
          {
            key: 'openSelf',
            langKey: 'desk_openSelf',
            label: '当前页面打开',
            icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462092069885.png`,
          },
          {
            key: 'openBlank',
            langKey: 'desk_openBlank',
            label: '新标签页打开',
            icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462130354690.png`,
          },
          app.classify === 2
            ? {
                key: 'edit',
                langKey: 'desk_edit',
                label: '重命名',
                icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462332528373.png`,
              }
            : null,
          {
            key: 'refresh',
            langKey: 'desk_refresh',
            label: '刷新',
            icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724327838456666.png`,
          },
          { type: 'divider' },
          {
            key: 'delete',
            langKey: 'desk_delete',
            label: '删除',
            icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724462170921508.png`,
          },
        ] as ItemType[];
      }
      return [
        {
          key: 'refresh',
          langKey: 'desk_refresh',
          label: '刷新',
          icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724327838456666.png`,
        },
      ];
    },
    onSelect: ({ key }) => {
      const app = selectAppRef.current;
      if (key === 'refresh') {
        setRenderApps([]);
        setTimeout(() => {
          setRenderApps([...renderApps]);
        }, 200);
        onRefresh();
        return;
      }
      if (!app) {
        return;
      }
      if (key === 'delete') {
        addOrRemoveApp({
          funcId: app.id,
          flag: 1,
          classify: app.classify,
          appId: app.classify === 3 ? app.echAppId : null,
        }).then(() => {
          message.success('删除成功');
          const index = renderApps.findIndex((it) => it.id === `${app.relationId}`);
          renderApps.splice(index, 1);

          setRenderApps([...renderApps]);
          onRefresh();
        });
      } else if (key === 'edit') {
        editCustomApp(app.id).then((data: any) => {
          const index = renderApps.findIndex((it) => it.id === `${app.relationId}`);
          renderApps[index].ext.name = data.name;
          renderApps[index].ext.photo = data.photo;

          setRenderApps([...renderApps]);
          onRefresh();
        });
      } else if (verifyApp(app)) {
        if (key === 'openSelf') {
          if (/^(https?:)?\/\//.test(app.url)) {
            const link = document.createElement('a');
            link.href = app.url;
            link.rel = 'noreferrer';
            // @ts-ignore
            link.referrerpolicy = 'no-referrer';
            link.click();
            link.remove();
          } else {
            navigate(app.url);
          }
        } else {
          openURL(app.url);
        }
      }
    },
    userInfo: userInfo.user,
  });

  return (
    <div
      className={styles.folderPopContent}
      ref={folderPopContentEl}
      role="button"
      tabIndex={0}
      data-content="folderPopContent"
      onClick={(e) => {
        // @ts-ignore
        if (e.target && e.target.getAttribute('data-content') === 'folderPopContent') {
          onClose();
        }
      }}
      style={folderPopContentStyle}
    >
      <div
        className={styles.folderName}
        role="button"
        tabIndex={0}
        data-content="folderName"
        onClick={(e) => {
          // @ts-ignore
          if (e.target && e.target.getAttribute('data-content') === 'folderName') {
            onClose();
          }
        }}
      >
        <Input
          defaultValue={name}
          className={styles.nameIpt}
          maxLength={10}
          onBlur={(e) => {
            onChangeName(e.target.value);
          }}
        />
      </div>
      <div className={styles.contentBox} role="button" tabIndex={0}>
        <div ref={sliderContainerEl} style={{ height: `${containerHeight}px` }}>
          {containerHeight > 0 && (
            <GridLayout
              cols={gridLayoutInfo.cols}
              gridSize={gridLayoutInfo.gridSize}
              longPressTime={longPressTime}
              drag
              onSortChange={onSortChange}
              ondragedInOutside={(grid) => {
                onRemoveApp(grid);
                onClose();
              }}
              // eslint-disable-next-line react/no-unstable-nested-components
              gridRender={(grid, getMouseDownGridId) => {
                const app = grid.ext;
                // @ts-ignore
                const Component = componentNodes[app.componentType];

                return (
                  <div
                    key={app.relationId}
                    data-id={app.id}
                    data-classify={app.classify}
                    className={classNames('grid')}
                  >
                    <Component
                      onClick={() => {
                        clickApp(app);
                      }}
                      data={app}
                      data-id={app.id}
                      data-classify={app.classify}
                      onMouseDown={() => {
                        runSetDragAppId(app.relationId);
                        getMouseDownGridId(grid.id);
                      }}
                    />
                  </div>
                );
              }}
              data={renderApps}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default FolderPopContent;
