.folder {
  width: calc(100% - 24px);
  height: calc(100% - 12px);
  margin: 12px;

  .folderContent {
    display: flex;
    height: calc(100% - 50px);
    // align-items: center;
    padding: 24px 40px;
    justify-content: flex-start;
    border-radius: 18px;
    backdrop-filter: blur(25.6px);
    background: rgb(255 255 255 / 40%);
    flex-wrap: wrap;
  }

  .folderName {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    margin-top: 6px;
    overflow: hidden; /* 隐藏溢出内容 */
    text-align: center;
    text-shadow: 0 0 13px rgb(7 20 103 / 60%);
    white-space: nowrap; /* 不换行 */
    text-overflow: ellipsis;
  }

  .appBox {
    display: flex;
    width: 50%;
    height: 50%;
    justify-content: center;
    align-items: center;
  }

  .app,
  .appsList {
    width: 68px;
    height: 68px;
  }

  .app {
    overflow: hidden;
    position: relative;
    border-radius: 16px;

    .tipsMask {
      display: none;
      justify-content: center;
      position: absolute;
      inset: 0;
      background: rgb(0 0 0 / 40%);
      border: 0.5px solid rgb(0 0 0 / 0%);
      backdrop-filter: blur(5px);
      align-items: center;
      flex-direction: column;
      cursor: pointer;
    }

    &:hover {
      .tipsMask {
        display: flex;
      }
    }
  }

  .appsList {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    // align-items: center;

    .app {
      width: 25px;
      height: 25px;
      margin: 0 2px;
      border-radius: 6px;
    }
  }

  .appImg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .tipsIcon {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
  }

  .noPermission {
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689851982755421.png');
    background-size: 100% 100%;
  }

  .noBuy {
    background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023720/1689852915033604.png');
    background-size: 100% 100%;
  }

  .tipsText {
    color: #fff;
    font-size: 12px;
    text-align: center;
  }
}

.folderPop {
  :global {
    .ant-modal {
      box-shadow: none;
    }

    .ant-modal-content {
      background-color: transparent;
      box-shadow: none;
    }
  }
}

@media screen and (width <= 600px) {
  .folder {
    width: calc(100% - 12px);
    height: calc(100% - 12px);
    margin: 12px 6px;
  }

  .app,
  .appsList {
    width: 40px !important;
    height: 40px !important;
    margin: 0 4px !important;
  }

  .folderContent {
    padding: 12px 24px !important;
  }

  .appImg {
    border-radius: 8px !important;
  }
}
