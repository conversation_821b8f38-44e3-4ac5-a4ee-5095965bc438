import dayjs from 'dayjs';
import { ViewCols } from '../types';

export const gridSize = {
  w: 140,
  h: 140,
};

export const getGridSize = (containerWidth: number) => {
  if (containerWidth <= 560) {
    return {
      w: 87,
      h: 96,
    };
  }
  return {
    w: 140,
    h: 140,
  };
};

export const getColsCount = (containerWidth: number, cols: ViewCols) => {
  let colsCount = 0;
  if (containerWidth >= 1400) {
    colsCount = cols.xl;
  } else if (containerWidth >= 1120) {
    colsCount = cols.lg;
  } else if (containerWidth >= 840) {
    colsCount = cols.md;
  } else if (containerWidth >= 560) {
    colsCount = cols.sm;
  } else if (containerWidth < 560) {
    colsCount = cols.xs;
  }
  return colsCount;
};

export const createRow = (containerWidth: number, cols: ViewCols) =>
  new Array(getColsCount(containerWidth, cols)).fill('null');

export const getGridLayoutSiteByEle = (ele: HTMLDivElement, cols: ViewCols) => {
  const site: Array<Array<string>> = [];

  const rows = Math.floor(ele.clientHeight / getGridSize(ele.clientWidth).h);

  for (let i = 0; i < rows; i += 1) {
    site[i] = createRow(ele.clientWidth, cols);
  }

  return site;
};

/**
 * 格式化时间
 * @param time 时间戳
 * @param detail 是否显示时分
 * @returns 格式化后的时间 2023-10-11 or 2023-10-11 10:10
 * */
export const formatTime = (time: number, detail = false) => {
  if (detail) return dayjs(time).format('YYYY-MM-DD HH:mm');
  return dayjs(time).format('YYYY-MM-DD');
};
