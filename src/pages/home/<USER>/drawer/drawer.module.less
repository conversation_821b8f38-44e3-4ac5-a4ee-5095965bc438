.drawerPopContainer {
  :global {
    .ant-drawer-body {
      padding: 0 !important;
    }

    .ant-drawer-content {
      background: transparent;
    }

    .ant-drawer-content-wrapper {
      // border-top-right-radius: 18px;
      // border-top-left-radius: 18px;
      overflow: hidden;
      border-radius: 18px;
      background: rgb(245 246 250 / 70%) !important;
      backdrop-filter: blur(100px);
      // stylelint-disable-next-line
      -webkit-backdrop-filter: blur(100px);
    }
  }
}
