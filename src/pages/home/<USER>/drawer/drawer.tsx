import ReactDOM from 'react-dom';
import { Drawer, DrawerProps } from 'antd';
import { ReactNode } from 'react';
import classNames from 'classnames';
import styles from './drawer.module.less';
import './drawer.less';

interface DrawerPopContainerProps extends DrawerProps {
  content: ReactNode;
  onCancel?: () => void;
  containerId?: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  height?: string;
}

function DrawerPopContainer({
  content,
  height,
  containerId,
  placement,
  ...prop
}: DrawerPopContainerProps) {
  const container = document.getElementById(containerId || ('desktop' as string));
  return (
    <Drawer
      placement={placement}
      className={classNames(styles.drawerPopContainer)}
      visible
      mask={prop.mask || false}
      height={height}
      closable={false}
      getContainer={container as HTMLElement}
      {...prop}
    >
      {content}
    </Drawer>
  );
}

DrawerPopContainer.defaultProps = {
  onCancel: null,
  containerId: '',
  placement: 'right',
  height: '',
};

function DrawerPop() {
  const container = document.createDocumentFragment();
  function destroy() {
    ReactDOM.unmountComponentAtNode(container);
  }

  function close() {
    destroy();
  }
  function render({
    content,
    height,
    containerId,
    placement,
    onCancel,
    ...props
  }: DrawerPopContainerProps) {
    ReactDOM.render(
      <DrawerPopContainer
        content={content}
        height={height}
        containerId={containerId}
        placement={placement}
        onClose={() => {
          if (onCancel) {
            onCancel();
          }
          close();
        }}
        {...props}
      />,
      container
    );
  }
  function open(props: DrawerPopContainerProps) {
    render(props);
  }

  return {
    open,
    close,
  };
}

export default DrawerPop;
