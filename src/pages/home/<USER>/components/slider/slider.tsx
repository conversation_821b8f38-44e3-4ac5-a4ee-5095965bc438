import classNames from 'classnames';
import { isArray } from 'lodash';
import {
  forwardRef,
  HTMLAttributes,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  WheelEventHandler,
} from 'react';
import Slick from 'react-slick';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import './slider.less';

// @ts-ignore
const Slicker = Slick.default || Slick;

export interface SliderInstance {
  slickNext: () => void;
  slickPrev: () => void;
  slickPause: () => void;
  // eslint-disable-next-line no-unused-vars
  slickGoTo: (num: number) => void;
}

const Slider = forwardRef<
  SliderInstance,
  HTMLAttributes<HTMLDivElement> & {
    // eslint-disable-next-line no-unused-vars
    afterChange?: (index: number) => void;
    // eslint-disable-next-line react/require-default-props
    beforeChange?: () => void;
  }
>(({ children, className, afterChange, beforeChange, ...props }, ref) => {
  const sliderRef = useRef(null as unknown as Slick);

  useImperativeHandle(ref, () => ({
    slickNext: () => {
      sliderRef.current?.slickNext();
    },
    slickPrev: () => {
      sliderRef.current?.slickPrev();
    },
    slickPause: () => {
      sliderRef.current?.slickPause();
    },
    slickGoTo: (num: number) => {
      sliderRef.current.slickGoTo(num);
    },
  }));

  const onScroll: WheelEventHandler = useCallback((e) => {
    if (e.deltaY > 0) {
      sliderRef.current?.slickNext();
    } else {
      sliderRef.current?.slickPrev();
    }
  }, []);
  const appendDots = useCallback(
    (dots: ReactNode[]) => (
      <ul style={{ margin: '0px' }}>
        {dots.map((it, index) => (
          <li
            className="dot-box"
            role="presentation"
            key={(it as any).key}
            onClick={() => {
              sliderRef.current.slickGoTo(index);
            }}
          >
            <div className={`dot ${(it as any).props.className === 'slick-active' && 'active'}`} />
          </li>
        ))}
      </ul>
    ),
    []
  );

  const len = isArray(children) ? children.length : 0;
  useEffect(() => {
    const el = len && sliderRef.current.innerSlider?.list;
    if (el) {
      const activeSide = el.querySelector('.slick-active');

      if (!activeSide) {
        sliderRef.current.slickGoTo(len - 1);
      }
    }
  }, [len]);

  return (
    <div {...props} className={classNames('m-slider-container', className)} onWheel={onScroll}>
      <Slicker
        ref={sliderRef}
        dots
        speed={500}
        infinite={false}
        arrows={false}
        draggable={false}
        slidesToShow={1}
        slidesToScroll={1}
        afterChange={afterChange}
        appendDots={appendDots}
        beforeChange={beforeChange}
        className="slider-box"
      >
        {children}
      </Slicker>
    </div>
  );
});

Slider.defaultProps = {
  afterChange: () => {},
};

export default Slider;
