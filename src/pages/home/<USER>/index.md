---
toc: content # 导航在内容区才显示，在左侧导航栏不显示
title: GridLayout 格子布局 # 组件的标题，会在菜单侧边栏展示
nav: 组件 # 约定式路由
group: # 配置当前页所属的侧边菜单分组，未配置时不会展示分组。
  title: 布局 # 分组的名称
---

# GridLayout 格子布局

## 示例

<code src="./demo/index.tsx">基础用法</code>

## GridLayout Params

|     参数      |        说明        |                                 类型                                  |
| :-----------: | :----------------: | :-------------------------------------------------------------------: |
|  gridRender   |      格子渲染      | "(grid: Grid, getMouseDownGridId: (id: string) => void) => ReactNode" |
|     cols      |        列数        |                                number                                 |
|   gridSize    |     格子的大小     |                               GridSize                                |
|     data      |      布局数据      |                               Object[]                                |
|     drag      |    是否可以拖拽    |                                boolean                                |
| onSortChange  |      排序回调      |                        (ids: string[]) => void                        |
| longPressTime | 长按开启拖拽的时间 |                                number                                 |
|  labelClass   |     标题的样式     |                                string                                 |
