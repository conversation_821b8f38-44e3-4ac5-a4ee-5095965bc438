// @ts-nocheck
import GridLayout from '@echronos/echos-ui/dist/grid-layout'
import '@echronos/echos-ui/dist/index.css'
import { useSize } from 'ahooks'
import React, { useEffect, useRef, useState } from 'react'
import './index.less'

function Demo() {
  const containerRef = useRef(null)
  const layoutContainerSize = useSize(containerRef) || { width: 0, height: 0 }
  const [apps, setData] = useState([] as any)

  const [cols, setCols] = useState(8)

  const data1 = [
    {
      id: '1',
      w: 1,
      h: 1,
    },
    {
      id: '2',
      w: 2,
      h: 2,
    },
    {
      id: '3',
      w: 2,
      h: 1,
    },
    {
      id: '4',
      w: 4,
      h: 2,
    },
    {
      id: '5',
      w: 1,
      h: 1,
    },
    {
      id: '9',
      w: 1,
      h: 1,
    },
    {
      id: '10',
      w: 1,
      h: 1,
    },
    {
      id: '11',
      w: 1,
      h: 1,
    },
    {
      id: '12',
      w: 1,
      h: 1,
    },
    {
      id: '13',
      w: 1,
      h: 1,
    },
    {
      id: '14',
      w: 1,
      h: 1,
    },
    {
      id: '15',
      w: 1,
      h: 1,
    },
    {
      id: '16',
      w: 1,
      h: 1,
    },
    {
      id: '6',
      w: 2,
      h: 2,
    },
    {
      id: '7',
      w: 1,
      h: 1,
    },
    {
      id: '8',
      w: 2,
      h: 2,
    },
    {
      id: '17',
      w: 1,
      h: 1,
    },
    {
      id: '18',
      w: 1,
      h: 1,
    },
    {
      id: '19',
      w: 1,
      h: 1,
    },
    {
      id: '20',
      w: 1,
      h: 1,
    },
    {
      id: '21',
      w: 1,
      h: 1,
    },
  ]

  // const data2 = [
  //   {
  //     id: '1',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '2',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '3',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '4',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '5',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '6',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '7',
  //     w: 1,
  //     h: 1,
  //   },
  //   {
  //     id: '8',
  //     w: 1,
  //     h: 1,
  //   },
  // ];

  const computeCols = () => {
    if (layoutContainerSize.width < 1120) {
      setCols(6)
    }
    if (layoutContainerSize.width < 840) {
      setCols(4)
    }
  }

  useEffect(() => {
    setTimeout(() => {
      setData(data1)
    }, 1000)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (layoutContainerSize.width && layoutContainerSize.height) {
      computeCols()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [layoutContainerSize.width, layoutContainerSize.height])

  return (
    <div className='m-grid-layout-container' ref={containerRef}>
      <GridLayout
        cols={cols}
        drag
        onSortChange={(ids: string[]) => {
          console.log('ids---', ids)
        }}
        gridRender={(grid, getMouseDownGridId) => (
          <div
            className='m-grid-layout-container-card'
            onMouseDown={() => {
              getMouseDownGridId(grid.id)
            }}
          >
            {grid.id}
          </div>
        )}
        data={apps}
      />
    </div>
  )
}

export default Demo
