.m-grid-layout-container {
  width: 100%;
  height: 600px;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px);
  }
}

.m-grid-layout-container-card {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  background-color: #999;
  border-radius: 20px;
  margin: 10px;
  text-align: center;
  cursor: pointer;

  &:active {
    // animation: shake 0.3s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  }
}
