@prefix-cls: ~'ech-grid-layout';

.@{prefix-cls} {
  width: 100%;
  height: 100%;

  &-slider-box {
    height: 100%;
  }

  &-conatiner {
    height: 100%;
    margin: 0 auto;
    position: relative;
  }

  &-grid {
    cursor: pointer;

    &.transition {
      transition: all 0.2s;
    }

    // &.draging {
    //   opacity: 0.5;
    // }

    img {
      -webkit-user-drag: none;
      user-select: none;
    }

    .mark {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9999;
    }
  }

  &-grid-shadow {
    opacity: 0.5;
  }
}
