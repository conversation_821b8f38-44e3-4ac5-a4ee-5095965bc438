.contextMenu {
  padding: 5px;
  position: fixed;
  border-radius: 6px;
  background: rgb(246 246 246 / 60%);
  backdrop-filter: blur(80px);
  box-shadow: 0 0 1px 0 rgb(0 0 0 / 40%), 0 0 1.5px 0 rgb(0 0 0 / 30%),
    0 7px 22px 0 rgb(0 0 0 / 25%);
}

.menuItem {
  color: #040919;
  font-size: 12px;
  font-weight: normal;
  min-width: 199px;
  line-height: 20px;
  padding: 1px 10px;
  position: relative;
  cursor: pointer;
  letter-spacing: 0;

  &:hover {
    border-radius: 5px;
    background: linear-gradient(0deg, rgb(0 140 255 / 75%), rgb(0 140 255 / 75%)), #008cff;
    color: #fff;
  }
}

.spliteLine {
  width: 90%;
  height: 1px;
  margin: 4px auto;
  background: rgb(0 0 0 / 10%);
}

.optGroup {
  &:last-child {
    .spliteLine {
      display: none;
    }
  }
}
