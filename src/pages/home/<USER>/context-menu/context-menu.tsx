import ReactDOM from 'react-dom';
import styles from './context-menu.module.less';

interface MenuOpt {
  label: string;
  key: string;
}

interface ContextMenuProps {
  clientX: number;
  clientY: number;
  options: MenuOpt[][];
  // eslint-disable-next-line no-unused-vars
  onSelect: (key: string) => void;
}

function ContextMenuContent({ clientX, clientY, options, onSelect }: ContextMenuProps) {
  return (
    <div
      className={styles.contextMenu}
      style={{
        top: `${clientY}px`,
        left: `${clientX}px`,
      }}
    >
      {options.map((item, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <div className={styles.optGroup} key={index}>
          {item.map((it) => (
            <div
              className={styles.menuItem}
              tabIndex={0}
              role="button"
              id={it.key}
              key={it.key}
              onClick={() => {
                onSelect(it.key);
              }}
            >
              {it.label}
            </div>
          ))}
          <div className={styles.spliteLine} />
        </div>
      ))}
    </div>
  );
}
let container = null as unknown as HTMLDivElement;

function ContextMenu() {
  const desktopEle = document.getElementById('desktop');
  function destroy() {
    if (container) {
      ReactDOM.unmountComponentAtNode(container);
      desktopEle?.removeChild(container);
      container = null as unknown as HTMLDivElement;
      // eslint-disable-next-line no-use-before-define
      document.removeEventListener('click', close);
    }
  }

  function close() {
    destroy();
  }

  function open(params: ContextMenuProps & { clickHide?: boolean }) {
    close();
    container = document.createElement('div');
    if (params.clickHide !== false) {
      document.addEventListener('click', close);
    }
    if (desktopEle) {
      desktopEle.appendChild(container);
    }
    ReactDOM.render(
      <ContextMenuContent
        clientY={params.clientY}
        clientX={params.clientX}
        options={params.options}
        onSelect={(key) => {
          params.onSelect(key);
          close();
        }}
      />,
      container
    );
  }

  return {
    open,
    close,
  };
}

export const clearContextMenuConatiner = () => {
  container = null as unknown as HTMLDivElement;
};

export default ContextMenu;
