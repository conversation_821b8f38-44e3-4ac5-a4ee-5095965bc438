.stepBox {
  position: absolute;
  right: 375px;
  bottom: 200px;
}

.tips {
  max-width: none;

  :global {
    .ant-tooltip-inner {
      padding: 0 !important;
      border-radius: 12px;
    }

    .ant-tooltip-arrow::before {
      background: #006eff;
    }

    .ant-tooltip-arrow-content {
      --antd-arrow-background-color: #006eff;
    }
  }
}

.tipBox {
  width: 290px;
  height: 185px;
  padding: 20px;
  background: #006eff;
  border-radius: 12px;

  .tipTitle {
    font-size: 18px;
    margin-bottom: 12px;
  }

  .tipItem {
    display: flex;
    margin-bottom: 8px;
  }

  .dotItem {
    margin-right: 5px;

    &::before {
      content: '•';
      color: #00c6ff;
    }
  }
}

.dot {
  width: 60px;
  height: 36px;
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690190248068623.png');
  background-size: contain;
  position: absolute;
  background-repeat: no-repeat;
}

.pointer {
  width: 47px;
  height: 52px;
  background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023724/1690190443859996.png');
  background-size: contain;
  position: absolute;
  top: 8px;
  left: 8px;
}
