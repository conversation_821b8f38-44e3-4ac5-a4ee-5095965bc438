import ReactDOM from 'react-dom';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import styles from './add-app-step.module.less';

function AddAppStepContent() {
  // 多语言导入
  const { t } = useTranslation();
  return (
    <div className={styles.stepBox}>
      <Tooltip
        placement="right"
        autoAdjustOverflow
        overlayClassName={styles.tips}
        title={
          <div className={styles.tipBox}>
            <div className={styles.tipTitle}>{t('desk_guide_clickAddApp')}</div>
            <div>{t('desk_guide_clickAddAppTo')}</div>
          </div>
        }
        visible
      >
        <div className={classNames(styles.station, 'station')} />
      </Tooltip>
    </div>
  );
}

function AddAppStep() {
  const addAppBtn = document.getElementById('add-app');
  let container = null as unknown as HTMLDivElement;

  function run() {
    container = document.createElement('div');
    if (addAppBtn) {
      addAppBtn.appendChild(container);
    }
    ReactDOM.render(<AddAppStepContent />, container);
  }

  function destroy() {
    if (container) {
      ReactDOM.unmountComponentAtNode(container);
      addAppBtn?.removeChild(container);
    }
  }

  function close() {
    destroy();
  }

  return {
    run,
    close,
  };
}

export default AddAppStep;
