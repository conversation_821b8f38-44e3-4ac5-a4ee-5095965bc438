.stepBox {
  // // position: absolute;
  // top: 300px;
  // right: 300px;
}

.tipBox {
  width: 290px;
  height: 97px;
  padding: 20px;
  background: #006eff;
  border-radius: 12px;
}

.tipTitle {
  font-size: 18px;
  margin-bottom: 12px;
}

.tips {
  max-width: none;

  :global {
    .ant-tooltip-inner {
      padding: 0 !important;
      border-radius: 12px;
    }

    .ant-tooltip-arrow::before {
      background: #006eff;
    }

    .ant-tooltip-arrow-content {
      --antd-arrow-background-color: #006eff;
    }
  }
}

.station {
  width: 100%;
  position: absolute;
  top: 15px;
  right: 0;
}
