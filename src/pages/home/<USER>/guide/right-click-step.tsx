import ReactDOM from 'react-dom';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './right-click-step.module.less';

function RightClickStepContent() {
  // 多语言导入
  const { t } = useTranslation();
  return (
    <div className={styles.stepBox}>
      <Tooltip
        placement="right"
        autoAdjustOverflow
        overlayClassName={styles.tips}
        title={
          <div className={styles.tipBox}>
            <div className={styles.tipTitle}>{t('desk_guide_addApp')}</div>
            <div className={styles.tipItem}>
              <div className={styles.dotItem} />
              <div>{t('desk_guide_deskMessage')}</div>
            </div>
            <div className={styles.tipItem}>
              <div className={styles.dotItem} />
              <div>{t('desk_guide_pleRgClick')}</div>
            </div>
          </div>
        }
        visible
      >
        <div className={styles.dot}>
          <div className={styles.pointer} />
        </div>
      </Tooltip>
    </div>
  );
}

function RightClickStep() {
  const desktopEle = document.getElementById('desktop');
  let container = null as unknown as HTMLDivElement;

  function run() {
    container = document.createElement('div');
    if (desktopEle) {
      desktopEle.appendChild(container);
    }
    ReactDOM.render(<RightClickStepContent />, container);
  }

  function destroy() {
    if (container) {
      ReactDOM.unmountComponentAtNode(container);
      desktopEle?.removeChild(container);
    }
  }

  function close() {
    destroy();
  }

  return {
    run,
    close,
  };
}

export default RightClickStep;
