import { useTranslation } from 'react-i18next';
import RightClickStep from './right-click-step';
import AddAppStep from './add-app-step';
import ContextMenu from '../context-menu/context-menu';
import AllAppPop from '../all-app-pop/all-app-pop';

function Guide() {
  const { t } = useTranslation();
  let rightClickStepInstance = null as unknown as {
    run: () => void;
    close: () => void;
  };
  let addAppStepInstance = null as unknown as {
    run: () => void;
    close: () => void;
  };
  function runAddAppStep(e: any, added: () => void, closeGuide: () => void) {
    // @ts-ignore
    ContextMenu().open({
      clientX: e.clientX,
      clientY: e.clientY,
      clickHide: false,
      options: [
        [
          {
            label: t('desk_addApp'),
            key: 'add-app',
          },
        ],
      ],
      onSelect: (key: string) => {
        if (key === 'add-app') {
          AllAppPop().open({
            isGuide: true,
            added,
            closeGuide,
          });
        }
      },
    });
    addAppStepInstance = AddAppStep();
    addAppStepInstance.run();

    const addAppBtn = document.getElementById('add-app');

    function closeAddAppStep() {
      addAppBtn?.removeEventListener('click', closeAddAppStep);
      addAppStepInstance.close();
    }

    addAppBtn?.addEventListener('click', closeAddAppStep);
  }

  function runRightClickStep() {
    return new Promise((resolve) => {
      const desktopEle = document.getElementById('desktop');
      rightClickStepInstance = RightClickStep();
      rightClickStepInstance.run();
      function closeRightClickStep(e: any) {
        desktopEle?.removeEventListener('contextmenu', closeRightClickStep);
        rightClickStepInstance.close();
        resolve(e);
      }
      desktopEle?.addEventListener('contextmenu', closeRightClickStep);
    });
  }

  function start({ added, closeGuide }: { added: () => void; closeGuide: () => void }) {
    runRightClickStep().then((e) => {
      setTimeout(() => {
        runAddAppStep(e, added, closeGuide);
      });
    });
  }

  function destroy() {
    if (rightClickStepInstance) {
      rightClickStepInstance.close();
    }
    if (addAppStepInstance) {
      addAppStepInstance.close();
    }
  }

  return {
    start,
    destroy,
  };
}

export default Guide;
