import { message } from 'antd';
import classnames from 'classnames';
import { useTranslation } from 'react-i18next';
import { AddOrRemoveAppParams, addOrRemoveApp } from '@/apis';
import styles from './index.module.less';

interface DesktopCardDeleteProps {
  funcId: number;
  classify: number;
  appId: string;
  onDeleteSuccess: () => void;
}

function DesktopCardDelete({ funcId, classify, appId, onDeleteSuccess }: DesktopCardDeleteProps) {
  // 多语言导入
  const { t } = useTranslation();
  const onDeleteCard = (even: any) => {
    even.stopPropagation();
    addOrRemoveApp({
      funcId,
      flag: 1,
      classify,
      appId: classify === 3 ? appId : null,
    } as AddOrRemoveAppParams).then(() => {
      message.success(t('desk_removeSuccess'));
      onDeleteSuccess();
    });
  };

  return (
    // eslint-disable-next-line jsx-a11y/control-has-associated-label
    <div
      role="button"
      tabIndex={0}
      className={classnames(styles.deleteBtn, { [styles.deleteBtnBlock]: classify === 4 })}
      onClick={onDeleteCard}
    >
      <div className={styles.icon} />
    </div>
  );
}

export default DesktopCardDelete;
