const blocks = [
  {
    size: [2, 2],
    name: 'systemCard',
  },
  {
    size: [2, 2],
    name: 'noticeCard',
  },
  {
    size: [2, 2],
    name: 'newsCard',
  },
  {
    size: [2, 1],
    name: 'ScrmOpportunityTotalAmount',
  },
  {
    size: [2, 1],
    name: 'ScrmCustomersNumbers',
  },
  {
    size: [6, 2],
    name: 'banner',
  },
  {
    size: [2, 2],
    name: 'calendarCp',
  },
  {
    size: [2, 2],
    name: 'processApprove',
  },
  {
    size: [2, 2],
    name: 'StaffReturnMoney',
  },
  {
    size: [2, 2],
    name: 'scrmMemberMature',
  },
  {
    size: [2, 2],
    name: 'scrmDeptMature',
  },
  {
    size: [4, 2],
    name: 'scrmReturnMoneyVisual',
  },
  {
    size: [4, 2],
    name: 'scrmStaffReturnMoneyList',
  },
  {
    size: [4, 2],
    name: 'PerformanceCard',
  },
  {
    size: [2, 2],
    name: 'orderEntry',
  },
  {
    size: [4, 2],
    name: 'orderCardBuy',
  },
  {
    size: [4, 2],
    name: 'orderCardSell',
  },
  {
    size: [2, 1],
    name: 'stockCardEnter',
  },
  {
    size: [2, 1],
    name: 'stockCardOut',
  },
  {
    size: [2, 1],
    name: 'stockCardTaking',
  },
  {
    size: [2, 1],
    name: 'stockCardAllot',
  },
  {
    size: [2, 1],
    name: 'stockCardReceive',
  },
  {
    size: [4, 2],
    name: 'stockApprovalEnter',
  },
  {
    size: [4, 2],
    name: 'stockApprovalOut',
  },
  {
    size: [4, 2],
    name: 'DisCheckCard',
  },
  {
    size: [4, 2],
    name: 'DisRankingCard',
  },
  // {
  //   size: [4, 2],
  //   name: 'DisNewChartCard',
  // },
  {
    size: [2, 2],
    name: 'procurementView',
  },
  {
    size: [4, 2],
    name: 'publishedProgress',
  },
  {
    size: [2, 2],
    name: 'financeCollectionPayment',
  },
  {
    size: [4, 2],
    name: 'financeContrastChart',
  },
  {
    size: [2, 2],
    name: 'financeReceivablePayable',
  },
  {
    size: [2, 1],
    name: 'financeUpstreamDownstream',
  },

  { size: [4, 2], name: 'uoumProcurementNoticeCard' },
  { size: [4, 2], name: 'uoumVendorRecruitmentCard' },
  { size: [4, 2], name: 'uoumIntentSourcingCard' },
];

export default blocks;
