import blocksList from './mock';
import BlockContainer from '../../containers/block-container';
import './demo.less';

function Demo() {
  return (
    <div className="m-demo">
      {blocksList.map((item) => (
        <div
          style={{
            width: 140 * item.size[0],
            height: 140 * item.size[1],
          }}
        >
          <BlockContainer name={item.name} id={item.name} />
        </div>
      ))}
    </div>
  );
}

export default Demo;
