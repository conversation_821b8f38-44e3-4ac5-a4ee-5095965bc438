/* eslint-disable no-underscore-dangle */
// eslint-disable-next-line import/no-extraneous-dependencies
import '@echronos/ech-micro/runtime';
import ReactDOM from 'react-dom';
import { BrowserRouter } from 'react-router-dom';
import { CLIENT, onReady } from '@echronos/core';
import { ConfigProvider } from 'antd';
import { Wrapper } from '@echronos/react';
import zhCN from 'antd/es/locale/zh_CN';
import App from './app';
import './index.less';
import './i18n';

// 👇 将渲染操作放入 mount 函数 -- 必填
export function mount() {
  onReady(() => {
    ReactDOM.render(
      <BrowserRouter basename={(CLIENT && window.__MICRO_APP_BASE_ROUTE__) || '/'}>
        <ConfigProvider locale={zhCN}>
          <Wrapper>
            <App />
          </Wrapper>
        </ConfigProvider>
      </BrowserRouter>,
      document.querySelector('#micro-root')!
    );
  });
}

// 👇 将卸载操作放入 unmount 函数 -- 必填
export function unmount() {
  ReactDOM.unmountComponentAtNode(document.querySelector('#micro-root')!);
}

// 微前端环境下，注册mount和unmount方法
if (CLIENT && window.__MICRO_APP_ENVIRONMENT__) {
  // @ts-ignore
  window[`micro-app-${window.__MICRO_APP_NAME__}`] = { mount, unmount };
} else {
  // 非微前端环境直接渲染
  mount();
}
