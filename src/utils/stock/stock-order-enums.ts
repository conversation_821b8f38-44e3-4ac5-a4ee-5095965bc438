import i18n from 'i18next';

// 不同属性名对应的枚举值
export const enumsMap = {
  orderStatus: [
    { code: '', name: i18n.t('stock.orderStatus.all') },
    { code: 0, name: i18n.t('stock.orderStatus.pending') },
    { code: 1, name: i18n.t('stock.orderStatus.approved') },
    { code: 2, name: i18n.t('stock.orderStatus.draft') },
    { code: 3, name: i18n.t('stock.orderStatus.oaApproving') },
  ],
  otherOrderBusinessType: [
    { code: 10, name: i18n.t('stock.otherOrderBusinessType.scrapOutbound') },
    { code: 20, name: i18n.t('stock.otherOrderBusinessType.internalWelfare') },
    { code: 30, name: i18n.t('stock.otherOrderBusinessType.compensationOut') },
    { code: 40, name: i18n.t('stock.otherOrderBusinessType.other') },
  ],
  // 发货单 单据状态
  // statusStr: [
  //   { code: 0, name: '草稿' },
  //   { code: 10, name: '待审批' },
  //   { code: 30, name: '已审批' },
  //   { code: 40, name: '发货差审批中' },
  // ],
  // 发货单 出库状态
  // outboundStatusStr: [
  //   { code: 0, name: '未出库' },
  //   { code: 1, name: '已出库' },
  //   { code: 2, name: '部分出库' },
  // ],
  // 待收货 关联订单类型
  relateOrderType: [
    { code: 1, name: i18n.t('stock.relateOrderType.purchaseOrder') },
    { code: 2, name: i18n.t('stock.relateOrderType.outsourcingOrder') },
  ],
  // 已收货 关联订单类型
  orderBusinessType: [
    { code: 1, name: i18n.t('stock.orderBusinessType.purchaseOrder') },
    { code: 2, name: i18n.t('stock.orderBusinessType.outsourcingOrder') },
  ],
  // 已收货 入库状态
  inboundStatus: [
    { code: 0, name: i18n.t('stock.inboundStatus.notInbound') },
    { code: 1, name: i18n.t('stock.inboundStatus.inbound') },
    { code: 2, name: i18n.t('stock.inboundStatus.partialInbound') },
  ],
  // 已收货 单据状态
  status: [
    { code: 0, name: i18n.t('stock.status.draft') },
    { code: 10, name: i18n.t('stock.status.pending') },
    { code: 20, name: i18n.t('stock.status.rejected') },
    { code: 30, name: i18n.t('stock.status.approved') },
    { code: 40, name: i18n.t('stock.status.acceptanceApproving') },
  ],
  // 发货单 单据状态
  statusStr: [
    { code: 0, name: i18n.t('stock.statusStr.draft') },
    { code: 10, name: i18n.t('stock.statusStr.pending') },
    { code: 30, name: i18n.t('stock.statusStr.approved') },
    { code: 40, name: i18n.t('stock.statusStr.shippingApproving') },
  ],
  // 发货单 出库状态
  outboundStatusStr: [
    { code: 0, name: i18n.t('stock.outboundStatusStr.notOutbound') },
    { code: 1, name: i18n.t('stock.outboundStatusStr.outbound') },
    { code: 2, name: i18n.t('stock.outboundStatusStr.partialOutbound') },
  ],
  // 采购入库 关联订单类型
  purchaseSubtypeName: [
    { code: 1, name: i18n.t('stock.purchaseSubtypeName.purchaseOrder') },
    { code: 2, name: i18n.t('stock.purchaseSubtypeName.outsourcingPurchaseOrder') },
  ],
  // 采购退货 关联订单类型
  returnGoodsSourceName: [
    { code: 10, name: i18n.t('stock.returnGoodsSourceName.purchaseOrder') },
    { code: 20, name: i18n.t('stock.returnGoodsSourceName.outsourcingPurchaseOrder') },
  ],
  // 其他入库 入库类型
  sourceName: [
    { code: 1, name: i18n.t('stock.sourceName.giftInbound') },
    { code: 2, name: i18n.t('stock.sourceName.compensationInbound') },
    { code: 3, name: i18n.t('stock.sourceName.otherInbound') },
  ],
  // 根据不同的路由区分业务类型
  businessType: {
    // 入库表业务类型
    enter: [
      { code: 2, name: i18n.t('stock.businessType.enter.otherInbound'), stockType: 'other-order' },
      {
        code: 4,
        name: i18n.t('stock.businessType.enter.purchaseInbound'),
        stockType: 'purchase-inbound',
      },
      {
        code: 8,
        name: i18n.t('stock.businessType.enter.produceInbound'),
        stockType: 'produce-inbound',
      },
      { code: 9, name: i18n.t('stock.businessType.enter.salesReturn'), stockType: 'sales-return' },
      { code: 11, name: i18n.t('stock.businessType.enter.allotInbound'), stockType: 'allot-order' },
      {
        code: 14,
        name: i18n.t('stock.businessType.enter.paveGoodsInbound'),
        stockType: 'pave-goods-order',
      },
      {
        code: 16,
        name: i18n.t('stock.businessType.enter.diskBalanceInbound'),
        stockType: 'disk-balance-sheet',
      },
    ],
    // 出库表业务类型
    out: [
      {
        code: 1,
        name: i18n.t('stock.businessType.out.otherOutbound'),
        stockType: 'other-outbound',
      },
      {
        code: 3,
        name: i18n.t('stock.businessType.out.salesOutbound'),
        stockType: 'sales-outbound',
      },
      // { code: 7, name: '领用出库单' },
      {
        code: 10,
        name: i18n.t('stock.businessType.out.purchaseOutbound'),
        stockType: 'purchase-outbound',
      },
      {
        code: 12,
        name: i18n.t('stock.businessType.out.allotOutbound'),
        stockType: 'allot-outbound',
      },
      {
        code: 15,
        name: i18n.t('stock.businessType.out.paveGoodsOutbound'),
        stockType: 'stock-shortage-form',
      },
      {
        code: 17,
        name: i18n.t('stock.businessType.out.diskBalanceOutbound'),
        stockType: 'stock-shortage-form',
      },
    ],
    // 默认包含所有业务类型
    default: [
      {
        code: 1,
        name: i18n.t('stock.businessType.default.otherOutbound'),
        stockType: 'other-outbound',
      },
      {
        code: 2,
        name: i18n.t('stock.businessType.default.otherInbound'),
        stockType: 'other-order',
      },
      {
        code: 3,
        name: i18n.t('stock.businessType.default.salesOutbound'),
        stockType: 'sales-outbound',
      },
      {
        code: 4,
        name: i18n.t('stock.businessType.default.purchaseInbound'),
        stockType: 'purchase-inbound',
      },
      // { code: 7, name: '领用出库单' },
      {
        code: 8,
        name: i18n.t('stock.businessType.default.produceInbound'),
        stockType: 'produce-inbound',
      },
      {
        code: 9,
        name: i18n.t('stock.businessType.default.salesReturn'),
        stockType: 'sales-return',
      },
      {
        code: 10,
        name: i18n.t('stock.businessType.default.purchaseOutbound'),
        stockType: 'purchase-outbound',
      },
      {
        code: 12,
        name: i18n.t('stock.businessType.default.allotOutbound'),
        stockType: 'allot-outbound',
      },
      {
        code: 11,
        name: i18n.t('stock.businessType.default.allotInbound'),
        stockType: 'allot-order',
      },
    ],
    // 出库通知 订单来源类型
    orderFuncType: [
      { code: 0, name: i18n.t('stock.orderFuncType.onlineMall') },
      { code: 4, name: i18n.t('stock.orderFuncType.mallSupplement') },
      { code: 7, name: i18n.t('stock.orderFuncType.returnSupplement') },
    ],
  },
};

// 定义类型
export type EnumItem = {
  code: string | number;
  name: string;
  stockType?: string;
};

export type BusinessTypeKey = keyof typeof enumsMap.businessType;
export type EnumMapKey = keyof typeof enumsMap;

// 工具函数：将数字转换为对应的文本描述
const getEnumData = (
  type: EnumMapKey | 'businessType',
  subTypeOrCode?: BusinessTypeKey | number | string,
  code?: number | string,
  outputType: 'name' | 'type' = 'name'
): string => {
  // 处理嵌套路径的情况
  if (type === 'businessType' && subTypeOrCode && typeof subTypeOrCode === 'string') {
    const enumList = enumsMap.businessType[subTypeOrCode as BusinessTypeKey];
    if (!enumList) return '-';

    const item = Array.isArray(enumList) ? enumList.find((x) => x.code === code) : undefined;
    if (!item) return '-';

    return outputType === 'name' ? item.name : (item as EnumItem).stockType || '-';
  }

  // 处理直接枚举类型的情况
  const enumList = enumsMap[type as EnumMapKey];
  if (!enumList) return '-';

  const item = Array.isArray(enumList) ? enumList.find((x) => x.code === subTypeOrCode) : undefined;
  if (!item) return '-';

  return outputType === 'name' ? item.name : (item as EnumItem).stockType || '-';
};

export default getEnumData;
