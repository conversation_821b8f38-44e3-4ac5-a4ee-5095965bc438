@import '@echronos/react/less/index';

.box {
  padding: 16px 20px;
  border-radius: 18px;
  background-color: @white;
}

.text {
  color: @text-color-secondary;
  font-size: @font-size-xs;
  line-height: 20px;
}

.upload {
  font-size: @font-size-xs;
  display: flex;
  width: 86px;
  height: 86px;
  padding: 0;
  overflow: hidden !important;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: @border-radius-base;
  border: 0 solid transparent;
  background-color: @background-color-base;
  cursor: pointer;
  object-fit: contain;

  &Icon {
    font-size: @font-size-md;
  }
}

.icon {
  display: block;
  width: 86px;
  height: 86px;
  object-fit: contain;
}
