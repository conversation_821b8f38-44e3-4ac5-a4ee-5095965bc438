/* eslint-disable jsx-a11y/control-has-associated-label */
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>vent<PERSON><PERSON><PERSON>,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Form, Input, Row } from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import ColorPicker from './color-picker';
import type { BaseCustomIconProps } from './types';
import styles from './solid-color-icon.module.less';
import textSvg from '../../../assets/images/text.svg';

function SolidColorIcon({ form, onChange }: BaseCustomIconProps) {
  const defaultColor = '#AF52DE';
  const color = Form.useWatch<string>('colorValue', form) || defaultColor;
  const name = Form.useWatch<string>('text', form) || '';
  const canvasEl = useRef(null as unknown as HTMLCanvasElement);
  const [colors] = useState([
    '#FF453A',
    '#FF9500',
    '#E65175',
    '#F048BA',
    '#F88F7A',
    '#FFCC00',
    '#28CD41',
    '#59ADC4',
    '#007AFF',
    '#55BEF0',
    '#5856D6',
    '#AF52DE',
    '#6D835F',
    '#7DAB88',
    '#A9B4B6',
    '#8E8E93',
    '#333333',
  ]);
  const activeIndex = colors.indexOf(color);
  const setColor = useCallback(
    (colorValue: string) => {
      form.setFieldsValue({ colorValue });
    },
    [form]
  );
  const onSetColor: MouseEventHandler<HTMLButtonElement> = useCallback(
    (e) => {
      const btnColor = (e.target as HTMLButtonElement).getAttribute('data-color');
      if (btnColor) {
        setColor(btnColor);
      }
    },
    [setColor]
  );
  const onSetName: ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      form.setFieldsValue({ text: (e.target as HTMLInputElement)?.value || '' });
    },
    [form]
  );

  useEffect(() => {
    const canvas = canvasEl.current;
    if (!canvas) {
      return;
    }

    canvas.width = 100 * window.devicePixelRatio;
    canvas.height = 100 * window.devicePixelRatio;
  }, []);
  useEffect(() => {
    const canvas = canvasEl.current;
    const ctx = canvas?.getContext('2d');
    if (!ctx || name.length > 2) {
      return;
    }

    const { width, height } = canvas;
    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, width, height);

    if (name) {
      ctx.fillStyle = '#FFF';
      ctx.font = `${
        (name.length === 1 ? 40 : 24) * (width / 60)
      }px "BlinkMacSystemFont","Microsoft YaHei","PingFang SC","Helvetica Neue",serif`;
      ctx.textAlign = 'start';
      ctx.textBaseline = 'middle';
      let offset = 0;
      const text = ctx.measureText(name);
      const textBox = text.actualBoundingBoxDescent - text.actualBoundingBoxAscent;
      if (Math.abs(textBox) > 3) {
        offset = textBox / 2;
      }

      ctx.fillText(name, (width - text.width) / 2, height / 2 - offset);
    }

    canvas.toBlob((blob) => {
      onChange(blob);
    });
  }, [color, name]); // eslint-disable-line
  // 多语言导入
  const { t } = useTranslation();
  return (
    <div className="pt-5">
      <div className={classNames(styles.solidColor, styles.icon)}>
        <canvas ref={canvasEl} className={styles.canvas} />
      </div>

      <Row>
        {colors.map((cColor, index) => (
          <div key={cColor} className={styles.colorBox}>
            <button
              type="button"
              data-color={cColor}
              className={classNames(styles.colorBtn, { [styles.active]: activeIndex === index })}
              style={{ background: cColor }}
              onClick={onSetColor}
            />
          </div>
        ))}
        {/* @ts-ignore */}
        <ColorPicker onChange={setColor}>
          <div className={styles.colorBox}>
            <button
              type="button"
              className={classNames(styles.colorBtn, styles.moreColor, {
                [styles.active]: activeIndex === -1,
              })}
              onClick={onSetColor}
            />
          </div>
        </ColorPicker>
      </Row>

      <Form.Item name="colorValue" initialValue={defaultColor} hidden noStyle>
        <Input />
      </Form.Item>
      <Form.Item name="text" noStyle>
        <Input
          allowClear
          prefix={<img src={textSvg} alt="文本" />}
          size="large"
          placeholder={t('desk_custIcon_text')}
          maxLength={2}
          className={styles.input}
          onChange={onSetName}
        />
      </Form.Item>
    </div>
  );
}

export default SolidColorIcon;
