@import '../../../../node_modules/@echronos/react/less/index';

.tabs {
  display: flex;

  > li {
    .flex-column();
    .text-overflow();

    color: @text-color-secondary;
    padding: 8px 8px 14px;
    text-align: center;
    overflow: hidden;
    cursor: pointer;
    position: relative;
  }

  > .activeItem {
    color: @primary-color;

    &::after {
      content: '';
      display: block;
      width: 20px;
      height: 4px;
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
      background: @primary-color;
    }
  }
}
