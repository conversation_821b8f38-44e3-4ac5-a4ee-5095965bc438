import { useCallback, useState } from 'react';
import { Popover, PopoverProps } from 'antd';
import { SketchPicker, SketchPickerProps } from 'react-color';

export interface ColorPickerProps extends Omit<PopoverProps, 'content'> {
  onChange: MultipleParamsFn<string>;
}

function ColorPicker({ onChange, ...props }: ColorPickerProps) {
  const [presetColors] = useState<SketchPickerProps['presetColors']>([
    { color: '#000000', title: '#000000' },
    { color: '#FFFFFF', title: '#FFFFFF' },
    { color: '#016E8F', title: '#016E8F' },
    { color: '#0042A9', title: '#0042A9' },
    { color: '#7A219E', title: '#7A219E' },
    { color: '#E22400', title: '#E22400' },
    { color: '#D38301', title: '#D38301' },
    { color: '#F5EC00', title: '#F5EC00' },
    { color: '#669D34', title: '#669D34' },
    { color: '#01C7FC', title: '#01C7FC' },
    { color: '#3A87FD', title: '#3A87FD' },
  ]);
  const [color, setColor] = useState<SketchPickerProps['color']>();
  const [pickStyles] = useState<SketchPickerProps['styles']>({
    // @ts-ignore
    picker: { width: 288 },
  });
  const onChangeColor: SketchPickerProps['onChange'] = useCallback((colorResult) => {
    setColor(colorResult.hex);
  }, []);
  const onChangeColorComplete: SketchPickerProps['onChangeComplete'] = useCallback(
    (result) => {
      // @ts-ignore
      onChange((result.hex || '').toUpperCase());
    },
    [onChange]
  );

  return (
    <Popover
      {...props}
      placement="top"
      arrowPointAtCenter
      trigger="click"
      content={
        <SketchPicker
          disableAlpha
          color={color}
          presetColors={presetColors}
          styles={pickStyles}
          onChange={onChangeColor}
          onChangeComplete={onChangeColorComplete}
        />
      }
    />
  );
}

export default ColorPicker;
