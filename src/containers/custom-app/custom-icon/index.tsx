import { useMemo, useRef } from 'react';
import { Form, Tabs, TabsProps } from 'antd';
import { useMemoizedFn } from 'ahooks';
import { BaseCustomIconProps } from '@/containers/custom-app/custom-icon/types';
import { useTranslation } from 'react-i18next';
import OnlineIcon, { OnlineIconProps } from './online-icon';
import SolidColorIcon from './solid-color-icon';
import ImageIcon from './image-icon';
import styles from './index.module.less';

export type CustomIconProps = OnlineIconProps;

const renderTabBar: TabsProps['renderTabBar'] = (props: Record<string, any>) => {
  const { activeKey, panes, onTabClick } = props;
  return (
    <div>
      <ul className={styles.tabs}>
        {panes.map((pane: { key: string; props: { tab: string } }) => (
          <li key={pane.key} className={activeKey === pane.key ? styles.activeItem : ''}>
            <div
              tabIndex={0}
              role="button"
              onClick={(e) => {
                onTabClick(pane.key, e);
              }}
            >
              <span>{pane.props.tab}</span>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

function CustomIcon({ form, defaultIcons, onChange }: CustomIconProps) {
  const activeKey = Form.useWatch<string>('type', form);
  const imageRef = useRef<Map<string | number, string | Blob | null>>(new Map());
  // 多语言导入
  const { t } = useTranslation();
  const onChangeImage = useMemoizedFn((index: number | string, image: string | Blob | null) => {
    imageRef.current.set(index, image);
    if (index === activeKey) {
      onChange(image);
    }
  });
  const onChangeTab = useMemoizedFn((key: string | number) => {
    const type = (key || '').toString();
    // @ts-ignore
    form.setFieldsValue({ type });
    onChange(imageRef.current.get(type) || null);
  });
  const onChangeImages: BaseCustomIconProps['onChange'][] = useMemo(
    () => [
      (image) => {
        onChangeImage('1', image);
      },
      (image) => {
        onChangeImage('2', image);
      },
      (image) => {
        onChangeImage('3', image);
      },
    ],
    [onChangeImage]
  );

  return (
    <Tabs
      activeKey={activeKey}
      renderTabBar={renderTabBar}
      className={styles.tabs}
      onChange={onChangeTab}
    >
      <Tabs.TabPane tab={t('desk_custIcon_onlineIcon')} key="1">
        <OnlineIcon form={form} defaultIcons={defaultIcons} onChange={onChangeImages[0]} />
      </Tabs.TabPane>
      <Tabs.TabPane tab={t('desk_custIcon_SolidColorIcon')} key="2">
        <SolidColorIcon form={form} onChange={onChangeImages[1]} />
      </Tabs.TabPane>
      <Tabs.TabPane tab={t('desk_custIcon_ImageIcon')} key="3">
        <ImageIcon form={form} onChange={onChangeImages[2]} />
      </Tabs.TabPane>
    </Tabs>
  );
}

export default CustomIcon;
