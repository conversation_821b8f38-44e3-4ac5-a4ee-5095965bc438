@import './common.less';

.online {
  display: flex;
  align-items: center;
  justify-content: center;
}

.notice {
  color: @text-color-secondary;
  font-size: @font-size-xs;
  width: 220px;
  margin: 16px auto 0;
  text-align: center;
}

.img {
  width: 50px;
  height: 50px;
}

.loading:extend(.img) {
  animation: loading-rotate 0.3s linear infinite;
}

@keyframes loading-rotate {
  0% {
    transform: rotateZ(0deg);
  }

  100% {
    transform: rotateZ(360deg);
  }
}
