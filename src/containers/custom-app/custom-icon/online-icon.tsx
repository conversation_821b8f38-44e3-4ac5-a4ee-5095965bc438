/* eslint-disable jsx-a11y/control-has-associated-label */
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>allback, useEffect, useState } from 'react';
import { Col, Form, Row } from 'antd';
import classNames from 'classnames';
import { useDebounceFn } from 'ahooks';
import { getUrlIcons } from '@/apis';
import { useTranslation } from 'react-i18next';
import type { BaseCustomIconProps } from './types';
import styles from './online-icon.module.less';
import searchPng from '../../../assets/images/search.png';
import loadingPng from '../../../assets/images/loading.png';
import bellPng from '../../../assets/images/bell.png';

export interface OnlineIconProps extends BaseCustomIconProps {
  defaultIcons: string[];
}

function OnlineIcon({ form, defaultIcons, onChange }: OnlineIconProps) {
  const url = Form.useWatch<string>('url', form);
  const [loading, setLoading] = useState(false);
  const [icon, setIcon] = useState<string | null>();
  // 多语言导入
  const { t } = useTranslation();
  const [icons, setIcons] = useState<string[] | null>(null);
  const { run } = useDebounceFn(
    () => {
      if (url) {
        getUrlIcons(url)
          .then((list) => {
            const promises: Promise<string>[] = [];
            const verified: string[] = [];
            list.forEach((item) => {
              if (!verified.includes(item)) {
                verified.push(item);
                promises.push(
                  new Promise<string>((resolve) => {
                    fetch(item)
                      .then(() => {
                        resolve(item);
                      })
                      .catch(() => {
                        resolve('');
                      });
                  })
                );
              }
            });

            Promise.all(promises).then((res) => {
              setIcons((defaultIcons || []).concat(res.filter(Boolean)));
            });
          })
          .catch((e) => {
            if (!e || e.message !== 'cancel') {
              setIcons([]);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    },
    { wait: 500 }
  );
  const onSelectIcon: MouseEventHandler<HTMLDivElement> = useCallback(
    (e) => {
      let el = e.target as HTMLImageElement | null;
      while (el && el.tagName !== 'IMG') {
        el = el.children?.[0] as HTMLImageElement;
      }

      if (el && el.src) {
        setIcon(el.src);
        onChange(el.src);
      }
    },
    [onChange]
  );

  useEffect(() => {
    if (url) {
      setLoading(true);
      run();
    } else {
      setLoading(false);
    }
  }, [run, url]);
  useEffect(() => {
    setIcon(defaultIcons[0]);
    onChange(defaultIcons[0]);
  }, [defaultIcons, onChange]);

  let child;
  if (!url) {
    child = (
      <div className={styles.notice}>
        <img src={searchPng} alt="" className={styles.img} />
        <p>{t('desk_custIcon_searchPngWrn')}</p>
      </div>
    );
  } else if (loading) {
    child = (
      <div className={styles.notice}>
        <img src={loadingPng} alt="" className={styles.loading} />
        <p>{t('desk_custIcon_loadingPng')}</p>
      </div>
    );
  } else if (icons) {
    child = !icons.length ? (
      <div className={styles.notice}>
        <img src={bellPng} alt="" className={styles.img} />
        <p className="mt-2">
          {t('desk_custIcon_bellPng')}
          <br />
          {t('desk_custIcon_otherPng')}
        </p>
      </div>
    ) : (
      <Row gutter={28}>
        {icons.map((tmpIcon) => (
          <Col key={tmpIcon} span={6} className="py-2 text-center">
            <div
              role="button"
              tabIndex={0}
              className={classNames(styles.icon, styles.online, {
                [styles.active]: tmpIcon === icon,
              })}
              onClick={onSelectIcon}
            >
              <img src={tmpIcon} alt="" />
            </div>
          </Col>
        ))}
      </Row>
    );
  }

  return <div className="py-3">{child}</div>;
}

export default OnlineIcon;
