@import './common.less';

.solidColor {
  margin-bottom: 23px;
  box-shadow: 0 4px 16px 0 rgb(81 84 106 / 30%);
}

.canvas {
  width: 60px;
  height: 60px;
}

.colorBox {
  width: 11.1111%;
}

.colorBtn {
  display: flex;
  width: 22px;
  height: 22px;
  line-height: 22px;
  margin: 9px auto !important;
  padding: 0;
  justify-content: center;
  border-radius: 50%;
  border: 0 solid transparent;
  background: transparent;
  align-items: center;
  cursor: pointer;

  &.active {
    &::before {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid @white;
    }
  }
}

.moreColor {
  background: conic-gradient(
    from 180deg at 50% 50%,
    #cd00ff -5deg,
    #7858ff 31deg,
    #00a7ff 58deg,
    #32e746 98deg,
    #f5f22c 152deg,
    #f58a1c 207deg,
    #ff5145 274deg,
    #cd00ff 355deg,
    #7858ff 391deg
  );
}

.input {
  font-size: @font-size-base !important;
  margin-top: 19px;
  border-color: transparent;
  background-color: rgb(@white 0.4);
  backdrop-filter: blur(0);

  input {
    font-size: @font-size-base !important;
    background-color: transparent;
  }
}
