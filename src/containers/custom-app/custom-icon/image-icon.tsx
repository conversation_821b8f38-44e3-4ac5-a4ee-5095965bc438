import { ChangeEventHand<PERSON>, useCallback, useEffect, useRef, useState } from 'react';
import { Form, message, Spin } from 'antd';
import { PlusOutlined } from '@echronos/icons';
import classNames from 'classnames';
import { fileToUrl, isString, revokeUrl } from '@echronos/core';
import imageCrop from '@/components/image-crop';
import { useTranslation } from 'react-i18next';
import { BaseCustomIconProps } from './types';
import styles from './image-icon.module.less';

function ImageIcon({ form, onChange }: BaseCustomIconProps) {
  const inputEl = useRef<HTMLInputElement>(null as unknown as HTMLInputElement);
  const [uploading, setUploading] = useState(false);
  const [icon, setIcon] = useState<string | null>(null);
  // 多语言导入
  const { t } = useTranslation();
  const onSelectFile = useCallback(() => {
    inputEl.current?.click();
  }, []);
  const onChangeImage: ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      const input = e.target as unknown as HTMLInputElement;
      if (input) {
        const file = input.files?.[0];
        if (file && file.type.indexOf('image/') === 0) {
          if (file.size > 5 * 1024 * 1024) {
            message.error(`${t('desk_imgCrop_uploadWrn')}`);
          } else {
            imageCrop(file)
              .then((image) => {
                setIcon((prevState) => {
                  try {
                    if (prevState) {
                      revokeUrl(prevState);
                    }
                  } catch (err: unknown) {
                    console.warn(err);
                  }
                  return isString(image) ? image : fileToUrl(image);
                });
                onChange(image);
              })
              .finally(() => {
                setUploading(false);
              });
          }
        } else {
          message.error(`${t('desk_imgCrop_pleImg')}`);
        }
        input.value = '';
      }
    },
    [onChange] //eslint-disable-line
  );

  useEffect(() => {
    if (Number(form.getFieldValue('type')) === 3) {
      const image = form.getFieldValue('photo');
      if (image) {
        setIcon(image);
        onChange(image);
      }
    }
  }, []); // eslint-disable-line

  return (
    <div className={classNames(styles.box, 'mt-5')}>
      <Form.Item label={t('desk_custIcon_cusIcon')} className="mb-0">
        <p className={classNames(styles.text, 'mb-3')}>{t('desk_custIcon_imgUpload')}</p>
        <button type="button" className={styles.upload} onClick={onSelectFile}>
          {icon ? (
            <Spin spinning={uploading}>
              <img src={icon} alt="" className={styles.icon} />
            </Spin>
          ) : (
            <>
              <PlusOutlined className={styles.uploadIcon} />
              <p className="mt-1">{t('desk_custIcon_addImg')}</p>
            </>
          )}
        </button>
        <input ref={inputEl} type="file" hidden accept="image/*" onChange={onChangeImage} />
      </Form.Item>
    </div>
  );
}

export default ImageIcon;
