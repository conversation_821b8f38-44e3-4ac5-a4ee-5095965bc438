@import '@echronos/react/less/index';

.drawer {
  :global {
    .ant-drawer-content {
      border-radius: 18px;
      backdrop-filter: blur(100px);
      // stylelint-disable-next-line
      -webkit-backdrop-filter: blur(100px);
      background: rgb(@background-color-base 0.7);
    }

    .ant-drawer-header {
      padding: 15px 20px;
      border-bottom: none;
      background: transparent;

      &-title {
        display: block;
        position: relative;
      }
    }

    .ant-drawer-close {
      font-size: 24px;
      line-height: 1;
      padding: 0;
      border: 0 solid transparent;
      position: absolute;
      top: 0;
      left: 0;

      .anticon {
        vertical-align: top;
      }
    }

    .ant-drawer-title {
      font-size: @font-size-lg;
      line-height: 24px;
      text-align: center;
    }

    .ant-drawer-body {
      display: flex;
      flex-flow: column nowrap;
      width: 100%;
      height: 100%;
      padding: 12px 0 !important;
    }
  }
}
