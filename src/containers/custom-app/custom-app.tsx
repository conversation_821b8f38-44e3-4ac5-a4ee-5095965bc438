import React, {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>vent<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useRef,
  useState,
  useEffect,
} from 'react';
import { Button, Form, FormInstance, Input, message, Radio, Spin } from 'antd';
import classNames from 'classnames';
import { generateUploadFile, urlToFile } from '@echronos/core';
import { useMemoizedFn } from 'ahooks';
import { useTranslation } from 'react-i18next';
import {
  createDesktopExternalApp,
  CreateDesktopExternalAppData,
  CustomAppDetail,
  findCustomAppDetail,
} from '@/apis';
import CustomIcon from './custom-icon';
import styles from './custom-app.module.less';

export interface CustomAppProps {
  id: number;
  // eslint-disable-next-line no-unused-vars
  onSuccess: (e: React.MouseEvent, f: CreateDesktopExternalAppData) => void;
  onChange: () => void;
}

function NameItem({ form }: { form: FormInstance }) {
  const value = Form.useWatch('name', form) || '';
  // 多语言导入
  const { t } = useTranslation();
  return (
    <Form.Item
      name="name"
      label={
        <>
          {t('desk_custApp_name')}
          <span className={styles.lenCount}>（{value.length}/6）</span>
        </>
      }
    >
      <Input allowClear placeholder={t('desk_custApp_pleName')} maxLength={6} />
    </Form.Item>
  );
}

function CustomApp({ id, onSuccess, onChange }: CustomAppProps) {
  const [form] = Form.useForm<CreateDesktopExternalAppData>();
  const initial = useRef(false);
  const imageRef = useRef<string | null | Blob>();
  const detailRef = useRef<CustomAppDetail | null>();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [disabled, setDisabled] = useState(!!id);
  const [defaultIcons, setDefaultIcons] = useState<string[]>([]);
  // 多语言导入
  const { t } = useTranslation();
  const onBlurHref: FocusEventHandler<HTMLTextAreaElement> = useCallback(
    (e) => {
      const { value } = e.target;
      if (value && !/^https?:\/\//.test(value)) {
        form.setFieldsValue({
          url: `https://${value.replace(/^:?\/+/, '')}`,
        });
      }
    },
    [form]
  );
  const onChangeStatus = useMemoizedFn(() => {
    if (initial.current) {
      onChange?.();
      setDisabled(false);
    }
  });
  const onChangeFile = useCallback(
    (image: Blob | string | null) => {
      imageRef.current = image;
      onChangeStatus();
    },
    [onChangeStatus]
  );
  const submit = (fields: CreateDesktopExternalAppData) => {
    const image = imageRef.current;
    if (!image) {
      const translationKey =
        Number(fields.type) === 1 ? 'desk_custApp_chooseIcon' : 'desk_custApp_uploadIcon';
      message.error(t(translationKey));
      return Promise.reject();
    }

    setSubmitting(true);
    const values = fields;
    const photo = form.getFieldValue('photo');
    const detail = detailRef.current;
    let promise: Promise<Blob | void>;
    if (photo && (!detail || detail.photo === image)) {
      promise = Promise.resolve();
    } else {
      if (typeof image === 'string') {
        const matchFileName = image.match(/\/([^?/]+)(\?.*)?$/);
        promise = urlToFile(image, (matchFileName && matchFileName[1]) || 'image.png');
      } else {
        promise = Promise.resolve(image);
      }
      promise = promise
        .then((file) => {
          if (file) {
            return generateUploadFile(file instanceof File ? file : new File([file], 'image.png'));
          }
          return Promise.reject();
        })
        .then((file) => file.upload())
        .then((uploadFile) => {
          values.photo = uploadFile.url;
        });
    }

    return promise
      .then(() => {
        if (detail && detail.id) {
          values.id = detail.id;
        }
        return createDesktopExternalApp(values);
      })
      .then(() => {
        message.success(
          `${values.id ? t('desk_custApp_updateSuccess') : t('desk_custApp_saveSuccess')}`
        );
      })
      .finally(() => {
        setSubmitting(false);
      });
  };
  const onSubmit: MouseEventHandler<HTMLElement> = useMemoizedFn((e) => {
    const fields = form.getFieldsValue();
    if (!fields.url) {
      message.error(`${t('desk_custApp_pleUrl')}`);
    } else if (!fields.name) {
      message.error(`${t('desk_custApp_pleName')}`);
    } else {
      if (!fields.text) {
        fields.text = '';
      }
      submit(fields).then(() => {
        onSuccess(e, fields);
      });
    }
  });

  useEffect(() => {
    if (!id) {
      form.resetFields();
      initial.current = true;
    } else {
      setLoading(true);
      findCustomAppDetail(id)
        .then((res) => {
          detailRef.current = res;
          setDefaultIcons([res.type === 1 ? res.photo : ''].filter(Boolean));

          // @ts-ignore
          const fields = {
            id: res.id,
            photo: res.photo,
            name: res.name,
            url: res.url,
            isOpen: res.isOpen,
            // colorValue: res.colorValue,
            text: res.text,
            type: (res.type || 1).toString(),
          } as CreateDesktopExternalAppData;
          if (res.colorValue) {
            fields.colorValue = res.colorValue;
          }
          form.setFieldsValue(fields);
        })
        .finally(() => {
          setLoading(false);
          setTimeout(() => {
            initial.current = true;
          }, 50);
        });
    }

    return () => {
      initial.current = false;
    };
  }, [id]); // eslint-disable-line

  return (
    <div className={styles.wrapper}>
      <div className={styles.body}>
        <Spin spinning={loading}>
          <Form form={form} layout="vertical" onFieldsChange={onChangeStatus}>
            <div className={classNames(styles.box, 'mb-5')}>
              <Form.Item name="url" label={t('desk_custApp_url')} initialValue="https://">
                <Input.TextArea placeholder={t('desk_custApp_addUrl')} onBlur={onBlurHref} />
              </Form.Item>
              <NameItem form={form} />
              <Form.Item
                name="isOpen"
                label={t('desk_custApp_open')}
                initialValue={0}
                className="mb-0"
              >
                <Radio.Group>
                  <Radio value={0}>{t('desk_custApp_currentTab')}</Radio>
                  <Radio value={1}>{t('desk_custApp_newTab')}</Radio>
                </Radio.Group>
              </Form.Item>
            </div>
            <Form.Item name="photo" hidden noStyle>
              <Input />
            </Form.Item>

            <Form.Item name="type" hidden noStyle initialValue="1">
              <Input />
            </Form.Item>
            <CustomIcon form={form} defaultIcons={defaultIcons} onChange={onChangeFile} />
          </Form>
        </Spin>
      </div>
      <div className={styles.footer}>
        <Button block type="primary" loading={submitting} disabled={disabled} onClick={onSubmit}>
          {t('public_sure')}
        </Button>
      </div>
    </div>
  );
}

export default CustomApp;
