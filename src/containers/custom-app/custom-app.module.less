@import '@echronos/react/less/index';

.wrapper {
  .flex-row(100%);

  display: flex;
  flex-direction: column;
  height: 100%;
}

.body {
  .flex-row();

  padding: 0 18px;
  overflow-y: auto;
}

.footer {
  margin-bottom: -12px;
  padding: 24px 20px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 36%) 12%,
    rgb(255 255 255 / 58%) 25%,
    rgb(255 255 255 / 72%) 36%
  );
}

.disabled {
  &,
  &:hover,
  &:active {
    color: @white !important;
    border-color: #b1b3be !important;
    background: @disabled-color !important;
    text-shadow: none;
    box-shadow: none;
  }
}

.box {
  padding: 16px;
  border-radius: 18px;
  background: @white;

  :global {
    .ant-form-item {
      margin-bottom: 16px;
    }

    textarea {
      &.ant-input {
        height: 106px;
        padding: 10px;
        border-color: #f3f3f3;
        resize: none;
      }
    }

    .ant-input-affix-wrapper {
      font-size: @font-size-base;
      line-height: 1;
      padding: 8px 0 16px;
      border-top: 0;
      border-right: 0;
      border-left: 0;
      border-radius: 0;
      border-color: #f3f3f3;
    }

    .ant-radio {
      &-wrapper {
        display: inline-flex;
        align-items: center;
      }
    }
  }
}

.lenCount {
  color: @text-color-secondary;
  font-size: @font-size-xs;
  line-height: 20px;
}
