import React, { useCallback, useRef } from 'react';
import { popupComponent } from '@echronos/react';
import { Drawer, DrawerProps, Modal } from 'antd';
import { LeftOutlined } from '@echronos/icons';
import classNames from 'classnames';
import { CreateDesktopExternalAppData } from '@/apis';
import { useTranslation } from 'react-i18next';
import CustomApp from './custom-app';
import styles from './index.module.less';

export type CustomAppDrawerProps = DrawerProps & {
  id?: number;
  // eslint-disable-next-line no-unused-vars
  onSuccess: (f: CreateDesktopExternalAppData) => void;
};

export function CustomAppDrawer({
  id,
  title,
  className,
  onClose,
  onSuccess,
  ...props
}: CustomAppDrawerProps) {
  const hasChange = useRef(0);
  // 多语言导入
  const { t } = useTranslation();
  const onCloseDrawer = useCallback(
    (e: any) => {
      if (hasChange.current) {
        if (hasChange.current !== 1) {
          return;
        }
        hasChange.current += 1;
        Modal.confirm({
          icon: null,
          title: `${t('public_title')}`,
          width: 312,
          centered: true,
          content: `${t('desk_modal_content')}`,
          cancelText: `${t('desk_modal_cancelText')}`,
          okText: `${t('desk_modal_okText')}`,
          getContainer: '#custom-app .ant-drawer-content',
          onCancel: () => {
            onClose?.(e);
          },
          afterClose: () => {
            hasChange.current = 1;
          },
        });
      } else {
        onClose?.(e);
      }
    },
    [onClose] //eslint-disable-line
  );
  // eslint-disable-next-line no-unused-vars
  const onCreateSuccess: (e: React.MouseEvent, f: CreateDesktopExternalAppData) => void =
    useCallback(
      (e, f) => {
        onSuccess(f);
        hasChange.current = 0;
        onCloseDrawer(e);
      },
      [onCloseDrawer, onSuccess]
    );
  const onChange = useCallback(() => {
    if (!hasChange.current) {
      hasChange.current = 1;
    }
  }, []);

  return (
    <Drawer
      {...props}
      id="custom-app"
      title={title || t(id ? 'desk_custApp_cusEdit' : 'desk_allApp_cusAdd')}
      // title={title || `自定义${id ? '编辑' : '添加'}`}
      closeIcon={<LeftOutlined />}
      className={classNames(styles.drawer, className)}
      onClose={onCloseDrawer}
    >
      <CustomApp id={id || 0} onSuccess={onCreateSuccess} onChange={onChange} />
    </Drawer>
  );
}

CustomAppDrawer.defaultProps = {
  id: 0,
};

function customApp(id?: number) {
  return new Promise((resolve, reject) => {
    let isFail = true;
    // @ts-ignore
    popupComponent(CustomAppDrawer, {
      id,
      maskStyle: { background: 'transparent' },
      contentWrapperStyle: { zIndex: '9999' },
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      getContainer: () => document.querySelector<HTMLDivElement>('#micro-root')!,
      onSuccess: (f) => {
        isFail = true;
        resolve(f);
      },
      onClose: () => {
        if (isFail) {
          reject();
        }
      },
    });
  });
}

export default customApp;
