import { useNavigation } from '@/hooks';
import * as blocks from '../../blocks';
import './index.less';

export interface IBlockContainerProps {
  name?: string; // block 名称
  src?: string; // 代码的地址
  id: string; // 容器id
  verticalMargin?: number; // 水平边距
  horizontalMargin?: number; // 垂直边距
}

function BlockContainer({ name, src, id, verticalMargin, horizontalMargin }: IBlockContainerProps) {
  const navigate = useNavigation();
  let Ele = null as any;

  if (!name && !src) {
    console.error('block Card container missing name or src parameter');
  }

  // @ts-ignore
  Ele = blocks[name];

  return (
    <div
      id={id}
      className="ech-block-container"
      style={{
        width: `calc(100% - ${(verticalMargin || 0) * 2}px)`,
        height: `calc(100% - ${(horizontalMargin || 0) * 2}px)`,
      }}
    >
      {Ele ? <Ele navigate={navigate} blockId={id} /> : <div className="load-error">404</div>}
    </div>
  );
}

BlockContainer.defaultProps = {
  name: '', // block 名称
  src: '', // 代码的地址
  verticalMargin: 12, // 水平边距
  horizontalMargin: 12, // 垂直边距
};

export default BlockContainer;
