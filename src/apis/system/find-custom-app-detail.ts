import { omit } from 'lodash';
import request from './request';
import type { CreateDesktopExternalAppData } from './create-desktop-external-app';

export interface CustomAppDetail extends Required<CreateDesktopExternalAppData> {
  // 主键
  id: number;
  // 应用描述
  funcDesc: string;
  // 排序
  sort: number;
  // 编码
  coding: string;
  // 显示状态：0-web端  1-APP 2-都显示
  display: 0 | 1 | 2;
  // 使用状态：0-停用 1-启用
  useStatus: 0 | 1;
}

/**
 * 查找自定义外部应用详情
 * @param funcId
 */
function findCustomAppDetail(funcId: number): Promise<CustomAppDetail> {
  return request()('/v2/desktop/query/by', {
    params: { funcId },
    method: 'GET',
  }).then((res: CustomAppDetail) => omit(res, ['pagination']));
}

export default findCustomAppDetail;
