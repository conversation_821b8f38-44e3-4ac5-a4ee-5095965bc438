export { default as getDesktopAllApp } from './get-desktop-all-app';
export type { IApp } from './get-desktop-all-app';

export { default as getDesktopAllCard } from './get-desktop-all-card';

export type { AddOrRemoveAppParams } from './add-or-remove-app';
export { default as addOrRemoveApp } from './add-or-remove-app';
export { default as getDesktopMyApp } from './get-desktop-my-app';
export { default as sendPermissionMessage } from './send-permission-message';
export { default as getHomeGuide } from './get-home-guide';

export type { CreateDesktopExternalAppData } from './create-desktop-external-app';
export { default as createDesktopExternalApp } from './create-desktop-external-app';

export { default as getUrlIcons } from './get-url-icons';
export { default as appletSort } from './applet-sort';
export { default as addAppGroup } from './add-app-group';
export type { CustomAppDetail } from './find-custom-app-detail';
export { default as findCustomAppDetail } from './find-custom-app-detail';

export { default as getPermStatusList } from './get-perm-status-list';
export { default as updateGroupInfo } from './update-group-info';
