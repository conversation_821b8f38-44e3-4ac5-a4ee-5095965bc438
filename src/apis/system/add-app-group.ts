import request from './request';

export interface AddAppGroupParams {
  relationIds: number[]; // 映射ID
  groupId?: number; // 分组ID
  identity?: string; // 标识ID
  groupName: string; // 分组名称
  operateType: number; // 1,移入2,移出
}

export interface AddAppGroupRes {
  groupId: number;
  identity: string;
}

/**
 * app分组
 * @param props
 */
function addAppGroup(data: AddAppGroupParams): Promise<AddAppGroupRes> {
  return request()('/v2/desktop/group', {
    data,
    method: 'POST',
  });
}

export default addAppGroup;
