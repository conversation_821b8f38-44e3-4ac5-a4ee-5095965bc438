import { PaginationResponse } from '@echronos/core';
import request from './request';

interface PermStatusListParams {
  permCodes: string[];
}

export interface PermStatusListData {
  isCharge: boolean;
  isPermission: boolean;
  appId: number;
  permCode: string;
}

function getPermStatusList(data: PermStatusListParams): PaginationResponse<PermStatusListData> {
  return request()('/v1/system/permission/member/query/permission/status', {
    data,
    method: 'POST',
    autoToast: false,
  });
}

export default getPermStatusList;
