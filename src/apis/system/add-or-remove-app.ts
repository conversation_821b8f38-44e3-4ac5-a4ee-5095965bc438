import { isNil, isNumber } from '@echronos/core';
import request from './request';

export interface AddOrRemoveAppParams {
  //	功能Id
  funcId: number;
  //	新增:0 or 移除:1
  flag?: 0 | 1;
  // 应用分类 1：平台应用 2：自建应用 3:小程序应用  4小组件
  classify?: 1 | 2 | 3 | 4;
  appId?: string | null; // classify=3时传 小程序appId
}

export interface AddOrRemoveAppRes {
  flag: boolean;
  id: number;
}

/**
 * 页面新增or移除应用
 * @param props
 */
function addOrRemoveApp(props: AddOrRemoveAppParams | number): Promise<AddOrRemoveAppRes> {
  const data: AddOrRemoveAppParams = isNumber(props) ? { funcId: props } : props;
  if (isNil(data.flag)) {
    data.flag = 0;
  }
  return request()('/v2/desktop/apply/addOrRemove', {
    data,
    method: 'POST',
  });
}

export default addOrRemoveApp;
