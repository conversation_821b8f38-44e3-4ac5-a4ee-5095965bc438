import { isNil } from '@echronos/core';
import request from './request';

export interface CreateDesktopExternalAppData {
  // 图标
  photo: string;
  // 名称
  name: string;
  // 外部应用地址
  url?: string;
  // 是否打开新标签页
  isOpen?: 0 | 1;
  // 颜色值
  colorValue?: string;
  // 图标文字
  text?: string;
  // 图标类型: 1 在线图标  2 纯色图标  3 自定义图标
  type?: 1 | 2 | 3;

  id?: number;
  memberId?: number;
  companyId?: number;
}

/**
 * 创建桌面外部应用
 * @param props
 */
function createDesktopExternalApp(props: CreateDesktopExternalAppData) {
  const data = props;
  if (isNil(data.isOpen)) {
    data.isOpen = 1;
  }
  return request()('/v2/desktop/self/build', {
    data,
    method: 'POST',
  });
}

export default createDesktopExternalApp;
