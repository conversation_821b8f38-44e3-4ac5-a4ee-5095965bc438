import { PaginationResponse } from '@echronos/core';
import { IMyAppContent } from '../model';
import request from './request';

export type IApp = IMyAppContent & { exist: number };

/**
 * 批量过滤可添加的应用列表
 * @param params
 */
function getDesktopAllApp(params: { search: string }): PaginationResponse<IApp> {
  return request()('/v2/desktop/all/apply', {
    params,
    method: 'GET',
  });
}

export default getDesktopAllApp;
