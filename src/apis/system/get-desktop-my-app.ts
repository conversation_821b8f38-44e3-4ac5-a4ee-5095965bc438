import { PaginationResponse } from '@echronos/core';
import { IMyApp, IMyAppData } from '../model';
import request from './request';

const componentMap: Record<string, string> = {
  banner: 'banner',
  schedule: 'calendar',
  shareApp: 'shareApp',
  flowEngine: 'processApprove',
};

/**
 * 批量过滤可添加的应用列表
 * @param data
 */
function getDesktopMyApp(): PaginationResponse<IMyApp> {
  return request()('/v2/desktop/index', {
    data: {},
    method: 'GET',
  }).then(
    (res: {
      list: IMyAppData[];
      pagination: {
        count: number;
        total: number;
      };
    }) => ({
      list: [
        ...res.list.map((item) => ({
          ...item,
          componentType: item.type === 3 ? 'app' : componentMap[item.coding],
          level: item.type === 2 ? 1 : 0,
        })),
      ],
      pagination: res.pagination,
    })
  );
}

export default getDesktopMyApp;
