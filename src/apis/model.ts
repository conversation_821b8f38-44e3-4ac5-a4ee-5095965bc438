export interface IMyAppContent {
  appId: number; // 应用主键
  coding: string; // 功能code
  display: number; // 显示设置: 0 web端  1 APP端 2 都显示
  funcDesc: string; // 功能描述
  id: number; // 功能主键
  isCharge: number; // 是否购买：0 未购买， 1已购买
  isCompanyStop: number; // 是否公司应用级：0 启用， 1停用
  isOpen: number; // 是否打开新页面：0否 1是
  isPermission: number; // 是否有权限：0 无， 1有
  level: number; // 是否企业级固定应用： 0否 1是
  name: string; // 功能名称
  photo: string; // 图标
  updateTime: number; // 更新时间
  url: string; // 路由
  useStatus: number; // 平台级停用状态：0 未启用， 1 启用中
  sort: number; //	排序
  externalApp: number; // 是否外部app
  // 应用分类 1：内部 2：外部 3：小程序 4:小组件
  classify: 1 | 2 | 3 | 4;
  echAppId: string;
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'; // 应用逻辑大小
  export?: string; // 小组件组件名--过渡方案
  relationId: number; // 关系id
  groupId: number; // 分组id，文件夹分组
}

export interface IMyAppData extends IMyAppContent {
  // content: IMyAppContent[] | string;
  coding: string; // 功能code
  content: any | null;
  type: number; // 1 图片 2 功能性组件 3 应用 4 文件夹
}

interface NycbngAppFunction {
  id: number; // 功能主键
  appId: number; // 应用主键
  name: string; // 功能名称
  url: string; // url
  funcDesc: string; // 功能描述
  sort: number; // 排序
  coding: string; // 编码
  classify: number; // 功能类别：0-基本功能 1-定制功能 2-系统功能
  style: number; // 功能风格：0-原平台 1-APP风格 2-聊天列表功能
  styleName: string; // 功能风格：0-原平台 1-APP风格 2-聊天列表功能
  isOpen: number; // 是否打开新标签：0否 1是
  visibleSet: string; // 可见设置：0-施工方 1-商户 2-供货商 3-华元素 4-华立方 用逗号隔开
  photo: string; // 功能图片
  picRound: string; // 功能图片(圆形)
  display: number; // 显示设置: 0 web端  1 APP端 2 都显示
  useStatus: number; // 使用状态：0-停用 1-启用
  createUser: number; // 创建人
  updateUser: number; // 修改人
  createTime: string; // 创建时间
  updateTime: string; // 修改时间
  status: number; // 状态 0正常
  isDeleted: number; // 是否已删除：0否 1是
  counter: number; // 使用次数
  useDate: string; // 最近使用时间
  flag: boolean; // 是否为固定应用
  flagSort: number; // 固定应用排序
}

export interface OrgBusinessApp {
  orgId: number; // 分销组织ID
  orgName: string; // 分销组织名称
  companyId: number; // 供应商ID
  companyName: string; // 供应商公司名
  disCompanyId: number; // 分销商公司ID
  disCompanyName: string; // 分销商公司名
  nycbngAppFunctionList: NycbngAppFunction[]; // 分销应用列表
}

export type IMyApp = IMyAppData &
  IMyAppContent & { componentType: string; orgBusiness?: OrgBusinessApp };

export interface MessageBlock {
  id: number; // 自增主键
  appId: string; // 应用唯一标识appId
  blockName: string; // 信息块名称
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'; // 尺寸
  brief: string; // 简介
  packageAddress: string; // 信息块代码包回传地址
  backgroundImage: string; // 背景图片
  memberId: number; // 发布人
}

export interface Card {
  appId: string; // 应用appId
  appName: string; // 应用名称
  iconUrl: string; // 应用图标
  blockList: MessageBlock[];
}
