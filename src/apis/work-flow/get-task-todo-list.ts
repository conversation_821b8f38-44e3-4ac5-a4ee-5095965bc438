import { createHttpRequest, PaginationResponse } from '@echronos/core';

export interface ToDoTask {
  memberResp: {
    id: number; // 员工memberId
    name: string; // 员工名称
    url: string; // 员工头像
    avatar: number;
    phone: string; // 员工电话号码
    userId: number; // 员工用户ID
  };
  processInstanceId: string; // 流程实例id （流程id）
  processName: string; // 流程名称
  status: number; // 流程状态：0 待审核，1:通过，2:拒绝 3:撤销
  starTime: string; // 流程开始时间
  endTime: string; // 流程结束时间
  taskId: string; // 任务id
  taskName: string; // 任务名称
  readStatus: number; // 信息阅读状态 1 已读 0 未读
  processInstanceFormData: string; // 流程表单数据
  isRevokeProcess: false; // 是否是撤销流程 true 是  false 否
}

interface GetTaskTodoListParams {
  pageNo: number;
  pageSize: number;
}

/**
 * 获取待审批数据
 * @param data
 */
function getTaskTodoList(params: GetTaskTodoListParams): PaginationResponse<ToDoTask> {
  return createHttpRequest('ech-workflow')('/v1/flowable/task/listTodo', {
    params,
    method: 'GET',
  });
}

export default getTaskTodoList;
