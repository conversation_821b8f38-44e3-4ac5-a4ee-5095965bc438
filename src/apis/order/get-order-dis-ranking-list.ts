import { createHttpRequest } from '@echronos/core';
import type { PaginationResponse, Paginate } from '@echronos/core';

export type GetOrderDisRankingListOption = {
  companyName: string; // 公司名称
  companyId: number; // 公司id
  totalCount: number; // 订单总数量
  totalAmount: number; // 订单总金额
};

export type GetOrderDisRankingListParams = Paginate & {
  tenantId?: string;
  key?: string; // 搜索关键字
  statisticType: number; // 统计类型：1-本月，2-上月，3-本年，4-自定义
  sortType?: number; // 排序类型：1-按金额从高到低，2-按金额从低到高，3-按订单数从高到低，4-按订单数从低到高
  startDate?: number; // 自定义时开始日期
  endDate?: number; // 自定义时结束日期
  ifError?: number; // 是否判断权限
};

/**
 * 获取分销站点
 * @param typeCode
 */
function getOrderDisRankingList(
  params: GetOrderDisRankingListParams
): PaginationResponse<GetOrderDisRankingListOption> {
  return createHttpRequest('ech-order')('/v1/order/distribution/chart/rank', {
    params,
    autoToast: false,
  });
}

export default getOrderDisRankingList;
