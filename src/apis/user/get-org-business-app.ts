import { createHttpRequest, PaginationResponse } from '@echronos/core';
import { IMyApp, OrgBusinessApp } from '../model';

/**
 * 获取分销组织数据
 * @param data
 */
function getOrgBusinessApp(): PaginationResponse<IMyApp> {
  return createHttpRequest('ech-user')('/v1/nycbng/org/business/app/list', {
    params: {},
    method: 'GET',
  }).then(
    (res: {
      list: OrgBusinessApp[];
      pagination: {
        count: number;
        total: number;
      };
    }) => {
      const appList = res.list.map(
        (item) =>
          ({
            coding: `${item.orgId}`,
            content: '',
            sort: -1,
            type: 2,
            appId: item.orgId,
            display: 0,
            funcDesc: '',
            id: item.companyId,
            isCharge: 1,
            isCompanyStop: 0,
            isOpen: 0,
            isPermission: 1,
            level: 1,
            name: item.companyName,
            photo: '',
            updateTime: 0,
            url: '',
            useStatus: 1,
            componentType: 'shareApp',
            orgBusiness: item,
            externalApp: 0,
          } as IMyApp)
      );
      return {
        list: appList,
        pagination: res.pagination,
      };
    }
  );
}

export default getOrgBusinessApp;
