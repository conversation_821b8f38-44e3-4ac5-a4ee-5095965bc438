import { createHttpRequest } from '@echronos/core';

interface CompanyResult {
  companyId: number;
  companyName: string;
  companyPhone: string;
  companyNature: number;
  companyCategory: number;
  memberId: number;
  memberName: string;
  name: string;
  avatar: string;
  logoUrl: string;
  tenantId: string;
  isAdmin: 0 | 1 | boolean;
  isRecord: 0 | 1 | boolean;
  isSelected: 0 | 1 | boolean;
  isShop: 0 | 1 | boolean;
  isSubCompany: 0 | 1 | boolean;
  status: number;
}

export interface JoinCompanyResult extends CompanyResult {
  id: number;
  logo: number;
  memberId: number;
  category: number;
  nature: number;
}

function getJoinCompanies(): Promise<JoinCompanyResult[]> {
  return createHttpRequest('ech-user')('/v1/user/team/query/companyList', { method: 'POST' }).then(
    (res: { list?: CompanyResult[] }) =>
      (res.list || []).map((item) => {
        const company = item;
        if (!company.avatar) {
          company.avatar = `${
            import.meta.env.BIZ_ORIGIN_STATIC_URL
          }/static/img/default_avatar/24.png`;
        }
        if (!company.logoUrl) {
          company.logoUrl = `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/company_logo.png`;
        }
        return {
          ...company,
          id: company.companyId,
          name: company.companyName,
          logo: company.logoUrl,
          memberId: company.memberId,
          memberName: company.memberName,
          category: company.companyCategory,
          nature: company.companyNature,
          isSelected: company.isSelected,
          isAdmin: company.isAdmin,
        };
      })
  );
}

export default getJoinCompanies;
