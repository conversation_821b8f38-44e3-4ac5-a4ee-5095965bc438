import { createHttpRequest } from '@echronos/core';

export type GetImsDisStatisticOption = {
  normalCount: number;
  pendingApprovalCount: number;
  pendingApprovalNameList: string[];
  tenantId: string;
};

type GetImsDisStatisticParams = {
  tenantId?: string;
};

/**
 * 获取分销站点
 * @param typeCode
 */
function getImsDisStatistic(params: GetImsDisStatisticParams): Promise<GetImsDisStatisticOption> {
  return createHttpRequest('ech-ims')('/drp/get/distributor/statistic', {
    params,
    autoToast: false,
  });
}

export default getImsDisStatistic;
