import { popupComponent } from '@echronos/react';
import { isString } from '@echronos/core';
import ImageCropModal, { ImageCropModalProps } from './image-crop-modal';

interface ImageCropFn {
  // eslint-disable-next-line no-unused-vars
  (image: string | File | Blob): Promise<File | string | Blob>;
  // eslint-disable-next-line no-unused-vars
  (props: ImageCropModalProps): Promise<File | string | Blob>;
}

const imageCropFn: ImageCropFn = (image) => {
  let props: ImageCropModalProps;
  if (isString(image) || image instanceof Blob) {
    props = { image } as ImageCropModalProps;
  } else {
    props = image;
  }

  return new Promise<File | Blob | string>((resolve) => {
    let hasOk = false;
    popupComponent(ImageCropModal, {
      ...props,
      onCrop: (img) => {
        hasOk = true;
        resolve(img);
      },
      afterClose: () => {
        if (!hasOk) {
          resolve(props.image);
        }
      },
    });
  });
};

export default imageCropFn;
