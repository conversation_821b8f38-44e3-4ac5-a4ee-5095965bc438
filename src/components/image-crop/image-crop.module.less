@import '@echronos/react/less/index';

.box {
  position: relative;
}

.image {
  .flex-column(432px);

  height: 362px;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');
  overflow: hidden;
}

.full {
  .flex-column(100%);
}

.crop {
  width: 100%;
  height: 100%;

  :global {
    .ReactCrop__child-wrapper {
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;

      > img,
      > canvas {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .ReactCrop__crop-selection {
      &:not(.ReactCrop--no-animate .ReactCrop__crop-selection) {
        border: 1px solid @primary-color;
        background: none;
      }
    }

    .ReactCrop__drag-handle {
      margin-top: -3px;
      margin-left: -3px;

      &::after {
        width: 6px;
        height: 6px;
        border: none;
        background: @primary-color;
      }
    }
  }
}

.custom {
  .flex-column();

  :global {
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
    }
  }
}

.inputGroup {
  display: flex;
  align-items: center;

  :global {
    .ant-input {
      width: 57px;
    }
  }
}
