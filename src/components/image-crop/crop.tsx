import {
  Dispatch,
  forwardRef,
  SetStateAction,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Crop as ImageCrop, PixelCrop, ReactCrop, ReactCropProps } from 'react-image-crop';
import { EMPTY_FN } from '@echronos/core';
import 'react-image-crop/dist/ReactCrop.css';

export interface CropProps extends Partial<ReactCropProps> {
  image: string;
}

export interface CropInstance {
  resetCrop: MultipleParamsFn<[size?: number]>;
  resizeCrop: MultipleParamsFn<[]>;
  getCrop: MultipleParamsFn<[], ImageCrop>;
}

const Crop = forwardRef<CropInstance, CropProps>(
  ({ image, crop: initialCrop, aspect, onChange: onChangeCrop, ...props }, ref) => {
    const cropRef = useRef<ReactCrop>();
    const canvasRef = useRef(null as unknown as HTMLCanvasElement);
    const imageInfo = useRef({ w: 100, h: 100 });
    // @ts-ignore
    const [crop, setCrop] = useState<ImageCrop>(initialCrop);
    const onChange: ReactCropProps['onChange'] = useCallback(
      (imageCrop, preImageCrop) => {
        setCrop(imageCrop);
        onChangeCrop?.(imageCrop, preImageCrop);
      },
      [onChangeCrop]
    );
    const setPixelCrop = useRef<Dispatch<SetStateAction<ImageCrop>>>(EMPTY_FN);

    setPixelCrop.current = (pixelCrop: SetStateAction<ImageCrop>) => {
      const canvas = canvasRef.current;
      if (canvas) {
        const toPercentage = (val: number) => Math.round(val * 10000) / 100;
        const currentCrop = (
          typeof pixelCrop === 'function' ? pixelCrop(crop) : pixelCrop
        ) as PixelCrop;
        if (currentCrop.unit === 'px') {
          onChange(currentCrop, {
            width: toPercentage(currentCrop.width / canvas.clientWidth),
            height: toPercentage(currentCrop.height / canvas.clientHeight),
            x: toPercentage(currentCrop.x / canvas.clientWidth),
            y: toPercentage(currentCrop.y / canvas.clientHeight),
            unit: '%',
          });
        } else {
          setCrop(currentCrop);
        }
      }
    };
    const resetCrop = (s?: number) => {
      const canvas = canvasRef.current;
      const info = imageInfo.current;
      const size = s || Math.min(info.w, info.h);
      setPixelCrop.current({
        x: (canvas.clientWidth - size) / 2,
        y: (canvas.clientHeight - size) / 2,
        width: size,
        height: size,
        unit: 'px',
      } as PixelCrop);
    };

    useEffect(() => {
      const canvas = canvasRef.current;
      const ctx = canvas && canvas.getContext('2d');
      if (!ctx) {
        return;
      }
      const { devicePixelRatio } = window;

      const img = document.createElement('img');
      img.src = image;
      img.onload = () => {
        const { naturalWidth, naturalHeight } = img;
        const { clientWidth, clientHeight, offsetHeight, offsetWidth } = canvas;
        let width = Math.max(offsetWidth, naturalWidth * devicePixelRatio);
        let height = Math.max(offsetHeight, naturalHeight * devicePixelRatio);
        if (naturalWidth > naturalHeight) {
          height = Math.ceil(width * (clientHeight / clientWidth));
        } else {
          width = Math.ceil(height * (clientWidth / clientHeight));
        }

        const y = Math.max(0, (height - naturalHeight * devicePixelRatio) / 2);
        const x = Math.max(0, (width - naturalWidth * devicePixelRatio) / 2);

        canvas.width = width;
        canvas.height = height;
        ctx.clearRect(0, 0, width, height);
        ctx.drawImage(img, x, y, width - x * 2, height - y * 2);

        const proportion = Math.min(
          1,
          naturalWidth > naturalHeight ? offsetWidth / naturalWidth : offsetHeight / naturalHeight
        );
        imageInfo.current = {
          w: Math.ceil(naturalWidth * proportion),
          h: Math.ceil(naturalHeight * proportion),
        };

        setTimeout(() => {
          resetCrop();
          img.remove();
        });
      };
    }, [image]); // eslint-disable-line
    useEffect(() => {
      const canvas = canvasRef.current;
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    }, []); // eslint-disable-line

    useImperativeHandle(
      ref,
      () => ({
        resetCrop,
        resizeCrop: () => {
          const current = aspect || 1;
          setPixelCrop.current((prevState) => {
            if (prevState) {
              const info = imageInfo.current;
              let width = info.w;
              let height = info.h;
              if (current < 1) {
                width *= current;
              } else if (current > 1) {
                height *= current;
              } else {
                width = Math.min(width, height);
                height = width;
              }

              if (width !== prevState.width || height !== prevState.height) {
                const canvas = canvasRef.current;
                return {
                  width,
                  height,
                  x: (canvas.clientWidth - width) / 2,
                  y: (canvas.clientHeight - height) / 2,
                  unit: 'px',
                };
              }
            }
            return prevState;
          });
        },
        getCrop: () => cropRef.current!.dragCrop(),
      }),
      [aspect] // eslint-disable-line
    );

    return (
      // @ts-ignore
      <ReactCrop {...props} ref={cropRef} aspect={aspect} crop={crop} onChange={onChange}>
        <canvas ref={canvasRef} id="crop-image" />
      </ReactCrop>
    );
  }
);

Crop.defaultProps = {
  ariaLabels: {
    cropArea: '移动鼠标选择裁剪区域',
    nwDragHandle: '移动鼠标到裁剪区域左上角拖动以更改裁剪区域大小',
    nDragHandle: '移动鼠标到裁剪区域上方拖动以更改裁剪区域大小',
    neDragHandle: '移动鼠标到裁剪区域右上角拖动以更改裁剪区域大小',
    eDragHandle: '移动鼠标到裁剪区域右方拖动以更改裁剪区域大小',
    seDragHandle: '移动鼠标到裁剪区域右下角拖动以更改裁剪区域大小',
    sDragHandle: '移动鼠标到裁剪区域下方拖动以更改裁剪区域大小',
    swDragHandle: '移动鼠标到裁剪区域左下角拖动以更改裁剪区域大小',
    wDragHandle: '移动鼠标到裁剪区域左方拖动以更改裁剪区域大小',
  },
};

export default Crop;
