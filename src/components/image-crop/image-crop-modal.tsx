import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Button, message, Modal, ModalProps } from 'antd';
// import { loadable } from '@echronos/react';
import { CLIENT, EMPTY_FN, fileToUrl, isString, revokeUrl } from '@echronos/core';
import type { PixelCrop } from 'react-image-crop';
import { useTranslation } from 'react-i18next';
import ImageCropBox, { ImageCropProps as ImageCropBoxProps } from './image-crop';
import { generateImage } from './utils';
import styles from './image-crop-modal.module.less';

export interface ImageCropModalProps
  extends Pick<
      ModalProps,
      'title' | 'visible' | 'okText' | 'cancelText' | 'afterClose' | 'onCancel' | 'onOk'
    >,
    Partial<Omit<ImageCropBoxProps, 'image'>> {
  image: File | Blob | string;
  onCrop?: MultipleParamsFn<[file: File]>;
}

// const ImageCropBox = loadable(() => import('./image-crop'));

function ImageCropModal({
  image,
  okText,
  cancelText,
  title,
  visible,
  afterClose,
  onChange,
  onCancel,
  onOk,
  onCrop,
  ...props
}: ImageCropModalProps) {
  const cropRef = useRef<PixelCrop | null>(null);
  const wrapRef = useRef<HTMLDivElement>(null as unknown as HTMLDivElement);
  const fileRef = useRef(null as unknown as HTMLInputElement);
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState<File | string | null>();
  // 多语言导入
  const { t } = useTranslation();
  const url = useMemo(() => {
    const currentFile = file || image;
    return isString(currentFile) ? currentFile : fileToUrl(currentFile);
  }, [file, image]);
  const onAfterClose = useCallback(() => {
    if (!isString(image)) {
      revokeUrl(url);
    }
    afterClose?.();
  }, [image, url]); // eslint-disable-line
  const onChangeCrop: ImageCropModalProps['onChange'] = useCallback(
    (crop, percentageCrop) => {
      cropRef.current = crop;
      if (percentageCrop) {
        onChange?.(crop, percentageCrop);
      }
    },
    [onChange]
  );
  const onConfirm: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      const crop = cropRef.current;
      // @ts-ignore
      const img = wrapRef.current?.querySelector('#crop-image') as HTMLCanvasElement;
      if (crop && img && CLIENT) {
        setLoading(true);
        generateImage(img, crop)
          .then(onCrop || EMPTY_FN)
          .finally(() => {
            setLoading(false);
            onOk?.(e);
          });
      } else {
        onOk?.(e);
      }
    },
    [onCrop, onOk]
  );
  const onReUpload = useCallback(() => {
    fileRef.current?.click();
  }, []);
  const onChangeFile: ChangeEventHandler<HTMLInputElement> = useCallback((e) => {
    const tmpFile = e.target.files?.[0];
    if (tmpFile) {
      if (tmpFile.type.indexOf('image/') !== 0) {
        message.error(`${t('desk_imgCrop_pleImg')}`);
      } else if (tmpFile.size > 5 * 1024 * 1024) {
        message.error(`${t('desk_imgCrop_uploadWrn')}`);
      } else {
        setFile(tmpFile);
      }
    }
    e.target.value = '';
    e.stopPropagation();
  }, []); //eslint-disable-line

  return (
    <Modal
      centered
      width={700}
      visible={visible}
      title={title ?? t('desk_imgCrop')}
      okText={okText}
      cancelText={cancelText}
      confirmLoading={loading}
      maskClosable={false}
      getContainer="micro-app-body"
      afterClose={onAfterClose}
      className={styles.modal}
      onCancel={onCancel}
      onOk={onConfirm}
    >
      <div ref={wrapRef} className="px-5">
        <ImageCropBox {...props} image={url} onChange={onChangeCrop} />
      </div>
      <input ref={fileRef} type="file" accept="image/*" hidden onChange={onChangeFile} />
      <Button type="text" size="small" className={styles.reUpload} onClick={onReUpload}>
        {t('desk_imgCrop_reUpload')}
      </Button>
    </Modal>
  );
}

ImageCropModal.defaultProps = {
  // eslint-disable-next-line react/default-props-match-prop-types
  title: undefined,
  // title: '图片裁剪',
  onCrop: EMPTY_FN,
};

export default ImageCropModal;
