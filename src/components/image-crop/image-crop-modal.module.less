@import '@echronos/react/less/index';

.modal {
  :global {
    .ant-modal-body {
      padding: 0;
    }

    .ant-modal-footer {
      .ant-btn {
        font-size: @font-size-xs;
        height: 28px;
        min-height: 28px;
        line-height: 20px;
        padding-top: 4px;
        padding-bottom: 4px;
      }

      .ant-btn-primary {
        color: @white;
      }
    }
  }
}

.reUpload {
  color: @primary-color;
  font-size: @font-size-base;
  position: absolute;
  bottom: 24px;
  left: 24px;

  &:hover {
    color: rgb(@primary-color 0.8);
  }
}
