import { useCallback, useEffect, useRef, useState } from 'react';
import { Col, Form, Input, Radio, Row } from 'antd';
import classNames from 'classnames';
import type { RadioChangeEvent } from 'antd/lib/radio/interface';
import { useTranslation } from 'react-i18next';
import Crop, { CropInstance, CropProps } from './crop';
import styles from './image-crop.module.less';

export interface ImageCropProps extends CropProps {
  full?: boolean;
  // image: string | File | Blob;
}

function ImageCrop({ aspect, full, children, className, ...props }: ImageCropProps) {
  const [form] = Form.useForm();
  const cropRef = useRef<CropInstance>(null as unknown as CropInstance);
  const [cropType, setCropType] = useState(0);
  const [currentAspect, setAspect] = useState<CropProps['aspect']>(aspect);
  // 多语言导入
  const { t } = useTranslation();
  const onChangeAspect = useCallback(() => {
    const numerator = Number(form.getFieldValue('numerator'));
    const denominator = Number(form.getFieldValue('denominator'));

    if (numerator && denominator) {
      setAspect(numerator / denominator);
      setTimeout(() => {
        cropRef.current?.resizeCrop();
      }, 50);
    }
  }, [form]);
  const onChangeCropType = useCallback(
    (e: RadioChangeEvent) => {
      const { value } = e.target;
      setCropType(value);
      cropRef.current?.resetCrop();
      if (!value) {
        setAspect(undefined);
      } else {
        form.setFieldsValue({ numerator: 1, denominator: 1 });
        setTimeout(onChangeAspect, 50);
      }
    },
    [form, onChangeAspect]
  );

  useEffect(() => {
    setAspect(aspect);
  }, [aspect]);

  return (
    <Row className={classNames(styles.box, className)}>
      <Col className={classNames(styles.image, { [styles.full]: full })}>
        <Crop {...props} ref={cropRef} aspect={currentAspect} className={styles.crop} />
      </Col>
      {!full && (
        <Col className={classNames(styles.custom, 'pl-5')}>
          {children || (
            <Form form={form}>
              <p className="mb-3">{t('desk_imgCrop_dynamicCrop')}</p>
              <Form.Item name="type" initialValue={0} noStyle>
                <Radio.Group onChange={onChangeCropType}>
                  <Radio value={0} className="mr-0 mb-2">
                    {t('desk_imgCrop_randImgCrop')}
                  </Radio>
                  <Radio value={1} className="mr-0">
                    {t('desk_imgCrop_customCrop')}
                  </Radio>
                </Radio.Group>
              </Form.Item>
              {cropType === 1 && (
                <div className={classNames('mt-3', styles.inputGroup)}>
                  <Form.Item name="numerator" initialValue={1} noStyle>
                    <Input type="number" min={1} onChange={onChangeAspect} />
                  </Form.Item>
                  <span className="px-2">:</span>
                  <Form.Item name="denominator" initialValue={1} noStyle>
                    <Input type="number" min={1} onChange={onChangeAspect} />
                  </Form.Item>
                </div>
              )}
            </Form>
          )}
        </Col>
      )}
    </Row>
  );
}

ImageCrop.defaultProps = {
  full: false,
};

export default ImageCrop;
