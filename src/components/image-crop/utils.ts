import type { PixelCrop } from 'react-image-crop';

const TO_RADIANS = Math.PI / 180;

export const canvasPreview = async (
  image: HTMLCanvasElement,
  canvas: HTMLCanvasElement,
  crop: PixelCrop,
  scale = 1,
  rotate = 0
) => {
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('No 2d context');
  }

  const scaleX = image.width / image.clientWidth;
  const scaleY = image.height / image.clientHeight;
  // devicePixelRatio slightly increases sharpness on retina devices
  // at the expense of slightly slower render times and needing to
  // size the image back down if you want to download/upload and be
  // true to the images natural size.
  const pixelRatio = window.devicePixelRatio;
  // const pixelRatio = 1

  // eslint-disable-next-line no-param-reassign
  canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
  // eslint-disable-next-line no-param-reassign
  canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

  ctx.scale(pixelRatio, pixelRatio);
  ctx.imageSmoothingQuality = 'high';

  const cropX = crop.x * scaleX;
  const cropY = crop.y * scaleY;

  const rotateRads = rotate * TO_RADIANS;
  const centerX = image.width / 2;
  const centerY = image.height / 2;

  ctx.save();

  // 5) Move the crop origin to the canvas origin (0,0)
  ctx.translate(-cropX, -cropY);
  // 4) Move the origin to the center of the original position
  ctx.translate(centerX, centerY);
  // 3) Rotate around the origin
  ctx.rotate(rotateRads);
  // 2) Scale the image
  ctx.scale(scale, scale);
  // 1) Move the center of the image to the origin (0,0)
  ctx.translate(-centerX, -centerY);
  ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, image.width, image.height);
  ctx.restore();
};

export const generateImage = async (
  image: HTMLCanvasElement,
  crop: PixelCrop,
  scale?: number,
  rotate?: number
) => {
  const canvas = document.createElement('canvas');
  canvas.width = crop.width;
  canvas.height = crop.height;
  canvas.style.position = 'fixed';
  canvas.style.left = '100vw';

  try {
    document.body.appendChild(canvas);

    await canvasPreview(image, canvas, crop, scale, rotate);

    return new Promise<File>((resolve, reject) => {
      canvas.toBlob((result) => {
        if (result) {
          resolve(new File([result], 'image.png'));
        } else {
          reject();
        }
      }, 'image/png');
    });
  } finally {
    canvas.remove();
  }
};
