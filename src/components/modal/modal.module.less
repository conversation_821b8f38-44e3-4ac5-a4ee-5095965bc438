.echModalConfirm {
  :global {
    & .ant-modal-content {
      box-shadow: 0 32px 64px 0 rgb(0 0 0 / 22%), 0 2px 21px 0 rgb(0 0 0 / 22%);
      border-radius: 18px;
    }

    & .ant-modal-body {
      padding: 20px;
    }

    & .ant-btn {
      padding: 7px 20px;
    }

    & [role='img'] {
      display: none;
    }

    & .ant-modal-confirm-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 12px;
    }

    & .ant-modal-confirm-content {
      font-size: 14px;
      line-height: 20px;
      margin-top: 0;
    }

    & .ant-modal-confirm-body > .anticon + .ant-modal-confirm-title + .ant-modal-confirm-content {
      margin-left: 0;
    }

    & .ant-modal-confirm-btns {
      margin-top: 32px;
    }

    & .ant-modal-confirm-btns .ant-btn + .ant-btn {
      margin-left: 16px;
    }
  }
}

.echModal {
  :global {
    & .ant-modal-content {
      background-color: #fff;
      box-shadow: 0 32px 64px 0 rgb(0 0 0 / 22%), 0 2px 21px 0 rgb(0 0 0 / 22%);
      border-radius: 18px;
    }

    & .ant-modal-header,
    & .ant-modal-content .ant-modal-header {
      padding: 20px;
      border-bottom: none;
    }

    & .ant-modal-body,
    & .ant-modal-content .ant-modal-body {
      margin: 0 20px;
      padding: 0;
      // background-color: #f5f6fa;
    }

    & .ant-modal-footer {
      padding: 20px;
      padding-top: 24px;
      border-top: none;
    }

    & .ant-modal-title {
      color: #040919;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }

    & .ant-modal-close {
      color: #999eb2;
    }

    & .ant-modal-close-x {
      width: 62px;
      height: 62px;
      line-height: 62px;
    }
  }
}
