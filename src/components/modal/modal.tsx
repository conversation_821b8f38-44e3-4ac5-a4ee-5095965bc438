import { PropsWithChildren, ReactNode, useEffect, useState } from 'react';
import { Modal, ModalProps, ModalFuncProps, ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import i18next from 'i18next';
import classNames from 'classnames';
import ReactDOM from 'react-dom';
import styles from './modal.module.less';

export const MODAL_TEXT = {
  zh: {
    cancel: '取消',
    confirm: '确定',
  },
  en: {
    cancel: 'Cancel',
    confirm: 'Confirm',
  },
};
function EchModal({ children, wrapClassName, ...props }: PropsWithChildren<ModalProps>) {
  const classes = classNames(styles.echModal, wrapClassName);
  return (
    <Modal width={700} centered {...props} wrapClassName={classes}>
      {children}
    </Modal>
  );
}

EchModal.displayName = 'EchModal';

interface EchModalConfig extends ModalProps {
  content?: ReactNode;
  visible?: boolean;
}

EchModal.confirm = function confirmFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  const currentLang = i18next.language || 'zh';

  return Modal.confirm({
    width: 311,
    cancelText: MODAL_TEXT[currentLang as keyof typeof MODAL_TEXT]?.cancel || '取消',
    okText: MODAL_TEXT[currentLang as keyof typeof MODAL_TEXT]?.confirm || '确定',
    ...props,
    className: classes,
  });
};

EchModal.info = function infoFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  return Modal.info({
    width: 311,
    ...props,
    className: classes,
  });
};

function EchModalBox(props: EchModalConfig) {
  const { content, visible } = props;
  const [isVisible, setVisible] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setVisible(visible as boolean);
    });
  }, []); // eslint-disable-line

  return (
    <ConfigProvider locale={zhCN}>
      <EchModal {...props} visible={isVisible}>
        {content}
      </EchModal>
    </ConfigProvider>
  );
}

EchModal.getInstance = () => {
  const container = document.createDocumentFragment();

  function render(config: EchModalConfig) {
    ReactDOM.render(
      <EchModalBox
        visible
        onCancel={() => {
          close(); // eslint-disable-line
        }}
        {...config}
      >
        {config.content}
      </EchModalBox>,
      container
    );
  }

  function open(config: EchModalConfig) {
    render({ ...config, visible: true });
  }

  function destroy() {
    ReactDOM.unmountComponentAtNode(container);
  }

  function close() {
    render({ visible: false });
    destroy();
  }

  return {
    open,
    close,
  };
};

EchModalBox.defaultProps = {
  content: null,
  visible: true,
};

export default EchModal;
