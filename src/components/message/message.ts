import { message } from 'antd';
import { ReactNode } from 'react';

export default class Message {
  static config = () => {
    message.config({
      maxCount: 1,
      getContainer: () => document.querySelector('#micro-root') as HTMLElement,
    });
  };

  static success = (content: string | ReactNode, duration?: number, onClose?: () => void) => {
    Message.config();
    message.success(content, duration, onClose);
  };

  static info = (content: string | ReactNode, duration?: number, onClose?: () => void) => {
    Message.config();
    message.info(content, duration, onClose);
  };

  static error = (content: string | ReactNode, duration?: number, onClose?: () => void) => {
    Message.config();
    message.error(content, duration, onClose);
  };

  static warning = (content: string | ReactNode, duration?: number, onClose?: () => void) => {
    Message.config();
    message.warning(content, duration, onClose);
  };
}
