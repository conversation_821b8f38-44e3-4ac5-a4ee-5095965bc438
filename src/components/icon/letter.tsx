import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const letterIcon: IconComponentProps['icon'] = {
  name: 'letter',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M10.769 15.864H12.1l-1.726-4.487L7.78 3.75H6.266L2.083 15.864h1.323l1.26-3.649h4.7l1.403 3.649zm-1.85-4.903H5.1l1.924-5.575 1.897 5.575z',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M15.25 9.822c-.694 0-1.246.138-1.673.414-.472.299-.775.769-.896 1.385l.885.07c.091-.376.281-.664.584-.847.27-.186.618-.265 1.046-.265 1.009 0 1.517.493 1.517 1.49v.309l-1.393.022c-.877.012-1.55.187-2.021.508-.541.343-.797.847-.797 1.522 0 .482.179.885.537 1.194.347.299.808.448 1.393.448.537 0 1.023-.127 1.447-.37a2.67 2.67 0 0 0 .889-.79v.996h.84v-3.802c0-.69-.179-1.228-.515-1.62-.406-.448-1.009-.664-1.842-.664zm1.463 3.246v.504c0 .459-.216.87-.629 1.224-.428.37-.91.552-1.462.552-.358 0-.65-.093-.874-.265a.815.815 0 0 1-.329-.675c0-.858.655-1.295 1.956-1.317l1.338-.023z',
        },
      },
    ],
  },
};

function Letter(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={letterIcon} />;
}

Letter.displayName = 'EchLetterIcon';

export default Letter;
