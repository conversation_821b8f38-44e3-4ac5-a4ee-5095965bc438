import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const copyIcon: IconComponentProps['icon'] = {
  name: 'copy',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M19.65099992752075,3.749000072479248L7.950999927520752,3.749000072479248C7.620999927520752,3.749000072479248,7.350999927520752,4.019000072479248,7.350999927520752,4.349000072479248L7.350999927520752,5.850000072479248L16.050999927520753,5.850000072479248C17.210999927520753,5.850000072479248,18.15099992752075,6.790000072479248,18.15099992752075,7.950000072479248L18.15099992752075,16.649000072479247L19.65099992752075,16.649000072479247C19.980999927520752,16.649000072479247,20.250999927520752,16.37900007247925,20.250999927520752,16.04900007247925L20.250999927520752,4.349000072479248C20.250999927520752,4.019000072479248,19.980999927520752,3.749000072479248,19.65099992752075,3.749000072479248ZM18.15099992752075,18.149000072479247L19.65099992752075,18.149000072479247C20.210999927520753,18.149000072479247,20.74099992752075,17.929000072479248,21.13099992752075,17.539000072479247C21.530999927520753,17.13900007247925,21.750999927520752,16.609000072479247,21.750999927520752,16.04900007247925L21.750999927520752,4.349000072479248C21.750999927520752,3.189000072479248,20.81099992752075,2.249000072479248,19.65099992752075,2.249000072479248L7.950999927520752,2.249000072479248C6.790999927520752,2.249000072479248,5.850999927520752,3.189000072479248,5.850999927520752,4.349000072479248L5.850999927520752,5.850000072479248L4.350999927520752,5.850000072479248C3.190999927520752,5.850000072479248,2.250999927520752,6.790000072479248,2.250999927520752,7.950000072479248L2.250999927520752,19.650000072479248C2.250999927520752,20.810000072479248,3.190999927520752,21.75000007247925,4.350999927520752,21.75000007247925L16.050999927520753,21.75000007247925C17.210999927520753,21.75000007247925,18.15099992752075,20.810000072479248,18.15099992752075,19.650000072479248L18.15099992752075,18.149000072479247ZM3.750999927520752,19.650000072479248L3.750999927520752,7.950000072479248C3.750999927520752,7.6200000724792485,4.0209999275207515,7.350000072479248,4.350999927520752,7.350000072479248L16.050999927520753,7.350000072479248C16.380999927520755,7.350000072479248,16.65099992752075,7.6200000724792485,16.65099992752075,7.950000072479248L16.65099992752075,19.650000072479248C16.65099992752075,19.98000007247925,16.380999927520755,20.25000007247925,16.050999927520753,20.25000007247925L4.350999927520752,20.25000007247925C4.0209999275207515,20.25000007247925,3.750999927520752,19.98000007247925,3.750999927520752,19.650000072479248Z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Copy(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={copyIcon} />;
}

Copy.displayName = 'EchCopyIcon';

export default Copy;
