import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const leftIcon: IconComponentProps['icon'] = {
  name: 'left',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M16.031331329345704,6.531745700836182C16.321331329345703,6.241745700836182,16.321331329345703,5.761745700836181,16.031331329345704,5.471745700836181C15.741331329345703,5.181745700836181,15.261331329345703,5.181745700836181,14.971331329345702,5.471745700836181L8.971331329345704,11.47174570083618C8.831331329345703,11.611745700836181,8.751331329345703,11.801745700836182,8.751331329345703,12.001745700836182C8.751331329345703,12.201745700836181,8.831331329345703,12.391745700836182,8.971331329345704,12.531745700836183L14.971331329345702,18.531745700836183C15.111331329345703,18.671745700836183,15.301331329345704,18.75174570083618,15.501331329345703,18.75174570083618C15.701331329345702,18.75174570083618,15.891331329345704,18.671745700836183,16.031331329345704,18.531745700836183C16.321331329345703,18.241745700836184,16.321331329345703,17.76174570083618,16.031331329345704,17.47174570083618L10.561331329345704,12.001745700836182L16.031331329345704,6.531745700836182Z',
        },
      },
    ],
  },
};

function Left(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={leftIcon} />;
}

Left.displayName = 'EchLeftIcon';

export default Left;
