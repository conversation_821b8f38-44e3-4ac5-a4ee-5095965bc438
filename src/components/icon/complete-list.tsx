import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const completeListIcon: IconComponentProps['icon'] = {
  name: ' completeList',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M3.333 6.042a.623.623 0 0 1-.441-.184l-1.25-1.25a.625.625 0 0 1 .884-.883l.807.807 2.059-2.057a.625.625 0 1 1 .884.883l-2.5 2.5a.623.623 0 0 1-.443.184zm0 5.833a.623.623 0 0 1-.441-.183l-1.25-1.25a.625.625 0 0 1 .884-.884l.807.808 2.059-2.058a.625.625 0 1 1 .884.884l-2.5 2.5a.623.623 0 0 1-.443.183zm0 5.833a.624.624 0 0 1-.441-.183l-1.25-1.25a.625.625 0 0 1 .884-.883l.807.807 2.059-2.057a.625.625 0 1 1 .884.883l-2.5 2.5a.624.624 0 0 1-.443.183zm14.584-7.083H8.75a.625.625 0 1 1 0-1.25h9.167a.625.625 0 1 1 0 1.25zm0 5.833H8.75a.625.625 0 0 1 0-1.25h9.167a.625.625 0 1 1 0 1.25zm0-11.666H8.75a.625.625 0 0 1 0-1.25h9.167a.625.625 0 1 1 0 1.25z',
        },
      },
    ],
  },
};

function CompleteList(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={completeListIcon} />;
}

CompleteList.displayName = 'EchCompleteListIcon';

export default CompleteList;
