@font-face {
  font-family: echronos;
  src: url('fonts/echronos.woff2?t=1644400091203') format('woff2'),
    url('fonts/echronos.woff?t=1644400091203') format('woff'),
    url('fonts/echronos.ttf?t=1644400091203') format('truetype'),
    url('fonts/echronos.svg?t=1644400091203#echronos') format('svg');
}

.icon {
  // stylelint-disable-next-line font-family-no-missing-generic-family-keyword
  font-family: echronos !important;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
}

.shangchuan::before {
  content: '\e701';
}

.gif::before {
  content: '\e649';
}

.praise::before {
  content: '\e64a';
}

.close::before {
  content: '\e644';
}

.shop-bags::before {
  content: '\e646';
}

.separate::before {
  content: '\e64d';
}

.download-outline::before {
  content: '\e64c';
}

.zu14895::before {
  content: '\e64e';
}

.tick-solid::before {
  content: '\e651';
}

.round::before {
  content: '\e652';
}

.warn-sold::before {
  content: '\e653';
}

.eye-open::before {
  content: '\e654';
}

.eye-close::before {
  content: '\e655';
}

.card::before {
  content: '\e656';
}

.c231::before {
  content: '\e657';
}

.tag::before {
  content: '\e658';
}

.b171::before {
  content: '\e659';
}

.more-sold::before {
  content: '\e65a';
}

.zu16937::before {
  content: '\e65c';
}

.zu16934::before {
  content: '\e65d';
}

.play::before {
  content: '\e662';
}

.remove-user::before {
  content: '\e666';
}

.config-tree::before {
  content: '\e667';
}

.location-outline::before {
  content: '\e668';
}

.at::before {
  content: '\e663';
}

.plane::before {
  content: '\e669';
}

.unlock::before {
  content: '\e664';
}

.lock::before {
  content: '\e66a';
}

.users::before {
  content: '\e66b';
}

.user-briefcase::before {
  content: '\e65e';
}

.d1::before {
  content: '\e65f';
}

.stopwatch::before {
  content: '\e660';
}

.close-solid::before {
  content: '\e661';
}

.blob::before {
  content: '\e66d';
}

.edit::before {
  content: '\e66e';
}

.tick::before {
  content: '\e66f';
}

.zoom-solid::before {
  content: '\e670';
}

.center::before {
  content: '\e671';
}

.table::before {
  content: '\e672';
}

.palette::before {
  content: '\e673';
}

.align-right::before {
  content: '\e674';
}

.align-left::before {
  content: '\e675';
}

.switch::before {
  content: '\e676';
}

.volume-outline::before {
  content: '\e678';
}

.emoji::before {
  content: '\e679';
}

.keyboard::before {
  content: '\e67a';
}

.cart::before {
  content: '\e64b';
}

.tick-outline::before {
  content: '\e64f';
}

.star-solid::before {
  content: '\e602';
}

.ad::before {
  content: '\e67c';
}

.bags-acitved::before {
  content: '\e604';
}

.c181::before {
  content: '\e605';
}

.withdraw::before {
  content: '\e650';
}

.selected::before {
  content: '\e606';
}

.double-up::before {
  content: '\e65b';
}

.bags::before {
  content: '\e66c';
}

.location-solid::before {
  content: '\e607';
}

.briefcase::before {
  content: '\e608';
}

.call::before {
  content: '\e609';
}

.mansion::before {
  content: '\e60a';
}

.wallet::before {
  content: '\e677';
}

.daatabase::before {
  content: '\e67d';
}

.rmb::before {
  content: '\e67e';
}

.purse::before {
  content: '\e67f';
}

.up-down::before {
  content: '\e680';
}

.down-solid::before {
  content: '\e681';
}

.info::before {
  content: '\e682';
}

.must::before {
  content: '\e683';
}

.printer::before {
  content: '\e610';
}

.brush::before {
  content: '\e684';
}

.more-circle::before {
  content: '\e685';
}

.left-arrow-circle::before {
  content: '\e686';
}

.plus-circle::before {
  content: '\e687';
}

.close-circle::before {
  content: '\e688';
}

.restore::before {
  content: '\e689';
}

.message-circle::before {
  content: '\e68a';
}

.share-circle::before {
  content: '\e68b';
}

.telephone-circle::before {
  content: '\e68c';
}

.flash-outline::before {
  content: '\e68e';
}

.migration::before {
  content: '\e690';
}

.flash-circle::before {
  content: '\e691';
}

.minus::before {
  content: '\e68f';
}

.question::before {
  content: '\e68d';
}

.member::before {
  content: '\e625';
}

.scan::before {
  content: '\e692';
}

.member-cricle::before {
  content: '\e694';
}

.cart-circle::before {
  content: '\e697';
}

.envelope::before {
  content: '\e698';
}

.display-list::before {
  content: '\e699';
}

.gift::before {
  content: '\e69a';
}

.lower-left-corner::before {
  content: '\e696';
}

.add-emoji::before {
  content: '\e69c';
}

.zoom::before {
  content: '\e69d';
}

.clock::before {
  content: '\e69f';
}

.close-outline::before {
  content: '\e6a0';
}

.male::before {
  content: '\e6a1';
}

.female::before {
  content: '\e6a2';
}

.pen::before {
  content: '\e6a3';
}

.lineation::before {
  content: '\e6a4';
}

.irregular::before {
  content: '\e6a5';
}

.more-outline::before {
  content: '\e6a6';
}

.switch-user::before {
  content: '\e6a7';
}

.date-circle::before {
  content: '\e6a8';
}

.briefcase-circle::before {
  content: '\e6aa';
}

.location-circle::before {
  content: '\e6ab';
}

.time-circle::before {
  content: '\e6a9';
}

.slash::before {
  content: '\e6ac';
}

.text::before {
  content: '\e6ae';
}

.control::before {
  content: '\e6ad';
}

.bar-chart::before {
  content: '\e6b0';
}

.pause::before {
  content: '\e6b1';
}

.down-circle::before {
  content: '\e6b2';
}

.bell-solid::before {
  content: '\e6b3';
}

.meeting-room::before {
  content: '\e6b9';
}

.more::before {
  content: '\e6ba';
}

.rubber::before {
  content: '\e6bb';
}

.lens::before {
  content: '\e6bc';
}

.xinzengricheng::before {
  content: '\e6bd';
}

.meeting::before {
  content: '\e6b6';
}

.crm::before {
  content: '\e6b7';
}

.loop::before {
  content: '\e6b8';
}

.ol::before {
  content: '\e6bf';
}

.title::before {
  content: '\e6c0';
}

.contract::before {
  content: '\e6be';
}

.business-card::before {
  content: '\e600';
}

.close-circle2::before {
  content: '\e69b';
}

.wifi::before {
  content: '\e60e';
}

.appoint::before {
  content: '\e69e';
}

.claim::before {
  content: '\e6c1';
}

.face-verification::before {
  content: '\e612';
}

.attendance-machine::before {
  content: '\e6c2';
}

.safe::before {
  content: '\e6c3';
}

.clock-in-fill::before {
  content: '\e695';
}

.statistics-fill::before {
  content: '\e693';
}

.setup-fill::before {
  content: '\e6b4';
}

.clock-in::before {
  content: '\e6b5';
}

.statistics::before {
  content: '\e6c5';
}

.apply::before {
  content: '\e6c4';
}

.setup::before {
  content: '\e6c6';
}

.apply-fill::before {
  content: '\e6af';
}

.community::before {
  content: '\e6c7';
}

.telephone::before {
  content: '\e665';
}

.user-fill::before {
  content: '\e60b';
}

.backup::before {
  content: '\e60c';
}

.flag-fill::before {
  content: '\e60d';
}

.bar-chart-outline::before {
  content: '\e60f';
}

.customer::before {
  content: '\e611';
}

.eaa::before {
  content: '\e6c9';
}

.cc::before {
  content: '\e6ca';
}

.complete::before {
  content: '\e6cb';
}

.scan-outline::before {
  content: '\e613';
}

.well-number::before {
  content: '\e614';
}

.plus::before {
  content: '\e615';
}

.add-user::before {
  content: '\e616';
}

.clip::before {
  content: '\e617';
}

.message::before {
  content: '\e618';
}

.shop::before {
  content: '\e619';
}

.zu13366::before {
  content: '\e61a';
}

.start-outline::before {
  content: '\e61b';
}

.address-book::before {
  content: '\e61c';
}

.filter::before {
  content: '\e61d';
}

.phone::before {
  content: '\e61e';
}

.copy::before {
  content: '\e61f';
}

.view::before {
  content: '\e620';
}

.image::before {
  content: '\e621';
}

.link::before {
  content: '\e622';
}

.tree::before {
  content: '\e623';
}

.zu13360::before {
  content: '\e624';
}

.location::before {
  content: '\e626';
}

.trash::before {
  content: '\e627';
}

.folder::before {
  content: '\e628';
}

.bell::before {
  content: '\e629';
}

.todo-list::before {
  content: '\e62a';
}

.share-outline::before {
  content: '\e62b';
}

.camera::before {
  content: '\e62c';
}

.upload-outline::before {
  content: '\e62d';
}

.reset::before {
  content: '\e62e';
}

.search::before {
  content: '\e62f';
}

.mute::before {
  content: '\e630';
}

.refund::before {
  content: '\e631';
}

.regular::before {
  content: '\e632';
}

.list::before {
  content: '\e633';
}

.zu13416::before {
  content: '\e634';
}

.info-outline::before {
  content: '\e635';
}

.share::before {
  content: '\e636';
}

.plus-outline::before {
  content: '\e637';
}

.refresh::before {
  content: '\e638';
}

.left::before {
  content: '\e639';
}

.right::before {
  content: '\e63a';
}

.zu13401::before {
  content: '\e63b';
}

.mansion-outline::before {
  content: '\e63c';
}

.upload::before {
  content: '\e63d';
}

.edit1::before {
  content: '\e63e';
}

.download::before {
  content: '\e63f';
}

.broadcast::before {
  content: '\e640';
}

.close-outline1::before {
  content: '\e641';
}

.down::before {
  content: '\e642';
}

.up::before {
  content: '\e643';
}

.setting::before {
  content: '\e645';
}

.search-tag::before {
  content: '\e647';
}

.calendar-outline::before {
  content: '\e648';
}

.certification::before {
  content: '\e6d8';
  color: #3670f8;
}

.add::before {
  content: '\e6f8';
}

.eye::before {
  content: '\e6fe';
}
