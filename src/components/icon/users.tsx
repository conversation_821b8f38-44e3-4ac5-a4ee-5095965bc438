import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const usersIcon: IconComponentProps['icon'] = {
  name: 'users',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M11.4651,13.0028C11.583,13.0038,11.6967,13.0051,11.8062,13.0069L12.417,13.0223L12.943,13.0495C13.8379,13.1117,14.3717,13.2452,14.9064,13.5177C15.8002,13.9731,16.526899999999998,14.6998,16.982300000000002,15.5936C17.0015,15.6312,17.02,15.6685,17.0378,15.7056L17.1367,15.9277L17.2207,16.1542L17.2908,16.3935C17.3015,16.4349,17.3115,16.4773,17.3211,16.520699999999998L17.3729,16.7952C17.3805,16.843600000000002,17.3878,16.8934,17.3946,16.9448L17.4306,17.2738L17.4576,17.6497L17.4768,18.081L17.4936,18.8506L17.5,20.75C17.5,21.1642,17.1642,21.5,16.75,21.5L1.7500010000000001,21.5C1.335633,21.5,0.99978292,21.164,1,20.7496L1.000714779,19.4022L1.00687075,18.6938L1.0223372,18.083L1.0495453,17.557000000000002C1.111676,16.662100000000002,1.245262,16.1283,1.51772,15.5936C1.973118,14.6998,2.69978,13.9731,3.59357,13.5177C4.1769099999999995,13.2205,4.7592,13.0885,5.81025,13.0343L6.37701,13.0133L7.03494,13.0028L11.4651,13.0028ZM19.9064,13.5177C20.8002,13.9731,21.5268,14.6998,21.9823,15.5936L22.0892,15.8167C22.1056,15.8536,22.1215,15.8906,22.1367,15.9277L22.2206,16.1542C22.2591,16.2699,22.2923,16.3905,22.3211,16.520699999999998L22.3728,16.7952L22.4137,17.104C22.4197,17.1587,22.4253,17.2153,22.4305,17.2738L22.4575,17.6497L22.4767,18.081L22.4936,18.8506L22.5,20.15L22.5,20.75C22.5,21.1642,22.1642,21.5,21.75,21.5C21.3772,21.5,21.0679,21.228,21.0098,20.8717L21,20.75L20.9986,19.4889L20.9898,18.6417L20.9781,18.1766L20.9495,17.605800000000002L20.9218,17.2969C20.9061,17.1548,20.8873,17.0304,20.8651,16.9184L20.8158,16.709600000000002C20.8067,16.6771,20.7973,16.6456,20.7873,16.6149L20.7223,16.4393L20.6457,16.2745C20.3342,15.663,19.8369,15.1658,19.2254,14.8542C18.8563,14.6662,18.7096,14.2145,18.8976,13.8455C19.0857,13.4764,19.5373,13.3296,19.9064,13.5177ZM11.4198,14.5025L7.08211,14.5025L6.50392,14.5109L6.01168,14.5269L5.59489,14.5518C5.21232,14.5816,4.92714,14.6266,4.69203,14.6926L4.47185,14.7658C4.40286,14.7926,4.33767,14.822,4.27454,14.8542C3.66302,15.1658,3.16582,15.663,2.8542300000000003,16.2745C2.82206,16.337699999999998,2.79265,16.402900000000002,2.76579,16.471899999999998L2.6926,16.692C2.61562,16.9663,2.56719,17.3086,2.53819,17.794L2.51788,18.2469L2.5058100000000003,18.7803L2.50067,19.9993L15.9993,19.9993L15.9967,19.1838L15.9898,18.6394L15.97,17.9673L15.9493,17.6024L15.9215,17.293300000000002L15.8856,17.0312L15.8409,16.8069L15.7862,16.6114L15.7209,16.4356L15.6839,16.3524L15.6458,16.2745C15.3342,15.663,14.837,15.1658,14.2255,14.8542C14.1623,14.822,14.0972,14.7926,14.0282,14.7658L13.808,14.6926C13.5729,14.6266,13.2879,14.5816,12.9056,14.5518L12.4891,14.5269L11.9974,14.5109L11.4198,14.5025ZM9.25,2C11.5972,2,13.5,3.90279,13.5,6.25C13.5,8.59721,11.5972,10.5,9.25,10.5C6.90279,10.5,5,8.59721,5,6.25C5,3.90279,6.90279,2,9.25,2ZM16.4409,2.607609C17.7081,3.37115,18.5001,4.74256,18.5001,6.25C18.5001,7.75744,17.7081,9.12885,16.4409,9.892389999999999C16.086199999999998,10.10617,15.6252,9.991869999999999,15.4115,9.637080000000001C15.1977,9.2823,15.312,8.821390000000001,15.6668,8.607610000000001C16.488,8.11275,17.0001,7.22608,17.0001,6.25C17.0001,5.27392,16.488,4.38725,15.6668,3.89239C15.312,3.67861,15.1977,3.2177,15.4115,2.8629160000000002C15.6252,2.508133,16.086199999999998,2.393829,16.4409,2.607609ZM9.25,3.5C7.73122,3.5,6.5,4.73122,6.5,6.25C6.5,7.76879,7.73122,9,9.25,9C10.76879,9,12,7.76879,12,6.25C12,4.73122,10.76879,3.5,9.25,3.5Z',
        },
      },
    ],
  },
};

function Users(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={usersIcon} />;
}

Users.displayName = 'EchUsersIcon';

export default Users;
