import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const dashboardIcon: IconComponentProps['icon'] = {
  name: 'dashboard',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M12.5,17.5L21.75,17.5C22.1642,17.5,22.5,17.1642,22.5,16.75L22.5,3.75C22.5,3.335786,22.1642,3,21.75,3L1.75,3C1.335786,3,1,3.335786,1,3.75L1,16.75C1,17.1642,1.335786,17.5,1.75,17.5L11,17.5L11,20L5.75,20C5.33579,20,5,20.3358,5,20.75C5,21.1642,5.33579,21.5,5.75,21.5L17.75,21.5C18.1642,21.5,18.5,21.1642,18.5,20.75C18.5,20.3358,18.1642,20,17.75,20L12.5,20L12.5,17.5ZM11.75,16L21,16L21,4.5L2.5,4.5L2.5,16L11.75,16ZM15,7.25C15,6.835789999999999,15.3358,6.5,15.75,6.5C16.1642,6.5,16.5,6.835789999999999,16.5,7.25L16.5,13.25C16.5,13.6642,16.1642,14,15.75,14C15.3358,14,15,13.6642,15,13.25L15,7.25ZM11.75,14C11.3358,14,11,13.6642,11,13.25L11,9.25C11,8.83579,11.3358,8.5,11.75,8.5C12.1642,8.5,12.5,8.83579,12.5,9.25L12.5,13.25C12.5,13.6642,12.1642,14,11.75,14ZM7,13.25L7,11.25C7,10.83579,7.33579,10.5,7.75,10.5C8.16421,10.5,8.5,10.83579,8.5,11.25L8.5,13.25C8.5,13.6642,8.16421,14,7.75,14C7.33579,14,7,13.6642,7,13.25Z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Dashboard(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={dashboardIcon} />;
}

Dashboard.displayName = 'EchDashboardIcon';

export default Dashboard;
