import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const folderIcon: IconComponentProps['icon'] = {
  name: 'folder',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M3.001 21.75c-.97 0-1.75-.78-1.75-1.75V5c0-.97.78-1.75 1.75-1.75h6.5c.22 0 .43.1.58.27l2.27 2.73h8.65c.97 0 1.75.79 1.75 1.75v12c0 .97-.78 1.75-1.75 1.75h-18zm6.15-17 2.27 2.73c.15.17.36.27.58.27h9q.25.11.25.25v12q0 .14-.25.25h-18q-.25-.11-.25-.25V5q0-.14.25-.25h6.15zm5.826 8.5c.41 0 .75.34.75.75s-.34.75-.75.75h-6c-.41 0-.75-.34-.75-.75s.34-.75.75-.75h6z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Folder(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={folderIcon} />;
}

Folder.displayName = 'EchFolderIcon';

export default Folder;
