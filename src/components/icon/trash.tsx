import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const trashIcon: IconComponentProps['icon'] = {
  name: 'trash',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M7.118009912872314,5.442239977874756L5.747659912872314,5.442239977874756C5.742629912872315,5.442509977874756,5.737609912872315,5.442829977874756,5.732599912872315,5.443199977874755L3.399359912872314,5.443199977874755C2.9993599128723143,5.443199977874755,2.6793599128723145,5.763199977874756,2.6793599128723145,6.163199977874756C2.6793599128723145,6.563199977874756,2.9993599128723143,6.883199977874756,3.399359912872314,6.883199977874756L5.120849912872314,6.883199977874756L5.977659912872314,20.192199977874758C6.037659912872314,21.072199977874757,6.767659912872315,21.762199977874754,7.657659912872314,21.762199977874754L16.427659912872315,21.762199977874754C17.307659912872314,21.762199977874754,18.047659912872312,21.072199977874757,18.097659912872317,20.192199977874758L18.963959912872316,6.883199977874756L20.679359912872314,6.883199977874756C21.079359912872313,6.883199977874756,21.399359912872313,6.563199977874756,21.399359912872313,6.163199977874756C21.399359912872313,5.763199977874756,21.079359912872313,5.443199977874755,20.679359912872314,5.443199977874755L18.323759912872312,5.443199977874755C18.311759912872315,5.442569977874756,18.299759912872315,5.442239977874756,18.287659912872314,5.442239977874756L16.971459912872312,5.442239977874756L16.634759912872312,3.4831999778747558C16.494759912872315,2.673201977874756,15.794759912872314,2.083199977874756,14.984759912872315,2.083199977874756L9.104719912872316,2.083199977874756C8.284719912872315,2.083199977874756,7.594719912872314,2.673201977874756,7.4547199128723145,3.4831999778747558L7.118009912872314,5.442239977874756ZM15.510159912872314,5.442239977874756L8.579259912872313,5.442239977874756L8.874719912872315,3.7231999778747555C8.894719912872315,3.603199977874756,8.994719912872315,3.523199977874756,9.104719912872316,3.523199977874756L14.984759912872315,3.523199977874756C15.094759912872314,3.523199977874756,15.194759912872314,3.603199977874756,15.214759912872314,3.7231999778747555L15.510159912872314,5.442239977874756ZM17.517759912872314,6.883199977874756L6.5577599128723145,6.883199977874756L7.407759912872314,20.103199977874755C7.417759912872315,20.223199977874756,7.527759912872314,20.323199977874754,7.657759912872314,20.323199977874754L16.417759912872313,20.323199977874754C16.547759912872316,20.323199977874754,16.657759912872315,20.223199977874756,16.657759912872315,20.103199977874755L17.517759912872314,6.883199977874756ZM15.159359912872315,17.203199977874753C15.159359912872315,16.803199977874755,14.839359912872315,16.483199977874754,14.439359912872314,16.483199977874754L9.639359912872315,16.483199977874754C9.239359912872313,16.483199977874754,8.919359912872315,16.803199977874755,8.919359912872315,17.203199977874753C8.919359912872315,17.603199977874755,9.239359912872313,17.923199977874756,9.639359912872315,17.923199977874756L14.439359912872314,17.923199977874756C14.839359912872315,17.923199977874756,15.159359912872315,17.603199977874755,15.159359912872315,17.203199977874753Z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Trash(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={trashIcon} />;
}

Trash.displayName = 'EchTrashIcon';

export default Trash;
