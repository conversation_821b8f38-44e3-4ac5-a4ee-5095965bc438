import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const uploadIcon: IconComponentProps['icon'] = {
  name: 'upload',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M2 20.97c0 .57.46 1.03 1.03 1.03h17.94c.57 0 1.03-.46 1.03-1.03V12c0-.21-.08-.4-.23-.54a.725.725 0 0 0-.54-.23c-.2 0-.4.08-.54.23-.15.14-.23.33-.23.54v8.46H3.54V12c0-.42-.35-.77-.77-.77-.42 0-.77.35-.77.77v8.97z',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M11.467 16.777a.75.75 0 0 0 1.28-.53V5.057l3.22 3.22c.29.29.77.29 1.06 0 .29-.29.29-.77 0-1.06l-4.28-4.28-.44-.44a.44.44 0 0 0-.62 0l-.44.44-4.28 4.28c-.29.29-.29.77 0 1.06.29.29.77.29 1.06 0l3.22-3.22v11.19c0 .2.08.39.22.53z',
        },
      },
    ],
  },
};

function Upload(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={uploadIcon} />;
}

Upload.displayName = 'EchUploadIcon';

export default Upload;
