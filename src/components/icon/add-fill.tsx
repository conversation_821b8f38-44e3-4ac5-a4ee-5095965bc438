import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const addFillIcon: IconComponentProps['icon'] = {
  name: 'addFill',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 17 17', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M8.33333,16.6667C3.73083,16.6667,0,12.9358,0,8.33333C0,3.73083,3.73083,0,8.33333,0C12.9358,0,16.6667,3.73083,16.6667,8.33333C16.6667,12.9358,12.9358,16.6667,8.33333,16.6667ZM7.5,7.5L4.16667,7.5L4.16667,9.16667L7.5,9.16667L7.5,12.5L9.16667,12.5L9.16667,9.16667L12.5,9.16667L12.5,7.5L9.16667,7.5L9.16667,4.16667L7.5,4.16667L7.5,7.5Z',
        },
      },
    ],
  },
};

function AddFill(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={addFillIcon} />;
}

AddFill.displayName = 'EchAddFillIcon';

export default AddFill;
