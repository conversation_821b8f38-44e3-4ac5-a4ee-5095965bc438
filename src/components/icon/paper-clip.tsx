import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const paperClipIcon: IconComponentProps['icon'] = {
  name: 'paper-clip',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M12.813999891281128,5.824896030426025C13.083999891281128,5.5548960304260255,13.083999891281128,5.114896030426025,12.813999891281128,4.844896030426026C12.683999891281127,4.714896030426026,12.513999891281127,4.644896030426025,12.323999891281128,4.644896030426025C12.143999891281128,4.644896030426025,11.973999891281128,4.714896030426026,11.843999891281127,4.844896030426026L4.983999891281128,11.704896030426024C4.073999891281128,12.614896030426026,3.563999891281128,13.844896030426025,3.563999891281128,15.124896030426026C3.563999891281128,17.804896030426026,5.733999891281128,19.974896030426024,8.413999891281128,19.974896030426024C9.693999891281127,19.974896030426024,10.923999891281127,19.454896030426028,11.833999891281128,18.544896030426024L19.993999891281128,10.394896030426025Q20.103999891281127,10.274896030426024,20.16399989128113,10.214896030426026C20.79399989128113,9.544896030426026,21.123999891281127,8.654896030426025,21.09399989128113,7.734896030426025C21.053999891281126,6.814896030426025,20.66399989128113,5.944896030426025,19.993999891281128,5.324896030426025C19.313999891281128,4.694896030426025,18.423999891281127,4.364896030426025,17.503999891281126,4.394896030426025C16.59399989128113,4.424896030426026,15.723999891281128,4.824896030426025,15.093999891281127,5.494896030426025L6.933999891281128,13.654896030426025C6.553999891281128,14.044896030426026,6.3339998912811275,14.574896030426025,6.3339998912811275,15.124896030426026C6.3339998912811275,16.264896030426023,7.263999891281128,17.204896030426028,8.403999891281128,17.204896030426028C8.963999891281128,17.204896030426028,9.483999891281128,16.984896030426025,9.873999891281127,16.594896030426025L16.73399989128113,9.734896030426025L15.753999891281127,8.764896030426026L8.893999891281128,15.614896030426026C8.633999891281128,15.864896030426026,8.213999891281128,15.864896030426026,7.953999891281128,15.614896030426026C7.813999891281128,15.484896030426025,7.733999891281128,15.314896030426025,7.733999891281128,15.134896030426026C7.723999891281128,14.944896030426026,7.793999891281128,14.764896030426025,7.913999891281128,14.634896030426026L16.073999891281126,6.4748960304260255C16.463999891281127,6.084896030426025,16.993999891281128,5.864896030426025,17.54399989128113,5.864896030426025C18.09399989128113,5.864896030426025,18.62399989128113,6.084896030426025,19.013999891281127,6.4748960304260255C19.82399989128113,7.284896030426026,19.82399989128113,8.604896030426026,19.013999891281127,9.414896030426025L10.853999891281127,17.574896030426025C10.803999891281128,17.634896030426027,10.743999891281128,17.694896030426026,10.683999891281129,17.744896030426027C9.283999891281127,19.044896030426024,7.093999891281127,18.974896030426024,5.793999891281128,17.574896030426025C5.163999891281128,16.904896030426023,4.8339998912811275,16.004896030426025,4.863999891281128,15.094896030426025C4.893999891281128,14.174896030426025,5.293999891281128,13.304896030426026,5.963999891281128,12.684896030426025L12.813999891281128,5.824896030426025Z',
        },
      },
    ],
  },
};

function PaperClip(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={paperClipIcon} />;
}

PaperClip.displayName = 'EchPaperClipIcon';

export default PaperClip;
