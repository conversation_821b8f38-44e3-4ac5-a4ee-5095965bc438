import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const closeIcon: IconComponentProps['icon'] = {
  name: 'close',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M12.008 13.14 6.436 18.71a.817.817 0 0 1-1.16 0 .809.809 0 0 1 .007-1.152l5.572-5.572-5.559-5.559a.817.817 0 0 1 0-1.16.809.809 0 0 1 1.153.008l5.559 5.559 5.55-5.552a.809.809 0 0 1 1.153-.007.817.817 0 0 1 0 1.16l-5.55 5.551 5.563 5.564a.809.809 0 0 1 .007 1.153.817.817 0 0 1-1.16 0l-5.563-5.564Z',
        },
      },
    ],
  },
};

function Close(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={closeIcon} />;
}

Close.displayName = 'EchCloseIcon';

export default Close;
