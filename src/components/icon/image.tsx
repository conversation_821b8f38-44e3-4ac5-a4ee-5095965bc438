import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const imageIcon: IconComponentProps['icon'] = {
  name: 'image',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M18.543 14.164v-10a1.46 1.46 0 0 0-1.459-1.458H2.918c-.809 0-1.459.65-1.459 1.458v11.667a1.46 1.46 0 0 0 1.459 1.458h14.166a1.454 1.454 0 0 0 1.458-1.458v-1.667zm-1.25-1.27v-8.73q0-.117-.209-.208H2.918q-.209.091-.209.208v8.492l3.1-3.1a.635.635 0 0 1 .834-.05l1.608 1.292 2.108-2.459a.633.633 0 0 1 .859-.091l6.075 4.645zm0 1.575-6.367-4.867-2.117 2.467a.627.627 0 0 1-.866.083l-1.65-1.316-3.584 3.583v1.408q0 .117.209.209h14.166q.209-.092.209-.209V14.47zm-10-7.593c0-.692-.559-1.25-1.25-1.25-.692 0-1.25.558-1.25 1.25 0 .691.558 1.25 1.25 1.25.691 0 1.25-.559 1.25-1.25z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Image(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={imageIcon} />;
}

Image.displayName = 'EchImageIcon';

export default Image;
