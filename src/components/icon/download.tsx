import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const downloadIcon: IconComponentProps['icon'] = {
  name: 'download',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'm12.75 14.19 3.22-3.22c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-4.28 4.28-.44.44a.44.44 0 0 1-.62 0l-.44-.44-4.28-4.28a.754.754 0 0 1 0-1.06c.29-.29.77-.29 1.06 0l2.86 2.86.36.36V3a.75.75 0 0 1 1.5 0v11.19zm-10.5 6.56c0 .55.45 1 1 1h17.5c.55 0 1-.45 1-1V12a.75.75 0 0 0-1.5 0v8.25H3.75V12c0-.41-.34-.75-.75-.75s-.75.34-.75.75v8.75z',
        },
      },
    ],
  },
};

function Download(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={downloadIcon} />;
}

Download.displayName = 'EchDownloadIcon';

export default Download;
