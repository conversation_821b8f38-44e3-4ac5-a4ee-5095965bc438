import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const questionIcon: IconComponentProps['icon'] = {
  name: 'question',
  theme: 'filled',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M11.99 2c-5.522 0-10 4.477-10 10s4.478 10 10 10c5.523 0 10-4.477 10-10s-4.477-10-10-10Zm.63 14.91c-.199.188-.464.292-.738.29a1.006 1.006 0 0 1-.74-.303.994.994 0 0 1-.303-.727.933.933 0 0 1 .303-.728c.196-.194.464-.3.74-.29.28-.011.551.094.751.29a.934.934 0 0 1 .303.728c.004.283-.11.555-.315.751v-.012Zm2.062-5.577c-.383.395-.788.767-1.213 1.115-.249.201-.451.453-.594.74-.156.29-.236.616-.23.945v.243H11.13v-.243c-.017-.47.083-.936.29-1.357a7.369 7.369 0 0 1 1.6-1.794l.243-.267c.248-.292.39-.66.4-1.042a1.648 1.648 0 0 0-.46-1.115c-.33-.3-.768-.453-1.212-.425a1.588 1.588 0 0 0-1.394.619c-.28.43-.417.94-.388 1.454H8.754c-.04-.896.283-1.77.897-2.424a3.333 3.333 0 0 1 2.485-.91c.82-.07 1.636.178 2.279.692.584.516.9 1.27.86 2.048a2.764 2.764 0 0 1-.593 1.733v-.012Z',
        },
      },
    ],
  },
};

function Question(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={questionIcon} />;
}

Question.displayName = 'EchQuestionIcon';

export default Question;
