import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const calendarIcon: IconComponentProps['icon'] = {
  name: 'calendar',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M5.208 2.5a.625.625 0 0 1 1.25 0v1.042h7.084V2.5a.625.625 0 1 1 1.25 0v1.042H17.5q.603 0 1.03.428.428.427.428 1.03v10.833q0 .603-.427 1.03-.428.429-1.031.429h-15q-.603 0-1.03-.428-.428-.428-.428-1.03V5q0-.603.428-1.03.427-.428 1.03-.428h2.708V2.5zm0 2.292H2.5q-.208 0-.208.208v10.833q0 .209.208.209h15q.208 0 .208-.209V5q0-.208-.208-.208h-2.708v1.041a.625.625 0 1 1-1.25 0V4.792H6.458v1.041a.625.625 0 1 1-1.25 0V4.792zm.625 4.166h4.584a.625.625 0 0 1 0 1.25H5.833a.625.625 0 0 1 0-1.25zm0 3.334h8.334a.625.625 0 0 1 0 1.25H5.833a.625.625 0 1 1 0-1.25z',
        },
      },
    ],
  },
};

function Calendar(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={calendarIcon} />;
}

Calendar.displayName = 'EchCalendarIcon';

export default Calendar;
