import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const pendIcon: IconComponentProps['icon'] = {
  name: 'pend',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 15 15', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M14.8465,1.87759Q14.9147,2.03184,14.9573,2.23751Q15,2.44318,15,2.66598Q15,2.88879,14.9147,3.1116Q14.8294,3.33441,14.6417,3.54008Q14.437,3.74575,14.2749,3.9Q14.1129,4.05425,13.9934,4.17422Q13.857,4.31134,13.7375,4.41417L10.5472,1.20916Q10.752,1.02063,11.0335,0.754977Q11.315,0.489321,11.5026,0.335069Q11.7415,0.146539,11.9974,0.0694133Q12.2533,-0.00771259,12.5007,0.000856953Q12.748,0.0094265,12.9783,0.0779828Q13.2087,0.146539,13.3793,0.232235Q13.7375,0.420764,14.1726,0.892089Q14.6076,1.36341,14.8465,1.87759ZM1.55643,10.2415Q1.65879,10.1386,2.03412,9.75299Q2.40945,9.36736,2.97244,8.80177L4.21785,7.55062L5.59974,6.16236L9.26772,2.47745L12.458,5.6996L8.79003,9.3845L7.4252,10.7728Q6.74278,11.4412,6.19685,11.9982Q5.65092,12.5552,5.29265,12.9152Q4.93438,13.2751,4.86614,13.3265Q4.69554,13.4807,4.47375,13.6521Q4.25197,13.8235,4.01312,13.9264Q3.77428,14.0463,3.31365,14.2177Q2.85302,14.3891,2.34974,14.5519Q1.84646,14.7148,1.39436,14.8347Q0.942257,14.9547,0.720472,14.989Q0.259843,15.0404,0.106299,14.8519Q-0.0472441,14.6633,0.0209973,14.2177Q0.0551181,13.9778,0.183071,13.5236Q0.311024,13.0694,0.456037,12.5809Q0.60105,12.0925,0.737533,11.6811Q0.874016,11.2698,0.959318,11.1156Q1.06168,10.8756,1.18963,10.6785Q1.31759,10.4814,1.55643,10.2415Z',
        },
      },
    ],
  },
};

function Pend(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={pendIcon} />;
}

Pend.displayName = 'EchPendIcon';

export default Pend;
