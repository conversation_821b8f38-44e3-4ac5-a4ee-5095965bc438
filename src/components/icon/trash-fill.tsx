import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const trashFillIcon: IconComponentProps['icon'] = {
  name: 'trashFill',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 17 17', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M15.3125,3.125L0.9375,3.125C0.419733,3.125,0,2.70527,0,2.1875C0,1.66973,0.419733,1.25,0.9375,1.25L5.625,1.25C5.625,0.559644,6.18464,0,6.875,0L9.375,0C10.0654,0,10.625,0.559644,10.625,1.25L15.3125,1.25C15.8303,1.25,16.25,1.66973,16.25,2.1875C16.25,2.70527,15.8303,3.125,15.3125,3.125ZM14.375,15C14.375,16.3807,13.2557,17.5,11.875,17.5L4.375,17.5C2.99429,17.5,1.875,16.3807,1.875,15L1.875,4.375L14.375,4.375L14.375,15ZM6.875,7.8125C6.875,7.29473,6.45527,6.875,5.9375,6.875C5.41973,6.875,5,7.29473,5,7.8125L5,13.4375C5,13.9553,5.41973,14.375,5.9375,14.375C6.45527,14.375,6.875,13.9553,6.875,13.4375L6.875,7.8125ZM11.25,7.8125C11.25,7.29473,10.8303,6.875,10.3125,6.875C9.79473,6.875,9.375,7.29473,9.375,7.8125L9.375,13.4375C9.375,13.9553,9.79473,14.375,10.3125,14.375C10.8303,14.375,11.25,13.9553,11.25,13.4375L11.25,7.8125ZZ',
        },
      },
    ],
  },
};

function TrashFill(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={trashFillIcon} />;
}

TrashFill.displayName = 'EchTrashFillIcon';

export default TrashFill;
