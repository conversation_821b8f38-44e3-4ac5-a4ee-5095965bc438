import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const downIcon: IconComponentProps['icon'] = {
  name: 'down',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M18.478 10.28c.29-.29.29-.77 0-1.06a.754.754 0 0 0-1.06 0l-5.47 5.47-5.47-5.47a.75.75 0 1 0-1.06 1.06l6 6a.75.75 0 0 0 1.06 0l6-6Z',
        },
      },
    ],
  },
};

function Down(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={downIcon} />;
}

Down.displayName = 'EchDownIcon';

export default Down;
