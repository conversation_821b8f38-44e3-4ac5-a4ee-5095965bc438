import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const csIcon: IconComponentProps['icon'] = {
  name: 'cs',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M18.751 9.588v6.324c1.43-.34 2.5-1.63 2.5-3.162a3.259 3.259 0 0 0-2.5-3.162zm-.035 7.858A6.748 6.748 0 0 1 12 23.5c-.41 0-.75-.34-.75-.75s.34-.75.75-.75c2.9 0 5.25-2.35 5.25-5.25v-8c0-2.9-2.35-5.25-5.25-5.25-1.39 0-2.73.55-3.71 1.54a5.22 5.22 0 0 0-1.54 3.71v8c0 .41-.34.75-.75.75a4.753 4.753 0 0 1-3.36-8.11 4.75 4.75 0 0 1 2.645-1.336A6.748 6.748 0 0 1 12.001 2a6.748 6.748 0 0 1 6.715 6.054 4.756 4.756 0 0 1 4.035 4.696 4.756 4.756 0 0 1-4.035 4.696zM5.25 15.913V9.587a3.252 3.252 0 0 0 0 6.326z',
        },
      },
    ],
  },
};

function Cs(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={csIcon} />;
}

Cs.displayName = 'EchCsIcon';

export default Cs;
