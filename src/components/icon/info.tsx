import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const infoIcon: IconComponentProps['icon'] = {
  name: 'info',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M1,12C1,18.07,5.93,23,12,23C18.07,23,23,18.07,23,12C23,9.08,21.84,6.28,19.78,4.220000000000001C17.72,2.16,14.92,1,12,1C5.93,1,1,5.93,1,12ZM21.35,12C21.35,6.84,17.16,2.65,12,2.65C6.84,2.65,2.65,6.84,2.65,12C2.65,17.16,6.84,21.35,12,21.35C17.16,21.35,21.35,17.16,21.35,12ZM10.95225,10.59163C11.1123,10.59163,11.2822,10.75163,11.1423,11.0816L9.80225,14.2816C9.39225,14.9616,9.12225,15.7316,9.02225,16.5216C8.972249999999999,17.2316,9.48225,17.8516,10.18225,17.9516C11.9222,17.7316,13.4622,16.7016,14.3422,15.1716C14.3422,15.0316,14.2423,14.9216,14.1023,14.9016C13.8422,14.9016,12.7922,16.4416,12.2222,16.4416C12.1523,16.4416,12.1023,16.4116,12.0522,16.3616Q11.9923,16.2516,12.0022,16.1916C12.0522,15.8316,12.1623,15.4916,12.3223,15.1716L13.6922,11.6216C14.3822,9.86163,13.5023,9.39163,12.6923,9.39163C11.6222,9.43163,10.60225,9.86163,9.83225,10.61163C9.43225,11.0416,9.14225,11.5516,8.972249999999999,12.1116C8.98225,12.2516,9.09225,12.3516,9.22225,12.3616C9.57225,12.3616,10.25225,10.59163,10.95225,10.59163ZM12.1626,7.50093C12.1526,7.84093,12.2826,8.17093,12.5326,8.41093C12.7826,8.650929999999999,13.1126,8.77093,13.4526,8.740929999999999C13.8626,8.75093,14.2526,8.61093,14.5526,8.34093C14.8426,8.07093,15.0226,7.69093,15.0326,7.28093C15.0426,6.94093,14.9026,6.61093,14.6526,6.37093C14.4026,6.14093,14.0626,6.02093,13.7226,6.04093C12.8926,6.03093,12.2026,6.67093,12.1626,7.50093Z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Info(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={infoIcon} />;
}

Info.displayName = 'EchInfoIcon';

export default Info;
