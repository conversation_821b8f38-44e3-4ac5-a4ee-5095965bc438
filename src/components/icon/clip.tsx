import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const clipIcon: IconComponentProps['icon'] = {
  name: 'clip',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M6.76 17.198A4.373 4.373 0 0 1 3.665 9.73l6.187-6.188a.625.625 0 0 1 .884.885L4.55 10.615a3.125 3.125 0 1 0 4.417 4.417l7.365-7.366a1.875 1.875 0 1 0-2.651-2.652L6.315 12.38a.626.626 0 0 0 .884.885l6.187-6.187.884.884-6.187 6.187a1.876 1.876 0 0 1-2.652-2.652l7.366-7.365a3.125 3.125 0 1 1 4.416 4.417l-7.366 7.365a4.364 4.364 0 0 1-3.088 1.285z',
        },
      },
    ],
  },
};

function Clip(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={clipIcon} />;
}

Clip.displayName = 'EchClipIcon';

export default Clip;
