import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const childFormIcon: IconComponentProps['icon'] = {
  name: 'childForm',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'm12.742 1.42 4.072 3.703q.478.434.478 1.079V17.5q0 .604-.427 1.031-.428.427-1.032.427H4.167q-.604 0-1.032-.427-.427-.427-.427-1.031v-15q0-.604.427-1.031.428-.427 1.032-.427h7.594q.564 0 .981.379zm-.84.926q-.06-.054-.14-.054H4.166q-.209 0-.209.208v15q0 .086.061.147t.148.061h11.666q.209 0 .209-.208V6.202q0-.092-.069-.154l-4.072-3.702z',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M16.667 6.875H12.5q-.604 0-1.031-.427-.427-.427-.427-1.031v-3.75h1.25v3.75q0 .086.06.147.062.061.148.061h4.167v1.25z',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M15.245 10.208h-5v-1.25h5v1.25z',
          transform: 'rotate(90 10.245 9.583)',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M16.286 11.875h-3.333v-1.25h3.333v1.25z',
          transform: 'rotate(90 12.953 11.25)',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M10.219 12.083H7.094v-1.25h3.125v1.25z',
          transform: 'rotate(90 7.094 11.458)',
        },
      },
    ],
  },
};

function ChildForm(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={childFormIcon} />;
}

ChildForm.displayName = 'EchChildFormIcon';

export default ChildForm;
