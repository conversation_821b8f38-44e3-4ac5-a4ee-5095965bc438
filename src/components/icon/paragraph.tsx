import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const paragraphIcon: IconComponentProps['icon'] = {
  name: 'paragraph',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M9.395 14.247h1.265l-1.362-3.992-2.087-6.922H5.828L2.5 14.207l.1.04H3.79l.974-3.182H8.31l1.085 3.182zm-1.49-4.465H5.156L6.541 5.26l1.363 4.522z',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M2.631 15.705h15',
          stroke: 'currentColor',
        },
      },
      {
        tag: 'path',
        attrs: {
          d: 'M17.63 13.205h-5.833v-1.25h5.834v1.25zM17.63 9.872h-5.833v-1.25h5.834v1.25zM17.63 6.539h-5.833v-1.25h5.834v1.25z',
        },
      },
    ],
  },
};

function Paragraph(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={paragraphIcon} />;
}

Paragraph.displayName = 'EchParagraphIcon';

export default Paragraph;
