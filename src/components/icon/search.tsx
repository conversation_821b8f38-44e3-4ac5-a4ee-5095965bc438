import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const searchIcon: IconComponentProps['icon'] = {
  name: 'search',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 14 14', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M7.63302,11.1611C7.58635,10.8987,7.32636,10.726,7.05304,10.7708C5.43311,11.0459,3.77318,10.566,2.58656,9.47161C0.533311,7.58368,0.459981,4.45421,2.42657,2.4767C4.39982,0.505579,7.65968,0.441582,9.71293,2.3295C11.7662,4.22382,11.8328,7.35329,9.86626,9.32441L9.79292,9.39481C9.5996,9.5804,9.5996,9.88119,9.79292,10.0732L12.6861,12.8507C12.7795,12.9403,12.9128,12.9915,13.0395,12.9915C13.1728,12.9915,13.2994,12.9467,13.3994,12.8507C13.4928,12.7611,13.5461,12.6395,13.5461,12.5115C13.5461,12.3835,13.4928,12.2619,13.3994,12.1723L10.8395,9.7212C12.0262,8.37725,12.5261,6.59813,12.2062,4.8702C11.6062,1.65753,8.41298,-0.47998,5.06646,0.0895963C1.72659,0.665572,-0.499979,3.73104,0.0933294,6.93731C0.693304,10.15,3.8865,12.2875,7.22637,11.7115C7.49969,11.6667,7.67968,11.4171,7.63302,11.1611Z',
        },
      },
    ],
  },
};

function Search(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={searchIcon} />;
}

Search.displayName = 'EchRightIcon';

export default Search;
