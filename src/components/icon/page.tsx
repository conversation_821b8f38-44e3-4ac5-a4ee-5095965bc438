import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const pageIcon: IconComponentProps['icon'] = {
  name: 'page',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 18 18', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M15.3,18L2.7,18C1.20883,18,0,16.7912,0,15.3L0,0.9C0,0.402944,0.402944,0,0.9,0L13.5,0C13.9971,0,14.4,0.402944,14.4,0.9L14.4,11.7L18,11.7L18,15.3C18,16.7912,16.7912,18,15.3,18ZM14.4,13.5L14.4,15.3C14.4,15.7971,14.8029,16.2,15.3,16.2C15.7971,16.2,16.2,15.7971,16.2,15.3L16.2,13.5L14.4,13.5ZM3.6,5.4C3.6,5.89706,4.00294,6.3,4.5,6.3L9.9,6.3C10.3971,6.3,10.8,5.89706,10.8,5.4C10.8,4.90294,10.3971,4.5,9.9,4.5L4.5,4.5C4.00294,4.5,3.6,4.90294,3.6,5.4M3.6,9C3.6,9.49706,4.00294,9.9,4.5,9.9L9.9,9.9C10.3971,9.9,10.8,9.49706,10.8,9C10.8,8.50294,10.3971,8.1,9.9,8.1L4.5,8.1C4.00294,8.1,3.6,8.50294,3.6,9M3.6,12.6C3.6,13.0971,4.00294,13.5,4.5,13.5L7.2,13.5C7.69706,13.5,8.1,13.0971,8.1,12.6C8.1,12.1029,7.69706,11.7,7.2,11.7L4.5,11.7C4.00294,11.7,3.6,12.1029,3.6,12.6',
        },
      },
    ],
  },
};

function Page(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={pageIcon} />;
}

Page.displayName = 'EchPageIcon';

export default Page;
