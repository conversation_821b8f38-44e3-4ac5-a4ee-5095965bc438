import { HTMLAttributes, MouseEventHandler } from 'react';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import classNames from 'classnames';
import styles from './index.module.less';

export interface IconProps extends HTMLAttributes<HTMLElement> {
  name: string;
  size?: number | string;
  color?: string;

  onClick?: MouseEventHandler;
}

function Icon({
  name,
  size,
  color,
  className,
  style,

  ...others
}: IconProps) {
  const iStyle = {
    color,
    fontSize: typeof size === 'string' ? size : `${size}px`,
    ...style,
  };

  if (name === 'loading') {
    return <LoadingOutlined className={className} style={iStyle} {...others} />;
  }
  const classes = classNames(styles.icon, styles[name], className);
  return <i className={classes} style={iStyle} {...others} />;
}

Icon.defaultProps = {
  size: '',
  color: '',
  onClick: undefined,
};

export default Icon;
