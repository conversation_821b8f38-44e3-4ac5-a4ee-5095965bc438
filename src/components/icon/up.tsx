import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const upIcon: IconComponentProps['icon'] = {
  name: 'up',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 15 15', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M14.3983,8.62975L10.2169,8.62975C10.4663,8.98112,10.5795,9.28237,10.5573,9.53425C10.554,9.5695,10.5482,9.60413,10.5399,9.63725C10.4674,9.923,10.2281,10.058,9.82187,10.0421C9.57525,10.0421,9.256,10.0501,8.86438,10.0661C8.47275,10.0816,8.14638,10.0896,7.88525,10.0896C7.59525,10.1058,7.4175,10.205,7.352,10.3875C7.322,10.4721,7.29862,10.5711,7.28238,10.6848L14.3983,10.6848C14.7305,10.6848,15,10.3898,15,10.0255L15,9.289C15,8.92512,14.7305,8.62975,14.3983,8.62975ZM14.3983,12.9446L7.25688,12.9449C7.25875,13.1665,7.2615,13.39,7.265,13.6155C7.2695,13.8934,7.27238,14.1676,7.27413,14.4391C7.27513,14.6026,7.27588,14.765,7.27588,14.9259L7.27588,15L14.3983,15C14.7305,15,15,14.7049,15,14.3406L15,13.6044C15,13.2401,14.7305,12.9446,14.3983,12.9446ZM14.3983,0L0.6015,0C0.269375,0,0,0.295125,0,0.659375L0,1.39575C0,1.76,0.269375,2.05512,0.6015,2.05512L14.3983,2.05512C14.7305,2.05512,15,1.75987,15,1.39575L15,0.659375C15,0.295125,14.7305,0,14.3983,0ZM14.3983,4.315L7.25563,4.315L7.46975,4.62975C7.66325,4.91463,7.85962,5.206,8.059,5.50437L8.64213,6.37025L14.3981,6.37025C14.7304,6.37025,14.9999,6.07487,14.9999,5.71075L14.9999,4.97438C15,4.61025,14.7305,4.315,14.3983,4.315ZM7.67262,8.35213C7.72525,8.14487,7.636,7.88575,7.40475,7.57463C7.19462,7.28688,6.95562,6.94713,6.68775,6.55562C6.41987,6.164,6.14925,5.764,5.87612,5.35512C5.60287,4.94637,5.33775,4.55475,5.08037,4.18062C4.823,3.80638,4.5945,3.48675,4.395,3.222C4.19537,2.96862,4.00875,2.84763,3.8355,2.85925C3.66212,2.87075,3.481,3.00888,3.29175,3.27375C3.0815,3.56163,2.84262,3.90413,2.57475,4.3015C2.30688,4.69875,2.03113,5.1075,1.7475,5.528C1.46375,5.94825,1.19063,6.35413,0.928,6.74563C0.665375,7.13713,0.43425,7.47113,0.234625,7.74763C0.0455004,8.01225,-0.00962466,8.22838,0.0691253,8.39525C0.147875,8.56225,0.32925,8.64575,0.612875,8.64575C0.791375,8.64575,0.993625,8.65175,1.21963,8.66325C1.4455,8.6745,1.63725,8.6805,1.79488,8.6805C2.0155,8.669,2.15725,8.76388,2.22038,8.96538C2.28338,9.16687,2.31475,9.44038,2.31475,9.78588L2.31475,10.5976C2.31475,10.8971,2.3175,12.044,2.32275,12.3435C2.328,12.643,2.33063,12.925,2.33063,13.1899L2.33063,13.8637C2.33063,14.2552,2.391,14.5402,2.51188,14.7188C2.63275,14.8971,2.86638,14.9865,3.21313,14.9865C3.46525,14.9865,3.68325,14.9836,3.86713,14.9779C4.051,14.972,4.25325,14.9694,4.47388,14.9694C4.663,14.9694,4.81263,14.9347,4.923,14.8656C5.03325,14.7965,5.11738,14.7102,5.17513,14.6065C5.23288,14.503,5.26963,14.3877,5.28538,14.261C5.30113,14.1344,5.309,14.0135,5.309,13.8985L5.309,13.0346C5.309,12.7239,5.30638,12.4071,5.30113,12.0848C5.29588,11.7622,5.29325,10.5979,5.29325,10.2871L5.29325,9.44062C5.29325,9.21037,5.31688,9.02913,5.364,8.8965C5.41125,8.764,5.54,8.69213,5.75013,8.68062C5.93913,8.68062,6.1755,8.67463,6.45925,8.66338C6.74288,8.65188,6.974,8.64588,7.15263,8.64588C7.44675,8.65738,7.62012,8.55937,7.67262,8.35213Z',
        },
      },
    ],
  },
};

function Up(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={upIcon} />;
}

Up.displayName = 'EchUpIcon';

export default Up;
