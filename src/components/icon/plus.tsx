import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const plusIcon: IconComponentProps['icon'] = {
  name: 'plus',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 1024 1024', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M546.602667 546.133333v336.213334c0 19.626667-15.786667 34.986667-34.986667 34.986666-19.2 0-34.56-15.36-34.56-34.986666V546.133333H141.653333c-19.626667 0-34.986667-15.786667-34.986666-34.986666 0-19.2 15.36-34.56 34.986666-34.56h335.402667V141.653333c0-19.626667 15.36-34.986667 34.56-34.986666 19.2 0 34.986667 15.36 34.986667 34.986666v334.933334h335.744c19.626667 0 34.986667 15.36 34.986666 34.56 0 19.2-15.36 34.986667-34.986666 34.986666h-335.744z',
        },
      },
    ],
  },
};

function Plus(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={plusIcon} />;
}

Plus.displayName = 'EchPlusIcon';

export default Plus;
