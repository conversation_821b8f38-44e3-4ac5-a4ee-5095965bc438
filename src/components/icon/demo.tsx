import { CSSProperties } from 'react';
import Icon from './index';

const iconNames = [
  'shangchuan',
  'gif',
  'praise',
  'close',
  'shop-bags',
  'separate',
  'download-outline',
  'zu14895',
  'tick-solid',
  'round',
  'warn-sold',
  'eye-open',
  'eye-close',
  'card',
  'c231',
  'tag',
  'b171',
  'more-sold',
  'zu16937',
  'zu16934',
  'play',
  'remove-user',
  'config-tree',
  'location-outline',
  'at',
  'plane',
  'unlock',
  'lock',
  'users',
  'user-briefcase',
  'd1',
  'stopwatch',
  'close-solid',
  'blob',
  'edit',
  'tick',
  'zoom-solid',
  'center',
  'table',
  'palette',
  'align-right',
  'align-left',
  'switch',
  'volume-outline',
  'emoji',
  'keyboard',
  'cart',
  'tick-outline',
  'star-solid',
  'ad',
  'bags-acitved',
  'c181',
  'withdraw',
  'selected',
  'double-up',
  'bags',
  'location-solid',
  'briefcase',
  'call',
  'mansion',
  'wallet',
  'daatabase',
  'rmb',
  'purse',
  'up-down',
  'down-solid',
  'info',
  'must',
  'printer',
  'brush',
  'more-circle',
  'left-arrow-circle',
  'plus-circle',
  'close-circle',
  'restore',
  'message-circle',
  'share-circle',
  'telephone-circle',
  'flash-outline',
  'migration',
  'flash-circle',
  'minus',
  'question',
  'member',
  'scan',
  'member-cricle',
  'cart-circle',
  'envelope',
  'display-list',
  'gift',
  'lower-left-corner',
  'add-emoji',
  'zoom',
  'clock',
  'close-outline',
  'male',
  'female',
  'pen',
  'lineation',
  'irregular',
  'more-outline',
  'switch-user',
  'date-circle',
  'briefcase-circle',
  'location-circle',
  'time-circle',
  'slash',
  'text',
  'control',
  'bar-chart',
  'pause',
  'down-circle',
  'bell-solid',
  'meeting-room',
  'more',
  'rubber',
  'lens',
  'xinzengricheng',
  'meeting',
  'crm',
  'loop',
  'ol',
  'title',
  'contract',
  'business-card',
  'close-circle2',
  'wifi',
  'appoint',
  'claim',
  'face-verification',
  'attendance-machine',
  'safe',
  'clock-in-fill',
  'statistics-fill',
  'setup-fill',
  'clock-in',
  'statistics',
  'apply',
  'setup',
  'apply-fill',
  'community',
  'telephone',
  'user-fill',
  'backup',
  'flag-fill',
  'bar-chart-outline',
  'customer',
  'eaa',
  'cc',
  'complete',
  'scan-outline',
  'well-number',
  'plus',
  'add-user',
  'clip',
  'message',
  'shop',
  'zu13366',
  'start-outline',
  'address-book',
  'filter',
  'phone',
  'copy',
  'view',
  'image',
  'link',
  'tree',
  'zu13360',
  'location',
  'trash',
  'folder',
  'bell',
  'todo-list',
  'share-outline',
  'camera',
  'upload-outline',
  'reset',
  'search',
  'mute',
  'refund',
  'regular',
  'list',
  'zu13416',
  'info-outline',
  'share',
  'plus-outline',
  'refresh',
  'left',
  'right',
  'zu13401',
  'mansion-outline',
  'upload',
  'edit1',
  'download',
  'broadcast',
  'close-outline1',
  'down',
  'up',
  'setting',
  'search-tag',
  'calendar-outline',
];

const listStyle: CSSProperties = {
  display: 'flex',
  flexWrap: 'wrap',
  width: '100%',
  height: '100%',
};

const itemStyle: CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '20px',
};

function IcomView() {
  return (
    <div style={listStyle}>
      {iconNames.map((iconName) => (
        <div style={itemStyle}>
          <Icon name={iconName} style={{ fontSize: '25px' }} />
          <span>{iconName}</span>
        </div>
      ))}
    </div>
  );
}

export default IcomView;
