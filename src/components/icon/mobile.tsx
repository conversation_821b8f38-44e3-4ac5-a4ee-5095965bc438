import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const mobileIcon: IconComponentProps['icon'] = {
  name: 'mobile',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 24 24', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M19.150153999328616,19.84692313194275L19.150153999328616,4.156923131942749C19.150153999328616,3.006923131942749,18.220153999328613,2.076923131942749,17.080153999328616,2.076923131942749L7.850153999328613,2.076923131942749C6.700153999328613,2.076923131942749,5.770153999328613,3.006923131942749,5.770153999328613,4.156923131942749L5.770153999328613,19.84692313194275C5.770153999328613,20.99692313194275,6.700153999328613,21.91692313194275,7.850153999328613,21.92692313194275L17.080153999328616,21.92692313194275C18.220153999328613,21.91692313194275,19.150153999328616,20.99692313194275,19.150153999328616,19.84692313194275ZM7.154773999328613,4.1515431319427485L7.154773999328613,19.85152313194275C7.154773999328613,20.231523131942748,7.464773999328614,20.54152313194275,7.844773999328613,20.54152313194275L17.074753999328614,20.54152313194275C17.464753999328614,20.54152313194275,17.76475399932861,20.231523131942748,17.774753999328613,19.85152313194275L17.774753999328613,4.1515431319427485C17.774753999328613,3.771543131942749,17.464753999328614,3.461543131942749,17.074753999328614,3.461543131942749L7.844773999328613,3.461543131942749C7.464773999328614,3.461543131942749,7.154773999328613,3.771543131942749,7.154773999328613,4.1515431319427485ZM14.077073999328613,5.536153131942749C14.077073999328613,5.156153131942749,13.767073999328613,4.846153131942749,13.387073999328614,4.846153131942749L11.537073999328612,4.846153131942749C11.157073999328613,4.846153131942749,10.847073999328614,5.156153131942749,10.847073999328614,5.536153131942749C10.847073999328614,5.91615313194275,11.157073999328613,6.226153131942749,11.537073999328612,6.226153131942749L13.387073999328614,6.226153131942749C13.767073999328613,6.226153131942749,14.077073999328613,5.91615313194275,14.077073999328613,5.536153131942749ZM14.304003999328613,17.76922313194275C14.694003999328613,17.76922313194275,15.004003999328614,18.07922313194275,15.004003999328614,18.45922313194275C15.004003999328614,18.83922313194275,14.694003999328613,19.149223131942748,14.304003999328613,19.149223131942748L10.614003999328613,19.149223131942748C10.234003999328614,19.149223131942748,9.924003999328614,18.83922313194275,9.924003999328614,18.45922313194275C9.924003999328614,18.07922313194275,10.234003999328614,17.76922313194275,10.614003999328613,17.76922313194275L14.304003999328613,17.76922313194275Z',
          fillRule: 'evenodd',
        },
      },
    ],
  },
};

function Mobile(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={mobileIcon} />;
}

Mobile.displayName = 'EchMobileIcon';

export default Mobile;
