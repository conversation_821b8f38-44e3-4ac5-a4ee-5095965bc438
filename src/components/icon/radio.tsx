import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const radioIcon: IconComponentProps['icon'] = {
  name: 'radio',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false', fill: 'none' },
    children: [
      {
        tag: 'g',
        attrs: { fill: 'none' },
        children: [
          {
            tag: 'circle',
            attrs: {
              cx: '10',
              cy: '10',
              r: '8.333',
              stroke: 'currentColor',
            },
          },
          {
            tag: 'circle',
            attrs: {
              cx: '10',
              cy: '10',
              r: '3.333',
              stroke: 'currentColor',
            },
          },
        ],
      },
    ],
  },
};

function Radio(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={radioIcon} />;
}

Radio.displayName = 'EchRadioIcon';

export default Radio;
