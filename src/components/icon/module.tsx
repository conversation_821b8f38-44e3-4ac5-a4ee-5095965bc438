import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const moduleIcon: IconComponentProps['icon'] = {
  name: 'module',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 18 18', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M11,13L11,21L4,21C3.447715,21,3,20.5523,3,20L3,13L11,13ZM13,3L20,3C20.5523,3,21,3.447715,21,4L21,20C21,20.5523,20.5523,21,20,21L13,21L13,3ZM3,4C3,3.447715,3.447715,3,4,3L11,3L11,11L3,11L3,4Z',
        },
      },
    ],
  },
};

function Module(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={moduleIcon} />;
}

Module.displayName = 'EchModuleIcon';

export default Module;
