import Icon, { AntdIconProps, IconComponentProps } from '@ant-design/icons/es/components/AntdIcon';

const shopIcon: IconComponentProps['icon'] = {
  name: 'shop',
  theme: 'outlined',
  icon: {
    tag: 'svg',
    attrs: { viewBox: '0 0 20 20', focusable: 'false' },
    children: [
      {
        tag: 'path',
        attrs: {
          d: 'M16.333 11.384c-.21 0-.35.145-.35.363v3.412c0 .29-.246.544-.527.544H5.281c-.281 0-.527-.254-.527-.544v-3.412c0-.218-.14-.363-.35-.363-.211 0-.351.145-.351.363v3.412c0 .69.561 1.27 1.228 1.27h10.175c.667 0 1.228-.58 1.228-1.27v-3.412c0-.218-.14-.363-.35-.363Zm1.439-4.392c0-.072 0-.145-.035-.217l-1.825-3.557a.295.295 0 0 0-.28-.218H4.789c-.14 0-.28.109-.315.218l-1.439 3.63C3 6.92 3 6.955 3 7.027v1.235c0 1.306 1.053 2.359 2.316 2.359h.666c.807 0 1.51-.4 1.93-1.053.421.617 1.193 1.053 2.14 1.053h.737c.913 0 1.685-.436 2.106-1.125.42.653 1.123 1.089 1.93 1.089h.666c1.263 0 2.281-1.053 2.281-2.36V6.992ZM5.035 3.726h10.386l1.474 2.904H3.877l1.158-2.904ZM15.491 9.86h-.666c-.878 0-1.58-.726-1.58-1.634 0-.217-.14-.362-.35-.362-.21 0-.351.145-.351.362v.182c0 .798-.772 1.488-1.755 1.488h-.736c-.983 0-1.755-.58-1.755-1.38 0-.035 0-.072-.035-.108v-.036c0-.218-.14-.363-.35-.363-.211 0-.352.145-.352.363a1.56 1.56 0 0 1-1.579 1.56h-.666c-.877-.036-1.614-.762-1.614-1.67v-.907H17.07v.871c0 .908-.702 1.634-1.579 1.634Z',
        },
      },
    ],
  },
};

function Shop(props: AntdIconProps) {
  // @ts-ignore
  return <Icon {...props} icon={shopIcon} />;
}

Shop.displayName = 'EchShopIcon';

export default Shop;
