import { HTMLAttributes, MouseEventHandler, ReactNode, useCallback } from 'react';
import Portal, { PortalProps } from '@rc-component/portal';
import classNames from 'classnames';
import { isNil } from '@echronos/core';
import { useTranslation } from 'react-i18next';
import FuncCreateEntry from './func-create-entry';
import styles from './desktop-menu.module.less';

interface MenuItemType {
  key: string | number;
  langKey?: string;
  label: ReactNode;
  title?: string;
  disabled?: boolean;
  icon: string;
  bgcIcon?: string;
  type?: 'item';
}

interface MenuDividerType {
  type: 'divider';
}

export type ItemType = MenuItemType | MenuDividerType | null;

export interface DesktopMenuProps
  extends Pick<PortalProps, 'getContainer' | 'open'>,
    Omit<HTMLAttributes<HTMLDivElement>, 'onSelect'> {
  items: ItemType[];
  onSelect: MultipleParamsFn<[MenuItemType]>;
  userInfo: any;
}

function DesktopMenu({
  open,
  items,
  getContainer = '#micro-root',
  className,
  onSelect,
  userInfo,
  ...props
}: DesktopMenuProps) {
  const onClick: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      e.stopPropagation();

      let el = e.target as HTMLElement | null;
      while (el && !el.hasAttribute('data-index')) {
        el = el.parentElement;
      }

      if (el) {
        // @ts-ignore
        const currentItem = items[Number(el.getAttribute('data-index') || '-1')];
        if (currentItem) {
          // @ts-ignore
          onSelect(currentItem);
        }
      }
    },
    [items, onSelect]
  );
  // 多语言导入
  const { t } = useTranslation();

  return (
    <Portal open={open} getContainer={getContainer}>
      <div {...props} className={classNames(styles.menuWrapper, className)} id="desktopMenu">
        <ul className={styles.menu}>
          {items.map((item, index) => {
            if (isNil(item)) {
              return null;
            }
            return (
              // @ts-ignore
              <li key={item.key || index}>
                {item.type === 'divider' ? (
                  <div className={styles.divider} />
                ) : (
                  <div>
                    {item.key === 'createFunc' ? (
                      <FuncCreateEntry userInfo={userInfo}>
                        <div
                          role="button"
                          tabIndex={index}
                          data-index={index}
                          title={item.title}
                          className={styles.item}
                          onClick={onClick}
                        >
                          <div
                            style={{
                              backgroundImage: item.bgcIcon ? `url('${item.bgcIcon}')` : '',
                            }}
                          >
                            <img className={styles.itemImg} src={item.icon} alt="" />
                          </div>
                          <span>{item.langKey ? t(item.langKey) : item.label}</span>
                        </div>
                      </FuncCreateEntry>
                    ) : (
                      <div
                        role="button"
                        tabIndex={index}
                        data-index={index}
                        title={item.title}
                        className={styles.item}
                        onClick={onClick}
                      >
                        <div
                          style={{
                            backgroundImage: item.bgcIcon ? `url('${item.bgcIcon}')` : '',
                          }}
                        >
                          <img className={styles.itemImg} src={item.icon} alt="" />
                        </div>
                        <span>{t(String(item.langKey))}</span>
                      </div>
                    )}
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      </div>
    </Portal>
  );
}

export default DesktopMenu;
