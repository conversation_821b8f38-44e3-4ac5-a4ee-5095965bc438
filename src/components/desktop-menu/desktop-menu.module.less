@import '@echronos/react/less/index';

.menu {
  margin: 0;
  padding: 5px !important;
  list-style: none;

  &Wrapper {
    width: 210px;
    backdrop-filter: blur(80px);
    background: hsla(#f5f5f5, 0.6);
    border-radius: 6px;
    box-shadow: 0 0 1px 0 rgb(0 0 0 / 40%), 0 0 1.5px 0 rgb(0 0 0 / 30%),
      0 7px 22px 0 rgb(0 0 0 / 25%);
    position: fixed;
    z-index: 9999;
  }
}

.item {
  font-weight: 400;
  display: flex;
  padding: 4px 8px;
  align-items: center;
  letter-spacing: 0;
  border-radius: 3px;
  cursor: pointer;
  user-select: none;

  &:hover {
    //color: @white;
    //background: linear-gradient(0deg, rgb(0 140 255 / 75%), rgb(0 140 255 / 75%)), #008cff;
    background: rgb(0 0 0 / 3.73%);
  }
}

.itemImg {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.divider {
  height: 1px;
  margin: 4px auto;
  background: rgb(0 0 0 / 10%);
}
