.popover {
  width: 210px;
  padding-left: 0 !important;
  position: fixed !important;
  z-index: 999999 !important;
  border-radius: 6px;
  backdrop-filter: blur(80px);
  background: hsla(#f5f5f5, 0.6);
  box-shadow: 0 0 1px 0 rgb(0 0 0 / 40%), 0 0 1.5px 0 rgb(0 0 0 / 30%),
    0 7px 22px 0 rgb(0 0 0 / 25%);
  list-style: none;

  :global {
    .ant-popover-content {
      width: 210px;
    }

    .ant-popover-arrow {
      display: none !important;
    }

    .ant-popover-inner-content {
      padding: 5px !important;
    }

    .ant-popover-inner {
      background-color: transparent !important;
    }
  }
}

.item {
  display: flex;
  height: 28px;
  padding: 4px 8px;
  align-items: center;
  border-radius: 3px;
  cursor: pointer;

  &:hover {
    background: rgb(0 0 0 / 3.73%);
  }
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
