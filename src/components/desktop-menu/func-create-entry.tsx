import { ReactNode, useEffect, useState } from 'react';
import { Popover, message } from 'antd';
import { emitBridgeEvent } from '@echronos/core';
import { useRequest } from 'ahooks';
import useNavigate from '@/hooks/use-navigation';
import { getJoinCompanies, getPermStatusList, JoinCompanyResult } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './func-create-entry.module.less';

interface FuncProps {
  key: string;
  langKey?: string;
  label: string;
  url: string;
  code: string;
  icon: string;
  isPermission?: boolean;
  isCharge?: boolean;
  appId?: number;
}

const list: FuncProps[] = [
  {
    key: 'createGroup',
    langKey: 'desk_createGroup',
    label: '发起群聊',
    url: '',
    code: '',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1620713676641168790.png`,
  },
  {
    key: 'addUser',
    langKey: 'desk_addUser',
    label: '添加好友',
    url: '',
    code: '',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1703071390537979.png`,
  },
  {
    key: 'createCalendar',
    langKey: 'desk_createCalendar',
    label: '日程会议',
    url: '/user/calendar/home?isCreate=1',
    code: '',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1716016668811530278.png`,
  },
  {
    key: 'createApprove',
    langKey: 'desk_createApprove',
    label: '审批申请',
    url: '/admin/process/apply',
    code: '',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1697441715683259208.png`,
  },
  {
    key: 'createCustom',
    langKey: 'desk_createCustom',
    label: '客户',
    url: '/scrm/client/firm-client?isCreate=1',
    code: 'F_001_002_001_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1708753971501752.png`,
  },
  {
    key: 'createBusiness',
    langKey: 'desk_createBusiness',
    label: '商机',
    url: '/scrm/client/business-opportunity?isCreate=1',
    code: 'F_001_003_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1708754031760902.png`,
  },
  {
    key: 'createFollow',
    langKey: 'desk_createFollow',
    label: '跟进记录',
    url: '/scrm/client/follow-record?isCreate=1',
    code: 'F_001_007_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1708754055016840.png`,
  },
  {
    key: 'createGoods',
    langKey: 'desk_createGoods',
    label: '商品',
    url: '/goods/create-product/single/0/',
    code: 'M_001_002_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1692688815824132.png`,
  },
  {
    key: 'createCircle',
    langKey: 'desk_createCircle',
    label: '圈层价',
    url: '/shop/circle/baseInfo/0/?status=create&link=0',
    code: 'Q_001_001_003',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1620713608346401161.png`,
  },
  {
    key: 'createSell',
    langKey: 'desk_createSell',
    label: '采购订单',
    url: '/indent/create?orderProperty=1&orderType=1',
    code: 'R_001_010_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724479980081118.png`,
  },
  {
    key: 'createBuy',
    langKey: 'desk_createBuy',
    label: '销售订单',
    url: '/indent/create?orderProperty=2&orderType=1',
    code: 'R_001_009_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724480039110782.png`,
  },
  {
    key: 'createEnter',
    langKey: 'desk_createEnter',
    label: '入库单',
    url: '/stock/create?stockType=enter',
    code: 'AV_001_002_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724480577847505.png`,
  },
  {
    key: 'createOut',
    langKey: 'desk_createOut',
    label: '出库单',
    url: '/stock/create?stockType=out',
    code: 'AV_001_001_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724480538319407.png`,
  },
  {
    key: 'createTaking',
    langKey: 'desk_createTaking',
    label: '盘点单',
    url: '/stock/create?stockType=taking',
    code: 'AV_001_004_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1724480618876946.png`,
  },
  {
    key: 'createBidding',
    langKey: 'desk_createBidding',
    label: '采购',
    url: '/bidding/bidding/plan/0',
    code: 'AT_001_002_001_002_001',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/bidding-function1.png`,
  },
  {
    key: 'createInquiry',
    langKey: 'desk_createInquiry',
    label: '询价',
    url: '/rfq/inquiry/info/0?from=1',
    code: 'C_001_001_002',
    icon: `${import.meta.env.BIZ_ORIGIN_STATIC_URL}/static/img/1698285160990687214.png`,
  },
];

interface FuncCreateEntryProps {
  userInfo: any;
  children: ReactNode;
}

function FuncCreateEntry({ userInfo, ...props }: FuncCreateEntryProps) {
  const navigate = useNavigate();
  const [funcList, setFuncList] = useState<FuncProps[]>([...list]);
  const [companyList, setCompanyList] = useState<JoinCompanyResult[]>([]);
  // @ts-ignore
  // eslint-disable-next-line no-unsafe-optional-chaining
  const microData = window.microApp?.getData();

  // 多语言导入
  const { t } = useTranslation();

  const { run } = useRequest(getJoinCompanies, {
    manual: true,
    onSuccess: (res) => {
      setCompanyList(res);
    },
  });

  // @ts-ignore
  const { run: runPerm } = useRequest(getPermStatusList, {
    manual: true,
    onSuccess: (res) => {
      list.forEach((item) => {
        const items = item;
        res.list.forEach((perm) => {
          if (item.code && item.code === perm.permCode) {
            items.isPermission = perm.isPermission;
            items.isCharge = perm.isCharge;
            items.appId = perm.appId;
          }
        });
      });
      setFuncList(list);
    },
  });

  useEffect(() => {
    run();
    const ids = list.filter((item) => item.code).map((item) => item.code);
    runPerm({ permCodes: ids });
  }, [run, runPerm]);

  const onHandle = (e: any, val: FuncProps) => {
    e.stopPropagation();
    switch (val.key) {
      case 'createGroup':
        if (microData && microData.navigate) {
          // @ts-ignore
          microData?.createChatGroup(
            {
              id: userInfo?.id,
              label: userInfo?.nickname,
              avatar: userInfo?.avatar,
              memberId: userInfo?.id || 0,
            },
            {
              companies: companyList,
              onSuccess: (session: any) => {
                if (session && session.id) {
                  navigate(`/imc/${session.id}`);
                }
              },
            }
          );
        }
        break;
      case 'addUser':
        emitBridgeEvent('searchAndAddFriends');
        break;
      case 'createCalendar':
      case 'createApprove':
      case 'createCustom':
      case 'createBusiness':
      case 'createFollow':
      case 'createGoods':
      case 'createCircle':
      case 'createSell':
      case 'createBuy':
      case 'createEnter':
      case 'createOut':
      case 'createTaking':
      case 'createBidding':
      case 'createInquiry':
        if (val.code) {
          if (!val.isCharge) {
            navigate(`/appstore/app/${val.appId}/1`);
            return;
          }
          if (!val.isPermission) {
            message.warning(`${t('public_noPerm')}`);
            return;
          }
        }
        if (val.url) {
          navigate(val.url);
        }
        break;
      default:
        break;
    }
  };

  const content = (
    <div>
      {funcList.map((item) => (
        <div
          role="button"
          tabIndex={0}
          className={styles.item}
          key={item.key}
          onClick={(e) => onHandle(e, item)}
        >
          <img className={styles.icon} src={item.icon} alt="" />
          <span>{item.langKey ? t(item.langKey) : item.label}</span>
        </div>
      ))}
    </div>
  );

  return (
    <Popover
      placement="rightBottom"
      content={content}
      trigger="hover"
      arrowPointAtCenter={false}
      overlayClassName={styles.popover}
      // getPopupContainer={() => document.getElementById('desktopMenu')}
    >
      {props.children}
    </Popover>
  );
}

export default FuncCreateEntry;
