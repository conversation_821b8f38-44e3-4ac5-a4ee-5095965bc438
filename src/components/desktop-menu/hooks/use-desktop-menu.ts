import { MouseEvent, useRef } from 'react';
import { useEventListener, useUnmount } from 'ahooks';
import { renderComponent, RenderComponentInstance } from '@echronos/react';
import { EMPTY_FN, isArray, isFunction } from '@echronos/core';
import DesktopMenu, { DesktopMenuProps, ItemType } from '../desktop-menu';
import styles from '../desktop-menu.module.less';

export type DesktopMenuItemType =
  | null
  | ItemType[]
  | MultipleParamsFn<[MouseEvent<HTMLElement>], null | ItemType[]>;

export interface DeskTopMenuItemOptions {
  items: DesktopMenuItemType;
  onSelect?: DesktopMenuProps['onSelect'];
  disable?: () => boolean;
  userInfo: any;
}

function useDesktopMenu(
  target: Parameters<typeof useEventListener>[2]['target'],
  opts: DesktopMenuItemType | DeskTopMenuItemOptions
) {
  const menuRef = useRef<RenderComponentInstance | null>();
  const destroy = () => {
    menuRef.current?.destroy();
    menuRef.current = null;
  };

  useUnmount(destroy);

  useEventListener(
    'contextmenu',
    (e: MouseEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      let items: DesktopMenuItemType | undefined;
      let onSelect: DesktopMenuProps['onSelect'] = EMPTY_FN;
      if (isArray(opts) || isFunction(opts)) {
        items = opts;
      } else if (opts) {
        items = opts.items;
        onSelect = opts.onSelect || EMPTY_FN;
        if (opts && opts.disable && !opts.disable()) {
          return;
        }
      }

      const instance = menuRef.current;
      const props = {
        open: true,
        // @ts-ignore
        items: isFunction(items) ? items(e) : items,
        style: {
          top: e.clientY,
          left: e.clientX,
        },
        onSelect: (value: any) => {
          onSelect(value);
          if (value.key !== 'createFunc') {
            destroy();
          }
        },
        // @ts-ignore
        userInfo: opts?.userInfo,
      } as DesktopMenuProps;
      if (instance) {
        if (props.items) {
          instance.update(props);
        } else {
          instance.destroy();
        }
      } else if (props.items) {
        menuRef.current = renderComponent(DesktopMenu, props);
      }
    },
    { target }
  );
  useEventListener('click', (e) => {
    const maxCount = 4;
    let el = e.target as HTMLElement | null;
    let i = 0;
    while (el && i < maxCount) {
      const { classList } = el;
      if (classList.contains(styles.item) || classList.contains(styles.menu)) {
        break;
      }
      i += 1;
      el = i === maxCount ? null : el.parentElement;
    }

    if (!el) {
      destroy();
    }
  });
}

export default useDesktopMenu;
