/* eslint-disable no-underscore-dangle */
import { useContext, useEffect, useState } from 'react';
import { useRoutes } from 'react-router-dom';
import { Spin } from 'antd';
import {
  CLIENT,
  emitBridgeEvent,
  initPermission,
  userInit,
  setPermissionMap,
  UserPermission,
} from '@echronos/core';
import { setLazyFallback, UserinfoContext } from '@echronos/react';
import SpinBox from './components/spin-box';
import routes from './routes';
import './styles/antd/index.less';
import './app.less';

function App() {
  const router = useRoutes(routes);
  const { updateUserStore } = useContext(UserinfoContext);
  const [initial, setInitial] = useState(false);

  // const lang = localStorage.getItem('lang') || 'zh';
  // setHttpConfig({
  //   headers: {
  //     'accept-language': lang === 'zh' ? 'zh-cn' : lang,
  //   },
  // });

  const initUserPermission = () => {
    const microData = window.microApp?.getData();
    if (microData) {
      // eslint-disable-next-line no-console
      console.log('microData.permissionMap.size', (microData.permissionMap as any).size);
    }

    if (
      CLIENT &&
      window.__MICRO_APP_ENVIRONMENT__ &&
      microData &&
      microData.permissionMap &&
      (microData.permissionMap as any).size > 0
    ) {
      setPermissionMap(microData.permissionMap as UserPermission);
      return Promise.resolve();
    }
    // eslint-disable-next-line no-underscore-dangle
    return initPermission(!(CLIENT && window.__MICRO_APP_ENVIRONMENT__));
  };

  useEffect(() => {
    initUserPermission().then(() => {
      userInit()
        .then((result) => {
          if (result?.user.id) {
            // @ts-ignore
            updateUserStore(result);
          }
        })
        .finally(() => {
          setInitial(true);
          emitBridgeEvent('micro-app:mounted');
        });
    });

    setLazyFallback(<SpinBox />);
  }, []); // eslint-disable-line

  return initial ? (
    router
  ) : (
    <Spin spinning>
      <div style={{ width: '100%' }} />
    </Spin>
  );
}

export default App;
