@import 'styles/mixins/mixins';

html,
body {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

ol,
ul,
dl {
  margin: 0;
  padding: 0;
  list-style: none;
}

section,
header,
nav,
main,
aside,
footer {
  display: block;
}

img {
  max-width: 100%;
  max-height: 100%;

  &.error-white,
  &.error-gray {
    overflow: hidden;
    position: relative;

    &::after {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #fff;
    }
  }

  &.error-gray {
    &::after {
      background: #f5f5f5;
    }
  }
}

// 360急速游览器BUG
:focus {
  outline: 0;
}
