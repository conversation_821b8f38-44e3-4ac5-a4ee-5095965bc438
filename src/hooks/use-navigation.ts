import { useMemo } from 'react';
import { NavigateFunction, useNavigate as useCurrentNavigate } from 'react-router-dom';
import { CLIENT, EMPTY_FN } from '@echronos/core';

// eslint-disable-next-line import/no-mutable-exports
let useNavigation: () => NavigateFunction;

if (CLIENT && window.microApp) {
  useNavigation = () =>
    useMemo(() => {
      const microData = window.microApp?.getData();
      if (microData && microData.navigate) {
        return microData.navigate as NavigateFunction;
      }
      return EMPTY_FN as NavigateFunction;
    }, []);
} else {
  useNavigation = useCurrentNavigate;
}

export default useNavigation;
