import { useSize } from 'ahooks';
import { useMemo, CSSProperties } from 'react';
import cloneDeep from 'lodash/cloneDeep';
import { IMyApp } from '@/apis';

// 网格尺寸类型（使用的时候要注意是网格还是屏幕）
export type GridSizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
// 容器尺寸类型（使用的时候要注意是网格还是屏幕）
export type ContainerSizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

export type PageGridColumns = Record<ContainerSizeType, number>;

export type GridPlace = [cols: number, rows: number];

export type PageGridPlace = Record<ContainerSizeType, GridPlace>;

export interface GridSize {
  w: number;
  h: number;
}

export interface Grid {
  id: string;
  // 宽格子数
  w: number;
  // 高格子数
  h: number;
}

interface GridLayout extends Grid {
  left?: number;
  top?: number;
}

export interface GridData {
  id: number | string;
  app: IMyApp;
  style: CSSProperties;
}

export interface PageGrid {
  page: number;
  grids: GridData[];
}

export interface LayoutInfo {
  rows: number;
  cols: number;
  size: GridSize;
  place: PageGridPlace;
  pageGridColumns: PageGridColumns;
}

export type ComputeGridFn = MultipleParamsFn<[apps: IMyApp[]], PageGrid[]>;

// 网格尺寸类型
const gridSizeType: GridSize[] = [
  { w: 87, h: 96 },
  { w: 140, h: 140 },
];

// 每种屏幕尺寸下每行的列数
const defaultPageGridColumns: PageGridColumns = { xs: 4, sm: 4, md: 6, lg: 8, xl: 10, xxl: 10 };

// 所有屏幕下的网格站位
const gridPlaceMapForAllSizeScreen: PageGridPlace[] = [
  {
    xs: [1, 1],
    sm: [4, 2],
    md: [4, 4],
    lg: [4, 4],
    xl: [4, 4],
    xxl: [4, 4],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [4, 2],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [6, 2],
  },
];

const toGridPlace: Record<string, GridSizeType> = {
  app: 'xs',
  folder: 'md',
  calendar: 'md',
  shareApp: 'md',
  processApprove: 'md',
  banner: 'xxl',
};

export const createRow = (cols: number) => new Array(cols).fill('null');

// 创建单页的坐标布局
export const createSinglePageGridLayoutSite = (
  containerSize: ReturnType<typeof useSize>,
  gridSize: GridSize,
  cols: number
) => {
  const site: Array<Array<string>> = [];

  const rows = containerSize ? Math.floor(containerSize.height / gridSize.h) : 1;

  for (let i = 0; i < rows; i += 1) {
    site[i] = createRow(cols);
  }

  return site;
};

// 判断是否可以定位
export const isCanLocate = (
  currentGridLayoutSite: Array<Array<string>>,
  lay: Grid,
  site: [number, number]
) => {
  let arr: string[] = [];
  const x = site[0];
  const y = site[1];
  for (let i = 0, j = lay.h; i < j; i += 1) {
    if (currentGridLayoutSite[x + i]) {
      arr = arr.concat(currentGridLayoutSite[x + i].slice(y, y + lay.w));
    }
  }
  const isExitUnNull = arr.filter((str) => str !== 'null').length;
  if (arr.length !== lay.w * lay.h || isExitUnNull) {
    return false;
  }
  return true;
};

// 转化成页面数据
const toPageData = (data: Array<Array<GridLayout>>, apps: IMyApp[], gridSize: GridSize) => {
  const appsMap = {};
  apps.forEach((app) => {
    // @ts-ignore
    appsMap[`${app.id}`] = app;
  });
  const pages: PageGrid[] = [];

  data.forEach((page, index) => {
    pages.push({
      page: index,
      grids: page.map((item) => ({
        id: item.id,
        // @ts-ignore
        app: appsMap[item.id],
        style: {
          width: item.w * gridSize.w,
          height: item.h * gridSize.h,
          top: item.top,
          left: item.left,
          position: 'absolute',
        },
      })),
    });
  });
  return pages;
};

const recordGridLayoutSite = (
  singPageGridLayoutSite: Array<Array<string>>,
  startSite: [number, number],
  grid: Grid
) => {
  for (let i = 0; i < grid.h; i += 1) {
    singPageGridLayoutSite[startSite[0] + i].splice(
      startSite[1],
      grid.w,
      ...new Array(Number(grid.w)).fill(grid.id)
    );
  }
};

const computePagesGridLayoutByGrids = (
  grids: Grid[],
  layoutInfo: LayoutInfo,
  containerSize: ReturnType<typeof useSize>
) => {
  const gridSize = layoutInfo.size;
  const { cols } = layoutInfo;
  const gridLayoutSiteData: Array<Array<Array<string>>> = [
    createSinglePageGridLayoutSite(containerSize, gridSize, cols),
  ];

  const gridLayouts: GridLayout[] = cloneDeep(grids);
  gridLayouts.forEach((item) => {
    let isCanLocateFlag = false;
    for (
      let pageIndex = 0, pageLength = gridLayoutSiteData.length;
      pageIndex < pageLength;
      pageIndex += 1
    ) {
      const singPageGridLayoutSite = gridLayoutSiteData[pageIndex];
      for (let i = 0, l = singPageGridLayoutSite.length; i < l; i += 1) {
        for (let x = 0, y = singPageGridLayoutSite[i].length; x < y; x += 1) {
          if (singPageGridLayoutSite[i][x] === 'null') {
            if (isCanLocate(singPageGridLayoutSite, item, [i, x])) {
              // eslint-disable-next-line no-param-reassign
              item.top = i * gridSize.h;
              // eslint-disable-next-line no-param-reassign
              item.left = x * gridSize.w;
              isCanLocateFlag = true;
              recordGridLayoutSite(singPageGridLayoutSite, [i, x], item);
              break;
            }
          }
        }
        if (isCanLocateFlag) {
          break;
        }
      }
      if (isCanLocateFlag) {
        break;
      }
      if (pageIndex === pageLength - 1 && !isCanLocateFlag) {
        gridLayoutSiteData.push(createSinglePageGridLayoutSite(containerSize, gridSize, cols));
      }
    }
  });

  const gridLayoutsMap: Record<string, GridLayout> = {};
  gridLayouts.forEach((grid) => {
    gridLayoutsMap[grid.id] = grid;
  });
  const pagesGridLayouts: Array<Array<GridLayout>> = [];
  const pagesGridLayoutsMap: Array<Array<string>> = [];
  gridLayoutSiteData.forEach((pageGrids, index) => {
    pagesGridLayoutsMap[index] = [];
    pagesGridLayouts[index] = [];
    pageGrids.forEach((row) => {
      row.forEach((gridId) => {
        if (!pagesGridLayoutsMap[index].includes(gridId)) {
          if (gridId !== 'null') {
            pagesGridLayoutsMap[index].push(gridId);
          }
          if (gridLayoutsMap[gridId]) {
            pagesGridLayouts[index].push(gridLayoutsMap[gridId]);
          }
        }
      });
    });
  });
  // eslint-disable-next-line no-console
  console.log('页面布局---', gridLayoutSiteData);
  // eslint-disable-next-line no-console
  console.log('pagesGridLayoutsMap---', pagesGridLayoutsMap);
  // eslint-disable-next-line no-console
  console.log('pagesGridLayouts---', pagesGridLayouts);

  return pagesGridLayouts;
};

function useComputeGrids(
  container: Parameters<typeof useSize>[0]
): [computeGrid: ComputeGridFn, containerSize: ReturnType<typeof useSize>, info: LayoutInfo] {
  const containerSize = useSize(container) || { width: 0, height: 0 };

  const layoutInfo = useMemo(() => {
    if (!containerSize.width) {
      return {
        pageGridColumns: defaultPageGridColumns,
        rows: 4,
        cols: defaultPageGridColumns.xs,
        size: gridSizeType[0],
        place: gridPlaceMapForAllSizeScreen[0],
      };
    }

    const { width, height } = containerSize;
    const size = gridSizeType[width > 560 ? 1 : 0];
    let cols: number;
    if (width < 560) {
      cols = defaultPageGridColumns.xs;
    } else if (width < 840) {
      cols = defaultPageGridColumns.sm;
    } else if (width < 1120) {
      cols = defaultPageGridColumns.md;
    } else if (width < 1400) {
      cols = defaultPageGridColumns.lg;
    } else if (width < 1600) {
      cols = defaultPageGridColumns.xl;
    } else {
      cols = defaultPageGridColumns.xxl;
    }
    const placeIndex = width < 560 ? 0 : (width < 840 && 1) || 2;
    return {
      pageGridColumns: defaultPageGridColumns,
      size,
      cols,
      rows: Math.max(2, Math.floor((height || 1) / size.h)),
      place: gridPlaceMapForAllSizeScreen[placeIndex],
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [containerSize.width, containerSize.height]);

  const computePageGrids = (apps: IMyApp[]) => {
    const gridSize = layoutInfo.size;
    const grids = apps.map((item) => {
      const place =
        gridPlaceMapForAllSizeScreen[1][toGridPlace[item.componentType] || item.size || 'xs'];
      return {
        id: `${item.id}`,
        w: place[0],
        h: place[1],
      };
    });
    return toPageData(
      computePagesGridLayoutByGrids(grids, layoutInfo, containerSize),
      apps,
      gridSize
    );
  };

  return [computePageGrids, containerSize, layoutInfo];
}

export default useComputeGrids;
