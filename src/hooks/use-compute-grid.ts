import { CSSProperties, useMemo } from 'react';
import { useMemoizedFn, useSize } from 'ahooks';
import { IMyApp } from '@/apis';

export type PageGridType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';

export type PageGridColumns = Record<PageGridType, number>;

export type GridPlace = [cols: number, rows: number];

export type PageGridPlace = Record<PageGridType, GridPlace>;

export interface GridSize {
  w: number;
  h: number;
}

export interface GridInfo {
  rows: number;
  cols: number;
  size: GridSize;
  place: PageGridPlace;
  grid: PageGridColumns;
}

export interface Grid {
  id: number | string;
  app: IMyApp;
  style: CSSProperties;
}

export interface PageGrid {
  page: number;
  grids: Grid[];
}

export type ComputeGridFn = MultipleParamsFn<[apps: IMyApp[]], PageGrid[]>;

// 每种屏幕尺寸下每行的列数
const defaultPageGridColumns: PageGridColumns = { xs: 4, sm: 4, md: 6, lg: 8, xl: 10, xxl: 10 };

// PC/WAP 两端下所有尺寸的网格占用位置
// const allGridCardItemPlace: PageGridPlace[] = [
//   {
//     xs: [1, 1],
//     sm: [2, 4],
//     md: [4, 4],
//     lg: [4, 4],
//     xl: [4, 4],
//     xxl: [4, 4],
//   },
//   {
//     xs: [1, 1],
//     sm: [1, 2],
//     md: [2, 2],
//     lg: [3, 2],
//     xl: [4, 2],
//     xxl: [4, 2],
//   },
//   {
//     xs: [1, 1],
//     sm: [1, 2],
//     md: [2, 2],
//     lg: [3, 2],
//     xl: [4, 2],
//     xxl: [6, 2],
//   },
// ];

const allGridItemPlace: PageGridPlace[] = [
  {
    xs: [1, 1],
    sm: [4, 2],
    md: [4, 4],
    lg: [4, 4],
    xl: [4, 4],
    xxl: [4, 4],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [4, 2],
  },
  {
    xs: [1, 1],
    sm: [2, 1],
    md: [2, 2],
    lg: [3, 2],
    xl: [4, 2],
    xxl: [6, 2],
  },
];

// 单个网格大小
const allGridItemSize: GridSize[] = [
  { w: 87, h: 96 },
  { w: 140, h: 140 },
];

const toGridPlace: Record<string, PageGridType> = {
  app: 'xs',
  folder: 'md',
  calendar: 'md',
  shareApp: 'md',
  processApprove: 'md',
  banner: 'xl',
};

function useComputeGrid(
  container: Parameters<typeof useSize>[0],
  grid = defaultPageGridColumns
): [computeGrid: ComputeGridFn, elSize: ReturnType<typeof useSize>, info: GridInfo] {
  const elSize = useSize(container) || { width: 0, height: 0 };
  const info = useMemo(() => {
    if (!elSize.width) {
      return {
        grid,
        rows: 4,
        cols: grid.xs,
        size: allGridItemSize[0],
        place: allGridItemPlace[0],
      };
    }
    const { width, height } = elSize;
    const size = allGridItemSize[width > 560 ? 1 : 0];
    let cols: number;
    if (width < 560) {
      cols = grid.xs;
    } else if (width < 840) {
      cols = grid.sm;
    } else if (width < 1120) {
      cols = grid.md;
    } else if (width < 1400) {
      cols = grid.lg;
    } else if (width < 1600) {
      cols = grid.xl;
    } else {
      cols = grid.xxl;
    }

    const placeIndex = width < 560 ? 0 : (width < 840 && 1) || 2;
    return {
      grid,
      size,
      cols,
      rows: Math.max(2, Math.floor((height || 1) / size.h)),
      place: allGridItemPlace[placeIndex],
    };
  }, [elSize.width, elSize.height]); // eslint-disable-line

  const computedGrid: ComputeGridFn = useMemoizedFn((apps) => {
    let row = 0;
    let col = 0;
    const pages: number[] = [];
    const { cols, rows, size } = info;
    let overflowPage = 0;

    // 获取占用位置的下标
    const getIndex = (r: number, c: number) => r * cols + c;
    // 获取下一步开始位置
    const next = (r: number, c: number): [row: number, col: number] => {
      let nextRow = r;
      let nextCol = c;
      do {
        if (nextCol + 1 >= cols) {
          nextCol = 0;
          nextRow += 1;
        } else {
          nextCol += 1;
        }
      } while (pages[getIndex(nextRow, nextCol)]);

      return [nextRow, nextCol];
    };

    // 溢出元素投放页面
    const computetNextPage = (page: number) => {
      if (page > overflowPage) {
        overflowPage = page;
      } else {
        overflowPage += 1;
      }
      return overflowPage;
    };

    // 计算占用的位置
    const computedPlace = (
      startRow: number,
      startCol: number,
      place: GridPlace,
      id: string
    ): [sRow: number, sCol: number, eRow: number, eCol: number] => {
      const endCol = startCol + place[0] - 1;
      const endRow = startRow + place[1] - 1;

      // 判断需要占用的位置中是否有位置已被占用
      if (
        !pages[getIndex(startRow, startCol)] &&
        !pages[getIndex(endRow, endCol)] &&
        !pages[getIndex(startRow, endCol)] &&
        !pages[getIndex(endRow, startCol)]
      ) {
        // 兼容卡片 如果卡片溢出，放到新的一页
        if (startCol + place[0] > cols && endCol > cols) {
          const nextPage = Math.floor(startRow / rows) * rows;
          return computedPlace(computetNextPage(nextPage), 0, place, id);
        }
        if (
          // 如果不存在换页的，确定占用空间
          startRow === endRow ||
          Math.floor(startRow / rows) === Math.floor(endRow / rows) ||
          // 如果组件占用行数超过每页最大行数，占用整页
          // 或者组件占用列数超过每行最大列数，占用整行
          (endRow - startRow >= rows && startRow % rows === 0) ||
          (endCol >= cols && startCol === 0)
        ) {
          return [startRow, startCol, endRow, endCol];
        }

        // 否则，切换下一页
        return computedPlace(...next(Math.floor(endRow / rows) * rows, -1), place, id);
      }

      return computedPlace(...next(startRow, startCol), place, id);
    };

    const grids: PageGrid[] = [];
    apps.forEach((app, index) => {
      // 获取占用位置
      const place = computedPlace(
        row,
        col,
        info.place[toGridPlace[app.componentType] || app.size || 'xs'],
        `${index}-${app.id}`
      );
      const currentIndex = Math.floor(place[0] / rows);

      // 生成网格信息
      if (!grids[currentIndex]) {
        grids[currentIndex] = { page: currentIndex + 1, grids: [] };
      }
      grids[currentIndex].grids.push({
        app,
        id: `${app.classify}-${app.id}`,
        style: {
          width: (place[3] - place[1] + 1) * size.w,
          height: (place[2] - place[0] + 1) * size.h,
          top: (place[0] % rows) * size.h,
          left: place[1] * size.w,
          position: 'absolute',
        },
      });

      // 占用已使用的位置
      for (let r = place[0]; r <= place[2]; r += 1) {
        for (let c = place[1]; c <= place[3]; c += 1) {
          const idx = getIndex(r, c);
          pages[idx] = (pages[idx] || 0) + 1;
        }
      }

      // 如果没有换页，获取下一步的行列
      if (place[0] === row) {
        [row, col] = next(row, col);
      }
    });

    return grids;
  });

  return [computedGrid, elSize, info];
}

export default useComputeGrid;
