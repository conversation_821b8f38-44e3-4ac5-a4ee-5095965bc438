import { useCallback } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { emitBridgeEvent, openURL } from '@echronos/core';
import { createFunctionSession, findOkrAuthCode } from '@/apis';
import useNavigation from './use-navigation';

export interface ToAppParams {
  id: number;
  icon: string;
  name: string;
  code: string;
  isOpen: number;
  externalApp: number;
  shopId: number;
  classify: number;
  url: string;
}

export type ToAppFn = MultipleParamsFn<[url: string, params: ToAppParams]>;

const useToApp = (): [toApp: ToAppFn, navigate: NavigateFunction] => {
  const navigate = useNavigation();
  const toApp = useCallback(
    (url: string, item: ToAppParams) => {
      if (item.code === 'myShop') {
        // eslint-disable-next-line no-param-reassign
        url += `?bid=${item.shopId}&code=deco_shop`;
      }
      // eslint-disable-next-line no-console
      console.log('desktop-item', item);
      if (item.code !== 'OKR') {
        if (item.icon) {
          emitBridgeEvent('program:emit', {
            id: item.id,
            icon: item.icon,
            name: item.name,
            code: item.code,
            classify: item.classify,
            url: item.url,
          });
        }

        emitBridgeEvent('session:select', { code: item.code });

        if (!item.externalApp) {
          createFunctionSession(item.code);
        }
      }

      if (item.isOpen) {
        if (item.code === 'OKR') {
          findOkrAuthCode(item.code).then((res) => {
            if (res && res.data) {
              openURL(url + res.data, '_blank');
            }
          });
        } else {
          openURL(url, '_blank');
        }
      } else if (/^(https?:)?\/\//.test(url)) {
        window.location.href = url;
      } else {
        navigate(url);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  return [toApp, navigate];
};

export default useToApp;
