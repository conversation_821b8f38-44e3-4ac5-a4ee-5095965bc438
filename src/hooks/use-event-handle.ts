import dayjs from 'dayjs';
import { useRef } from 'react';

const configDefault = {
  click: { time: 200, key: 'click' },
  longPress: { time: 1500, key: 'longPress' },
  drag: { time: -1, key: 'drag' },
  deviationX: 20,
  deviationy: 20,
  refInit: -1,
  eventHandleFlag: ['mousedown'],
  eventNameClick: 'click',
  eventNameMouseUp: 'mouseup',
  // eventNameDrag: 'drag',
};

function useEventHandle() {
  const downTime = useRef(configDefault.refInit);
  const downX = useRef(configDefault.refInit);
  const downY = useRef(configDefault.refInit);
  const timeRef = useRef(null);
  const mouseButton = useRef(configDefault.refInit); // 鼠标按下键

  const initDown = () => {
    downTime.current = configDefault.refInit;
    downX.current = configDefault.refInit;
    downY.current = configDefault.refInit;
    mouseButton.current = configDefault.refInit;
    if (timeRef.current) {
      clearTimeout(timeRef.current);
      timeRef.current = null;
    }
  };

  /**
   * 判断是否为原始值（是否按下赋值）
   * @returns 不是原始值则返回true
   */
  const unPrimitive = () => {
    if (
      downTime.current === configDefault.refInit &&
      downX.current === configDefault.refInit &&
      downY.current === configDefault.refInit &&
      mouseButton.current === configDefault.refInit
    ) {
      return false;
    }

    return true;
  };

  const getEventType = (event: MouseEvent) => {
    const isPrimitiveValue = unPrimitive();
    const isMouseLeft = mouseButton.current === 0; // 是否为左键

    if (isPrimitiveValue && isMouseLeft) {
      const timestamp = dayjs().valueOf();
      const startToEndTime = timestamp - downTime.current;
      const { clientX, clientY } = event;

      const deviation =
        Math.abs(clientX - downX.current) < configDefault.deviationX &&
        Math.abs(clientY - downY.current) < configDefault.deviationy;

      if (startToEndTime < configDefault.click.time && deviation) return configDefault.click.key;
      if (startToEndTime < configDefault.longPress.time && deviation)
        return configDefault.longPress.key;
      return configDefault.drag.key;
    }

    return '';
  };

  const onMouseDown = (event: MouseEvent, fn: any) => {
    const { clientX, clientY, button } = event;
    downTime.current = dayjs().valueOf();
    downX.current = clientX;
    downY.current = clientY;
    mouseButton.current = button;

    // 函数处理
    if (fn) {
      // @ts-ignore
      timeRef.current = setTimeout(() => {
        fn();
        initDown();
      }, configDefault.longPress.time);
    }
  };

  return { getEventType, initDown, onMouseDown };
}

export default useEventHandle;
