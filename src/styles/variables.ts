const colors = {
  color1: '#008CFF',
  color2: '#05D380',
  color3: '#006EFF',
  color4: '#00C6FF',
  color5: '#F9AE08',
  color6: '#EA1C26',
  color7: '#680BC4',
  color8: '#D9EEFF',
  color9: '#FCDDDF',
  color10: '#D0EFE2',
  color11: '#FEF3DA',
  color12: '#EDE5FA',
  color13: '#F5F6FA',
  color14: 'rgba(217, 238, 255, 0.3)',
  color15: 'rgba(217, 238, 255, 0.5)',
  color16: '#040919',
  color17: '#888B98',
  color18: '#B1B3BE',
  color19: '#FFFFFF',
  color20: '#000000',
  color21: '#F3F3F3',
  color22: '#DADBE0',
  color23: '#C6CCD8',
  color24: '#999EB2',
  color25: 'linear-gradient(224deg, #00C6FF 0%, #00C6FF 0%, #008CFF 100%, #008CFF 100%)',
  color26: 'transparent',
  color27: 'rgba(245, 246, 250, 0.7)',
};

export default {
  ...colors,
  // ----------------------------------------------------
  // 基础颜色
  // ----------------------------------------------------
  white: '#fff',
  black: '#000',
  green: colors.color2,
  blue: colors.color3,
  azure: colors.color4,
  yellow: colors.color5,
  red: colors.color6,
  'light-green': colors.color10,
  'light-blue': colors.color8,
  'light-yellow': colors.color11,
  'light-red': colors.color9,
  'text-colors': `{
  primary: ${colors.color16};
  secondary: ${colors.color17};
  click: ${colors.color18};
  disabled: ${colors.color22};
}`,
  'primary-text-colors': `{
  primary: ${colors.color1};
  secondary: ${colors.color4};
  click: ${colors.color3};
  disabled: ${colors.color22};
}`,
  'background-colors': `{
  heavy: ${colors.color23};
  normal: ${colors.color13};
  light: ${colors.color21};
  white: ${colors.color19};
}`,

  // 主色调
  'primary-color': colors.color1,
  'primary-color-hover': colors.color3,
  'primary-background': colors.color25,

  'success-color': colors.color2,
  'error-color': colors.color6,
  'warning-color': colors.color5,
  'disabled-color': colors.color23,

  'text-color': colors.color16,
  'text-color-secondary': colors.color17,
  'font-family':
    'BlinkMacSystemFont,"Helvetica Neue",Helvetica,"Segoe UI",Arial,Roboto,"Microsoft YaHei","PingFang SC","Source Han Sans","Hiragino Sans GB",SimSun,sans-serif',
  'font-size-sm': '12px',
  'font-size-base': '14px',
  'font-size-lg': '16px',
  'font-size-xl': '18px',
  'line-height-base': '1.3',
  'border-radius-base': '10px',
  'border-radius-sm': '8px',
  'border-radius-xs': '6px',
  'border-radius-xxs': '4px',
  'menu-item-padding': '12px 16px',
  'item-active-bg': 'transparent',
  'item-hover-bg': 'rgba(217, 238, 255, 0.3)',
  'border-color-base': colors.color18,
  'border-color-light': colors.color21,

  // ----------------------------------------------------
  // Ant Design 样式变量覆盖
  // ----------------------------------------------------

  // ------------ Buttons Start -----------------
  'btn-font-weight': '500',
  'btn-border-radius-base': '10px',
  'btn-border-radius-sm': '10px',
  'btn-border-width': '0',
  'btn-shadow': 'none',
  'btn-primary-shadow': 'none',
  'btn-text-shadow': 'none',
  'btn-primary-color': colors.color19,
  'btn-primary-bg': colors.color25,
  'btn-default-color': colors.color17,
  'btn-default-bg': colors.color21,
  'btn-disable-color': colors.color19,
  'btn-disable-bg': colors.color23,
  'btn-font-size-lg': '14px',
  'btn-font-size-sm': '12px',
  'btn-padding-horizontal-base': '16px',
  'btn-padding-horizontal-lg': '16px',
  'btn-padding-horizontal-sm': '20px',
  'btn-height-base': '38px',
  'btn-height-lg': '38px',
  'btn-height-sm': '28px',

  'btn-primary-hover-bg': colors.color4,
  'btn-primary-active-bg': colors.color3,
  'btn-primary-focus-bg': colors.color25,
  'btn-default-hover-bg': colors.color13,
  'btn-default-active-bg': colors.color22,
  'btn-default-focus-bg': colors.color21,
  // ------------ Buttons End -----------------

  // ------------ Shadow Start -----------------
  'box-shadow-base': '2px 4px 12px 0px rgba(0, 0, 0, 0.08)',

  'box-shadow-drawer': '-8px 8px 24px 0px rgba(0, 0, 0, 0.16)',
  'box-shadow-dialog': '0px 32px 64px 0px rgba(0, 0, 0, 0.22),0px 2px 21px 0px rgba(0, 0, 0, 0.22)',
  // ------------ Shadow End -----------------

  // form
  'form-vertical-label-padding': '0 0 12px',

  'control-border-radius': '6px',

  // input
  'input-border-color': colors.color18,
  'input-placeholder-color': colors.color18,
  'input-padding-vertical-base': '6px',
  'input-padding-horizontal-base': '12px',
  'input-hover-border-color': colors.color4,
  'input-disabled-bg': colors.color21,

  // checkbox
  'checkbox-color': colors.color1,
  'checkbox-size': '16px',
  'checkbox-border-radius': '4px',

  // radio
  'radio-button-color': colors.color16,
  'radio-focus-shadow': 'none',
  'radio-dot-color': colors.color1,
  'radio-button-checked-bg': colors.color8,
  'radio-button-checked-border-color': colors.color8,

  // switch
  'switch-min-width': '36px',
  'switch-height': '20px',
  'switch-color': '@primary-color',

  // ------------ Modal Start -----------------
  'modal-header-padding': '16px',
  'modal-body-padding': '16px',
  'modal-footer-padding-vertical': '16px',
  // ------------ Modal End -----------------

  // table
  'table-header-bg': colors.color13,
  'table-padding-vertical': '16px',
  'table-padding-horizontal': '16px',
  'table-footer-bg': 'transparent',

  // breadcrumb
  'breadcrumb-last-item-color': '#888b9b',

  // badge
  'badge-height': '18px',
  'badge-color': '#f90400',

  // menu
  'menu-highlight-color': '@primary-color',
  'menu-item-height': '44px',
  'menu-inline-toplevel-item-height': '44px',
  'menu-item-vertical-margin': '0',
  'menu-item-boundary-margin': '0',
  'menu-item-active-bg': 'transparent',

  // dropdown
  'dropdown-edge-child-vertical-padding': '8px',
  'dropdown-vertical-padding': '11px',
  'control-padding-horizontal': '16px',
  'dropdown-selected-color': '#f90400',
  'border-color-split': '#f5f6fa',

  // ------------ Tag Start -----------------
  'tag-default-bg': colors.color13,
  // ------------ Tag End -----------------

  // ------------ Tooltip Start -----------------
  'tooltip-max-width': '250px',
  'tooltip-color': 'colors.color19',
  'tooltip-bg': 'rgba(4, 9, 25, 1)',
  'tooltip-arrow-width': '8px * sqrt(2)',
  'tooltip-distance': '@tooltip-arrow-width - 1px + 4px',
  'tooltip-arrow-color': '@tooltip-bg',
  'tooltip-border-radius': '10px',
  'tooltip-padding ': '5px 12px',
  // ------------ Tooltip End -----------------

  // ------------ Message Start -----------------
  'message-notice-content-padding': '12px 16px',
  'message-notice-content-bg': 'rgba(0, 0, 0, .6)',
  // ------------ Message End -----------------

  // 栅格
  'screen-xs-min': '480px',
  'screen-sm-min': '768px',
  'screen-md-min': '1150px',
  'screen-lg-min': '1660px',
  'screen-xl-min': '1660px',
  'screen-xxl-min': '1660px',

  // ------------ Select Start -----------------
  'select-item-selected-color': colors.color1,
  'select-item-selected-bg': colors.color19,
  'select-item-active-bg': colors.color14,
  'select-dropdown-vertical-padding': '8px',
  'select-dropdown-line-height': '20px',
  // ------------ Select Start -----------------
};
