.flex-row(@height) when (isunit(@height, px)),(isunit(@height, '%')),(isunit(@height, rem)) {
  flex: 0 0 @height;
  max-height: @height;
}

.flex-row() {
  flex-basis: 0;
  flex-grow: 1;
  max-height: 100%;
}

.flex-column(@width) when (isunit(@width, px)),(isunit(@width, '%')),(isunit(@width, rem)) {
  flex: 0 0 @width;
  max-width: @width;
}

.flex-column() {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

.text-overflow() {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-overflow(@row) when (isnumber(@row)) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @row;
  overflow: hidden;
}

.text-wrap {
  word-wrap: break-word;
  word-break: break-all;
}

.screen-xs(@rules) {
  @media (min-width: @screen-xs-min) {
    @rules();
  }
}

.screen-sm(@rules) {
  @media (min-width: @screen-sm-min) {
    @rules();
  }
}

.screen-md(@rules) {
  @media (min-width: @screen-md-min) {
    @rules();
  }
}

.screen-lg(@rules) {
  @media (min-width: @screen-lg-min) {
    @rules();
  }
}
