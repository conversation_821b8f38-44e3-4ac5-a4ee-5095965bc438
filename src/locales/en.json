{"public_cancel": "Cancel", "public_sure": "Confirm", "public_title": "Prompt", "public_search": "Search", "public_submit": "Submit", "public_noPerm": "No permission, please contact the company administrator to enable", "desk_createGroup": "Start group chat", "desk_addUser": "Add friend", "desk_createCalendar": "Schedule meeting", "desk_createApprove": "Approval request", "desk_createCustom": "Customer", "desk_createBusiness": "Business opportunity", "desk_createFollow": "Follow-up records", "desk_createGoods": "Product", "desk_createCircle": "Exclusive Pricing", "desk_createSell": "Purchase order", "desk_createBuy": "Sales order", "desk_createEnter": "Goods receipt", "desk_createOut": "Delivery order", "desk_createTaking": "Inventory list", "desk_createBidding": "Procurement", "desk_createInquiry": "Request for quotation", "desk_openSelf": "Open in current page", "desk_openBlank": "Open in new tab", "desk_refresh": "Refresh", "desk_edit": "<PERSON><PERSON>", "desk_editScreen": "Edit home screen", "desk_delete": "Delete", "desk_deleteSuccess": "Deletion successful", "desk_removeSuccess": "Removal successful", "desk_search": "Search", "desk_addApp": "Add application", "desk_addCard": "Add widget", "desk_createFunc": "Create", "desk_set": "Settings", "desk_groupName": "Folder", "desk_verifyApp_info": "This app does not support web usage", "desk_verifyApp_infos": "{{name}} does not support web usage, please use the mobile app", "desk_verifyApp_sureSendPerm": "Are you sure to request the company administrator to enable access to {{name}}?", "desk_verifyApp_sendPerm": "Permission request has been sent to the company administrator", "desk_folderName": "Distribution organization {{name}}", "desk_imgCrop": "Image crop", "desk_imgCrop_randImgCrop": "Free crop", "desk_imgCrop_customCrop": "Custom crop ratio", "desk_imgCrop_dynamicCrop": "Crop image as needed", "desk_imgCrop_pleImg": "Please select an image", "desk_imgCrop_uploadWrn": "Image size cannot exceed 5MB, please re-upload", "desk_imgCrop_reUpload": "Re-upload", "desk_modal_content": "You have unsaved changes. Are you sure you want to leave?", "desk_modal_cancelText": "Leave", "desk_modal_okText": "Continue editing", "desk_custApp_cusEdit": "Custom edit", "desk_custApp_currentTab": "Current tab", "desk_custApp_newTab": "New tab", "desk_custApp_addUrl": "Please enter the link you want to add, the domain should start with http", "desk_custApp_url": "Link", "desk_custApp_pleUrl": "Please enter the link", "desk_custApp_pleName": "Please enter a name", "desk_custApp_name": "Name", "desk_custApp_open": "Open mode", "desk_custApp_updateSuccess": "Modification successful", "desk_custApp_saveSuccess": "Save successful", "desk_custApp_chooseIcon": "Please select an icon", "desk_custApp_uploadIcon": "Please upload an icon", "desk_custIcon_imgUpload": "You can upload PNG, JPG, GIF images, with a size limit of 5MB, recommended size is 1MB", "desk_custIcon_addImg": "Add image", "desk_custIcon_onlineIcon": "Online icon", "desk_custIcon_SolidColorIcon": "Solid color icon", "desk_custIcon_cusIcon": "Custom icon", "desk_custIcon_ImageIcon": "Custom", "desk_custIcon_text": "Please enter icon text, up to two characters", "desk_custIcon_searchPngWrn": "After entering the website link, the online icon will be automatically retrieved, but some sites may fail to retrieve", "desk_custIcon_loadingPng": "The icon is being retrieved, please wait", "desk_custIcon_bellPng": "The icon for the current link has not been retrieved", "desk_custIcon_otherPng": "You can try selecting a solid color icon or a custom icon", "desk_allApp": "All applications", "desk_allApp_noTitle": "No related content available", "desk_allApp_noText": "Try using a different keyword", "desk_allApp_cusAdd": "Custom add", "desk_allApp_add": "Addition successful", "desk_allApp_know": "I know", "desk_allApp_adds": "Addition successful", "desk_allApp_empty": "No search records, it’s empty", "desk_allApp_moreApp": "More applications", "desk_allApp_searchWid": "Search widgets", "desk_allApp_Widget": "Recommended widgets", "desk_allApp_get": "Recommended", "desk_allApp_all": "All results", "desk_allApp_itm": "Items", "desk_guide_addApp": "Right-click on the desktop to add an application", "desk_guide_deskMessage": "This is your desktop, which will display applications/widgets added by your company as well as your personal added apps/widgets", "desk_guide_pleRgClick": "Right-click on the desktop to start adding your own applications～", "desk_guide_clickAddApp": "Click to add an application", "desk_guide_clickAddAppTo": "Click to add an application and enter the 'All applications' page", "desk_apps_noPermission": "Request permissions", "desk_apps_noBuy": "Expired, please purchase", "desk_apps_calendar": "Calendar schedule", "desk_apps_mon": "Month", "desk_apps_aprv": "(Under review)", "desk_apps_nowEv": "No items", "desk_apps_emptyDes": "Great, no pending items", "desk_apps_checkPersonsStr": "Great, no pending items", "desk_apps_procAprv": "Process approval", "desk_apps_status": "Under review", "desk_apps_time": "YYYY-MM-DD", "desk_apps_person": "People", "desk_apps_and": "And", "desk_disCheck_title": "Distributor review", "desk_disCheck_pendingReview": "Distributors pending review", "desk_disCheck_unit": "Items", "desk_disCheck_normal": "Normal", "desk_disCheck_noData": "No data", "desk_disCheck_defaultName": "Distribution Management", "desk_disCheck_newCount": "Number of new distributors", "desk_disRanking_title": "Sales ranking for this month", "desk_disRanking_noData": "No data", "desk_financeCP_waitCollection": "Pending collection", "desk_financeCP_waitPayment": "Pending payment", "desk_financeCP_collection": "Receive", "desk_financeCP_payment": "Pay", "desk_financeCP_totalWait": "Total pending {{type}} payments ({{count}} entries)", "desk_financeCP_itemTitle": "{{type}} ({{count}} entries)", "desk_financeCP_defaultName": "Financial management", "desk_financeRP_defaultName": "Accounts receivable and payable", "desk_financeRP_receivable": "Accounts receivable", "desk_financeRP_payable": "Accounts payable", "desk_financeRP_dateRange": "Date range", "desk_financeRP_openingBalance": "Opening balance", "desk_financeRP_closingBalance": "Closing balance", "desk_financeRP_currentReceivable": "Receivables this period", "desk_financeRP_currentPayable": "Payables this period", "desk_financeRP_received": "Amount received this period", "desk_financeRP_paid": "Amount paid this period", "desk_financeUD_defaultName": "Upstream and downstream accounts", "desk_financeUD_upDownstream": "Upstream and downstream", "desk_financeUD_accountBalance": "Company account balance", "desk_financeUD_upstreamAccount": "Upstream account ({{count}})", "desk_financeUD_downstreamAccount": "Downstream account ({{count}})", "desk_financeChart_defaultName": "Payment and receipt comparison", "desk_financeChart_month": "Month", "desk_financeChart_payment": "Payment", "desk_financeChart_collection": "Collection", "desk_financeChart_done": "Complete", "desk_financeChart_year": "Year", "desk_financeChart_edit": "Edit", "desk_news_title": "News center", "desk_news_updateTime": "Real-time updated at {{time}}", "desk_notice_title": "Announcement", "desk_notice_view": "View", "desk_order_total": "Total orders", "desk_order_processing": "In progress", "desk_order_completed": "Completed", "desk_order_closed": "Closed", "desk_order_purchase": "Procurement", "desk_order_sale": "Sales", "desk_order_suffix": "Order", "desk_order_create": "New", "desk_order_pending": "Pending {{type}} orders", "desk_order_management": "Order management", "desk_orderEntry_title": "Smart orders", "desk_orderEntry_sales": "Sales orders", "desk_orderEntry_purchase": "Purchase orders", "desk_orderEntry_production": "Production orders", "desk_orderEntry_split": "Order splitting management", "desk_orderEntry_purchaseReturn": "Purchase return orders", "desk_orderEntry_salesReturn": "Sales return orders", "desk_orderEntry_sellToBuy": "Sales-driven procurement", "desk_orderEntry_report": "Query report", "desk_orderEntry_management": "Order management", "desk_procurement_title": "Procurement overview", "desk_procurementModal_title": "Bidding & Tendering", "desk_procurementModal_done": "Complete", "desk_procurementModal_description": "Quickly view procurement progress by specified method", "desk_procurementModal_methodName": "Procurement method name", "desk_procurementModal_select": "<PERSON><PERSON>", "desk_procurementModal_method": "Procurement method", "desk_procurementModal_searchPlaceholder": "Search procurement methods", "desk_procurementModal_cancel": "Cancel", "desk_progress_title": "Procurement progress monitoring", "desk_progress_methodPlaceholder": "Procurement method name", "desk_progress_viewMore": "View more >", "desk_progress_defaultProject": "Shunde Prefabricated Food Industry Development Base Site Enclosure Material Procurement Project", "desk_progress_noData": "No process information", "desk_progress_edit": "Edit", "desk_scrm_trigger": "Anger number", "desk_scrmCustomers_currentCustomers": "Current customer", "desk_scrmDeptMature_quarterOpportunity": "Total business opportunity amount for Q{{quarter}}", "desk_scrmDeptMature_matureOpportunity": "Total mature business opportunity amount for A+B", "desk_scrmDeptMature_edit": "Edit", "desk_scrmDeptMature_team": "Team", "desk_scrmDeptMature_coverageRate": "Business opportunity maturity coverage rate", "desk_scrmDeptMature_setConditions": "Set conditions", "desk_scrmMemberMature_quarterOpportunity": "Total business opportunity amount for Q{{quarter}}", "desk_scrmMemberMature_matureOpportunity": "Total mature business opportunity amount for A+B", "desk_scrmMemberMature_edit": "Edit", "desk_scrmMemberMature_member": "Member", "desk_scrmMemberMature_coverageRate": "Business opportunity maturity coverage rate", "desk_scrmMemberMature_setConditions": "Set conditions", "desk_scrmOpportunity_totalAmount": "Total business opportunity amount ({{unit}})", "desk_scrmOpportunity_yuan": "Yuan", "desk_scrmOpportunity_tenThousand": "Ten thousand yuan", "desk_scrmOpportunity_hundredMillion": "One hundred million yuan", "desk_scrmPerformance_title": "Q{{quarter}} Payment Completion Status (Unit: Ten Thousand Yuan)", "desk_scrmPerformance_department": "Department", "desk_scrmPerformance_target": "Target", "desk_scrmPerformance_totalAmount": "Total business opportunity amount", "desk_scrmPerformance_expectedReturn": "Expected payment", "desk_scrmPerformance_noData": "No data", "desk_scrmPerformance_edit": "Edit", "desk_scrmReturnMoney_quarterTarget": "Q{{quarter}} Payment Target", "desk_scrmReturnMoney_quarterOpportunity": "Q{{quarter}} Total Business Opportunity Amount", "desk_scrmReturnMoney_matureOpportunity": "A+B Total Mature Business Opportunity Amount", "desk_scrmReturnMoney_quarterExpected": "Q{{quarter}} Expected Payment Amount", "desk_scrmReturnMoney_unit": "Ten thousand yuan", "desk_scrmReturnMoney_month": "{{month}} Month", "desk_scrmReturnMoney_compareLastWeek": "Compared to last week", "desk_scrmReturnMoney_noChange": "No change", "desk_scrmReturnMoney_changeAmount": "{{amount}} Ten thousand yuan", "desk_scrmReturnMoney_edit": "Edit", "desk_scrmReturnMoney_team": "Team", "desk_scrmReturnMoney_maskTitle": "Designated Team Payment Data Analysis", "desk_scrmReturnMoney_setConditions": "Set conditions", "desk_scrmStaffReturn_opportunity": "Business opportunity", "desk_scrmStaffReturn_phase": "Stage", "desk_scrmStaffReturn_monthExpected": "{{month}} Expected Payment Amount", "desk_scrmStaffReturn_amount": "¥{{amount}} Ten thousand yuan", "desk_scrmStaffReturn_noData": "No data", "desk_scrmStaffReturn_edit": "Edit", "desk_scrmStaffReturn_staff": "Employee", "desk_scrmStaffReturn_maskTitle": "Designated Team Payment Data Analysis", "desk_scrmStaffReturn_setConditions": "Set conditions", "desk_scrmStaffReturnMoney_quarterTarget": "Q{{quarter}} Payment Target", "desk_scrmStaffReturnMoney_quarterExpected": "Q{{quarter}} Expected Payment Amount", "desk_scrmStaffReturnMoney_unit": "Ten thousand yuan", "desk_scrmStaffReturnMoney_compareLastWeek": "Compared to last week", "desk_scrmStaffReturnMoney_noChange": "No change", "desk_scrmStaffReturnMoney_changeAmount": "{{amount}} Ten thousand yuan", "desk_scrmStaffReturnMoney_edit": "Edit", "desk_scrmStaffReturnMoney_staff": "Employee", "desk_stockApproval_enter": "Stock-in", "desk_stockApproval_out": "Stock-out", "desk_stockApproval_pendingTitle": "{{type}} Documents Pending Review", "desk_stockApproval_supplier": "Supplier/Purchasing Unit", "desk_stockApproval_orderType": "Document Type", "desk_stockApproval_noData": "No data", "desk_stockApproval_warehouseManagement": "Warehouse Management", "desk_stockCard_enter": "Stock-in", "desk_stockCard_out": "Stock-out", "desk_stockCard_taking": "Inventory Check", "desk_stockCard_allot": "Transfer", "desk_stockCard_receive": "Usage", "desk_stockCard_unit": "Entries", "desk_stockCard_total": "Total {{type}} Count", "desk_stockCard_create": "New", "desk_stockCard_pendingApproval": "Pending Approval/Entries", "desk_stockCard_warehouseManagement": "Warehouse Management", "desk_stockEntry_title": "Jingwei Cloud Warehouse", "desk_stockEntry_warehouseList": "Warehouse List", "desk_stockEntry_warehouseQuery": "Warehouse Search", "desk_stockEntry_queryReport": "Query Reports", "desk_stockEntry_stockWarning": "Inventory Alerts", "desk_stockEntry_replenishment": "Replenishment Order", "desk_stockEntry_backendManagement": "Backend Management", "desk_stockEntry_locationManagement": "Storage Location Management", "desk_systemCard_title": "Company Management Policy", "desk_uoumCard_inProgress": "In Progress", "desk_uoumCard_deadline": "Deadline", "desk_uoumCard_ended": "Ended", "desk_uoumCard_timeExpired": "Deadline Reached", "desk_uoumCard_company": "Enterprise", "desk_uoumCardTitle_viewMore": "View More", "desk_uoumSendTime_publishTime": "Release Time", "desk_uoumIntent_title": "<PERSON><PERSON>g Intent Sourcing", "desk_uoumProcurement_title": "Youmeng Procurement Announcement", "desk_uoumVendor_title": "Youmeng Supplier Recruitment", "desk_calendarYear_previous": "Previous Page", "desk_calendarYear_next": "Next Page", "desk_calendarYear_reset": "Reset", "desk_noPermission_message": "Sorry, you do not have permission for this function", "desk_cardContext_edit": "Edit", "desk_selection_quarter1": "First Quarter", "desk_selection_quarter2": "Second Quarter", "desk_selection_quarter3": "Third Quarter", "desk_selection_quarter4": "Fourth Quarter", "desk_selection_choose": "Select", "desk_selection_yuanyuanSCRM": "Customer Relationship Management (CRM)", "desk_selection_pleaseSelect": "Please ensure a quarter and {name} are selected", "desk_selection_done": "Complete", "desk_selection_selectQuarter": "Select Quarter", "desk_selection_select": "Select", "desk_selection_search": "Search", "desk_selection_cancel": "Cancel", "desk_selection_noMemberData": "No member data available", "desk_financeCP_orderPayment": "Order Payments", "desk_financeCP_downstreamRecharge": "Downstream Recharge", "desk_financeCP_balanceWithdrawal": "<PERSON><PERSON>", "desk_procurementView_notIssued": "Not Released", "desk_procurementView_all": "All", "desk_procurement_qualification": "Qualification Review", "desk_procurement_bidding": "Bid Release", "desk_procurement_bidReturn": "Bid Response", "desk_procurement_evaluation": "Bid Evaluation", "desk_procurement_negotiation": "Business Negotiation", "desk_procurement_contract": "Signing", "desk_procurement_editFailed": "Edit Failed", "desk_procurement_methodPlaceholder": "Procurement Method Name", "desk_card_defaultApp": "Default Application", "stock": {"orderStatus": {"all": "All", "pending": "Pending", "approved": "Approved", "draft": "Draft", "oaApproving": "OA Approving"}, "otherOrderBusinessType": {"scrapOutbound": "Scrap Outbound", "internalWelfare": "Internal Welfare", "compensationOut": "Compensation Out", "other": "Other"}, "relateOrderType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "orderBusinessType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "inboundStatus": {"notInbound": "Not Inbound", "inbound": "Inbound", "partialInbound": "Partial Inbound"}, "status": {"draft": "Draft", "pending": "Pending", "rejected": "Rejected", "approved": "Approved", "acceptanceApproving": "Acceptance Approving"}, "statusStr": {"draft": "Draft", "pending": "Pending", "approved": "Approved", "shippingApproving": "Shipping Approving"}, "outboundStatusStr": {"notOutbound": "Not Outbound", "outbound": "Outbound", "partialOutbound": "Partial Outbound"}, "purchaseSubtypeName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "returnGoodsSourceName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "sourceName": {"giftInbound": "Gift Inbound", "compensationInbound": "Compensation Inbound", "otherInbound": "Other Inbound"}, "businessType": {"enter": {"otherInbound": "Other Inbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "allotInbound": "Allot Inbound", "paveGoodsInbound": "Pave Goods Inbound", "diskBalanceInbound": "Disk Balance Inbound"}, "out": {"otherOutbound": "Other Outbound", "salesOutbound": "Sales Outbound", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "paveGoodsOutbound": "Pave Goods Outbound", "diskBalanceOutbound": "Disk Balance Outbound"}, "default": {"otherOutbound": "Other Outbound", "otherInbound": "Other Inbound", "salesOutbound": "Sales Outbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "allotInbound": "Allot Inbound"}}, "orderFuncType": {"onlineMall": "Online Mall", "mallSupplement": "Mall Supplement", "returnSupplement": "Return Supplement"}}}