{"public_cancel": "<PERSON><PERSON>", "public_sure": "Pasti", "public_title": "Pemberitahuan", "public_search": "<PERSON><PERSON>", "public_submit": "Hantar", "public_noPerm": "<PERSON><PERSON><PERSON> keb<PERSON> se<PERSON>, sila hubungi pentadbir syarikat untuk membuka", "desk_createGroup": "<PERSON><PERSON> G<PERSON>", "desk_addUser": "Tambah Rakan", "desk_createCalendar": "<PERSON><PERSON><PERSON>", "desk_createApprove": "<PERSON><PERSON><PERSON><PERSON>", "desk_createCustom": "Pelanggan", "desk_createBusiness": "<PERSON><PERSON><PERSON>", "desk_createFollow": "<PERSON><PERSON><PERSON>", "desk_createGoods": "Barangan", "desk_createCircle": "<PERSON><PERSON>", "desk_createSell": "<PERSON><PERSON><PERSON>", "desk_createBuy": "<PERSON><PERSON><PERSON>", "desk_createEnter": "<PERSON><PERSON>", "desk_createOut": "<PERSON><PERSON>", "desk_createTaking": "<PERSON><PERSON>", "desk_createBidding": "Pembelian", "desk_createInquiry": "<PERSON><PERSON><PERSON>", "desk_openSelf": "<PERSON><PERSON> <PERSON>", "desk_openBlank": "<PERSON><PERSON> <PERSON>", "desk_refresh": "<PERSON><PERSON>", "desk_edit": "<PERSON><PERSON>", "desk_editScreen": "<PERSON> <PERSON><PERSON><PERSON>", "desk_delete": "Padam", "desk_deleteSuccess": "<PERSON><PERSON><PERSON><PERSON>", "desk_removeSuccess": "<PERSON><PERSON><PERSON><PERSON>", "desk_search": "Gelintar", "desk_addApp": "Tambah Aplikasi", "desk_addCard": "Tambah Widget", "desk_createFunc": "<PERSON><PERSON><PERSON>", "desk_set": "Tetapan", "desk_groupName": "Folder", "desk_verifyApp_info": "Aplikasi ini sementara tidak disokong di pelayar web", "desk_verifyApp_infos": "item.name sementara tidak disokong di pelayar web, sila gunakan di peranti mudah alih", "desk_verifyApp_sureSendPerm": "<PERSON><PERSON><PERSON> anda pasti untuk memohon pembukaan hak akses 【name】 daripada pentadbir syarikat?", "desk_verifyApp_sendPerm": "Telah menghantar permohonan hak akses kepada pentadbir syarikat", "desk_folderName": "Organisasi Penjualan name", "desk_imgCrop_randImgCrop": "<PERSON><PERSON><PERSON> bebas", "desk_imgCrop_customCrop": "Nisbah potongan tersuai", "desk_imgCrop_dynamicCrop": "Potong mengikut k<PERSON>an", "desk_imgCrop_pleImg": "<PERSON><PERSON> pilih imej", "desk_imgCrop_uploadWrn": "<PERSON><PERSON> imej tidak boleh melebihi 5MB, sila muat naik semula", "desk_imgCrop_reUpload": "<PERSON>at naik semula", "desk_modal_content": "Anda belum menyimpan perubahan yang telah diedit, anda pasti mahu mening<PERSON>?", "desk_modal_cancelText": "Tinggalkan", "desk_modal_okText": "Teruskan penyuntingan", "desk_custApp_cusEdit": "Penyuntingan Suai <PERSON>aia<PERSON>", "desk_custApp_currentTab": "<PERSON><PERSON> se<PERSON>a", "desk_custApp_newTab": "Tab baru", "desk_custApp_addUrl": "<PERSON><PERSON> masukkan pautan yang anda ingin tambah, format domain mesti bermula dengan http:// atau https://", "desk_custApp_url": "<PERSON><PERSON><PERSON>", "desk_custApp_pleUrl": "<PERSON><PERSON> ma<PERSON>kkan pautan", "desk_custApp_pleName": "<PERSON><PERSON> masukkan nama", "desk_custApp_name": "<PERSON><PERSON>", "desk_custApp_open": "Cara buka", "desk_custApp_updateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desk_custApp_saveSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "desk_custApp_chooseIcon": "<PERSON>la pilih ikon", "desk_custApp_uploadIcon": "<PERSON>la muat naik ikon", "desk_custIcon_imgUpload": "Boleh muat naik imej PNG\\JPG\\GIF, saiz imej dibatasi kepada 5MB, disarankan menggunakan imej nisbah 1:1", "desk_custIcon_addImg": "Tambah imej", "desk_custIcon_onlineIcon": "<PERSON><PERSON> dalam talian", "desk_custIcon_SolidColorIcon": "<PERSON><PERSON> warna pepejal", "desk_custIcon_cusIcon": "Tersuai talian", "desk_custIcon_ImageIcon": "Tersuai", "desk_custIcon_text": "<PERSON><PERSON> masukkan teks ikon, maksimum dua aksara", "desk_custIcon_searchPngWrn": "<PERSON><PERSON><PERSON> anda memas<PERSON>kan pautan <PERSON>, ikon dalam talian akan diperoleh secara automatik, be<PERSON><PERSON> laman web mungkin gagal diperoleh", "desk_custIcon_loadingPng": "Sedang mendapatkan ikon dalam talian, sila tunggu sebentar", "desk_custIcon_bellPng": "Belum mendapatkan ikon untuk pautan semasa", "desk_custIcon_otherPng": "Anda boleh cuba memilih ikon warna pepejal atau ikon tersuai", "desk_allApp": "<PERSON><PERSON><PERSON>", "desk_allApp_noTitle": "<PERSON><PERSON><PERSON>", "desk_allApp_noText": "Cuba Se<PERSON>la <PERSON>", "desk_allApp_cusAdd": "Tambah Secara Penggunaan Sendiri", "desk_allApp_add": "<PERSON><PERSON><PERSON><PERSON>", "desk_allApp_know": "<PERSON><PERSON>", "desk_allApp_adds": "<PERSON><PERSON><PERSON><PERSON>", "desk_allApp_empty": "Tiada Rekod <PERSON>", "desk_allApp_moreApp": "Lebih Banyak Aplikasi", "desk_allApp_searchWid": "<PERSON><PERSON>", "desk_allApp_Widget": "Widget Di<PERSON><PERSON>", "desk_allApp_get": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "desk_allApp_all": "<PERSON><PERSON><PERSON>", "desk_allApp_itm": "item", "desk_guide_addApp": "Klik Kanan pada Desktop untuk Menambah Aplikasi", "desk_guide_deskMessage": "<PERSON><PERSON> ad<PERSON>, yang akan menu<PERSON> komponen/aplikasi yang ditambah oleh syarikat dan komponen/aplikasi yang Anda tambahkan secara peribadi.", "desk_guide_pleRgClick": "Sila klik kanan pada desktop untuk mula menambah aplikasi anda sendiri～", "desk_guide_clickAddApp": "Klik untuk Menambah Aplikasi", "desk_guide_clickAddAppTo": "Klik untuk Menambah Aplikasi, masuk ke halaman '<PERSON><PERSON><PERSON>'", "desk_apps_noPermission": "<PERSON><PERSON>", "desk_apps_noBuy": "Telah Tamat Masa, Sila Beli", "desk_apps_calendar": "Jadual", "desk_apps_mon": "bulan", "desk_apps_aprv": "(<PERSON><PERSON>)", "desk_apps_nowEv": "Tiada <PERSON>", "desk_apps_emptyDes": "Hebat! Tiada Tin<PERSON> yang <PERSON>n", "desk_apps_checkPersonsStr": "Hebat! Tiada Tin<PERSON> yang <PERSON>n", "desk_apps_procAprv": "<PERSON><PERSON><PERSON><PERSON>", "desk_apps_status": "<PERSON><PERSON>", "desk_apps_time": "YYYY tahun MM bulan <PERSON> hari", "desk_apps_person": "orang", "desk_apps_and": "dan", "desk_card_defaultApp": "Default Application", "stock": {"orderStatus": {"all": "All", "pending": "Pending", "approved": "Approved", "draft": "Draft", "oaApproving": "OA Approving"}, "otherOrderBusinessType": {"scrapOutbound": "Scrap Outbound", "internalWelfare": "Internal Welfare", "compensationOut": "Compensation Out", "other": "Other"}, "relateOrderType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "orderBusinessType": {"purchaseOrder": "Purchase Order", "outsourcingOrder": "Outsourcing Order"}, "inboundStatus": {"notInbound": "Not Inbound", "inbound": "Inbound", "partialInbound": "Partial Inbound"}, "status": {"draft": "Draft", "pending": "Pending", "rejected": "Rejected", "approved": "Approved", "acceptanceApproving": "Acceptance Approving"}, "statusStr": {"draft": "Draft", "pending": "Pending", "approved": "Approved", "shippingApproving": "Shipping Approving"}, "outboundStatusStr": {"notOutbound": "Not Outbound", "outbound": "Outbound", "partialOutbound": "Partial Outbound"}, "purchaseSubtypeName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "returnGoodsSourceName": {"purchaseOrder": "Purchase Order", "outsourcingPurchaseOrder": "Outsourcing Purchase Order"}, "sourceName": {"giftInbound": "Gift Inbound", "compensationInbound": "Compensation Inbound", "otherInbound": "Other Inbound"}, "businessType": {"enter": {"otherInbound": "Other Inbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "allotInbound": "Allot Inbound", "paveGoodsInbound": "Pave Goods Inbound", "diskBalanceInbound": "Disk Balance Inbound"}, "out": {"otherOutbound": "Other Outbound", "salesOutbound": "Sales Outbound", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "paveGoodsOutbound": "Pave Goods Outbound", "diskBalanceOutbound": "Disk Balance Outbound"}, "default": {"otherOutbound": "Other Outbound", "otherInbound": "Other Inbound", "salesOutbound": "Sales Outbound", "purchaseInbound": "Purchase Inbound", "produceInbound": "Produce Inbound", "salesReturn": "Sales Return", "purchaseOutbound": "Purchase Outbound", "allotOutbound": "Allot Outbound", "allotInbound": "Allot Inbound"}}, "orderFuncType": {"onlineMall": "Online Mall", "mallSupplement": "Mall Supplement", "returnSupplement": "Return Supplement"}}}